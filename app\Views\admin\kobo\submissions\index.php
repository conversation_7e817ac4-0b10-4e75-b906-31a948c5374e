<?= $this->extend('admin/layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2 fw-bold">
        <i class="bi bi-database text-primary me-2"></i>
        Submission Management
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button class="btn btn-sm btn-outline-secondary" id="refreshSubmissions">
                <i class="bi bi-arrow-clockwise me-1"></i>
                Refresh
            </button>
            <button class="btn btn-sm btn-outline-primary" onclick="exportData('csv')">
                <i class="bi bi-download me-1"></i>
                Export
            </button>
        </div>
        <button class="btn btn-sm btn-info" onclick="showStatistics()">
            <i class="bi bi-bar-chart me-1"></i>
            Analytics
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card primary h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Total Submissions</div>
                        <div class="h3 fw-bold"><?= $stats['total'] ?? 0 ?></div>
                    </div>
                    <i class="bi bi-database align-self-center" style="font-size: 2rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card success h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Processed</div>
                        <div class="h3 fw-bold"><?= $stats['processed'] ?? 0 ?></div>
                    </div>
                    <i class="bi bi-check-circle align-self-center" style="font-size: 2rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card warning h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Pending</div>
                        <div class="h3 fw-bold"><?= $stats['pending'] ?? 0 ?></div>
                    </div>
                    <i class="bi bi-clock align-self-center" style="font-size: 2rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card info h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Today's Submissions</div>
                        <div class="h3 fw-bold"><?= $stats['today'] ?? 0 ?></div>
                    </div>
                    <i class="bi bi-calendar-day align-self-center" style="font-size: 2rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3" id="filterForm">
            <div class="col-md-3">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= $search ?? '' ?>" placeholder="Search submissions...">
            </div>
            <div class="col-md-2">
                <label for="form_id" class="form-label">Form</label>
                <select class="form-select" id="form_id" name="form_id">
                    <option value="">All Forms</option>
                    <?php foreach ($forms ?? [] as $form): ?>
                        <option value="<?= $form['form_id'] ?>" <?= ($filters['form_id'] ?? '') === $form['form_id'] ? 'selected' : '' ?>>
                            <?= esc($form['form_name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="pending" <?= ($filters['status'] ?? '') === 'pending' ? 'selected' : '' ?>>Pending</option>
                    <option value="processing" <?= ($filters['status'] ?? '') === 'processing' ? 'selected' : '' ?>>Processing</option>
                    <option value="processed" <?= ($filters['status'] ?? '') === 'processed' ? 'selected' : '' ?>>Processed</option>
                    <option value="error" <?= ($filters['status'] ?? '') === 'error' ? 'selected' : '' ?>>Error</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">From Date</label>
                <input type="date" class="form-control" id="date_from" name="date_from" 
                       value="<?= $filters['date_from'] ?? '' ?>">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">To Date</label>
                <input type="date" class="form-control" id="date_to" name="date_to" 
                       value="<?= $filters['date_to'] ?? '' ?>">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Submissions Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            Submissions List
        </h5>
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-secondary" onclick="selectAll()">
                <i class="bi bi-check-all me-1"></i>
                Select All
            </button>
            <button class="btn btn-outline-success" onclick="bulkAction('process')" id="bulkProcessBtn" style="display: none;">
                <i class="bi bi-gear me-1"></i>
                Process Selected
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($submissions)): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                            </th>
                            <th>Submission Info</th>
                            <th>Form</th>
                            <th>Status</th>
                            <th>Device</th>
                            <th>Submitted</th>
                            <th width="150">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($submissions as $submission): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input row-select" value="<?= $submission['id'] ?>">
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-bold small"><?= esc($submission['submission_id']) ?></div>
                                        <small class="text-muted">ID: <?= $submission['id'] ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <small class="fw-bold"><?= esc($submission['form_name'] ?? 'Unknown') ?></small>
                                        <br><code class="small"><?= esc($submission['form_id']) ?></code>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $statusColors = [
                                        'pending' => 'warning',
                                        'processing' => 'info',
                                        'processed' => 'success',
                                        'error' => 'danger'
                                    ];
                                    $statusColor = $statusColors[$submission['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?= $statusColor ?>">
                                        <?= ucfirst($submission['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= esc($submission['device_id'] ?? 'Unknown') ?>
                                    </small>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= date('M j, Y', strtotime($submission['created_at'])) ?>
                                        <br><?= date('H:i:s', strtotime($submission['created_at'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm table-actions">
                                        <button class="btn btn-outline-primary btn-sm" 
                                                onclick="viewSubmission(<?= $submission['id'] ?>)" 
                                                title="View Details">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" 
                                                onclick="downloadSubmission(<?= $submission['id'] ?>)" 
                                                title="Download">
                                            <i class="bi bi-download"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteSubmission(<?= $submission['id'] ?>)" 
                                                title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="bi bi-database" style="font-size: 4rem; color: #dee2e6;"></i>
                <h5 class="mt-3 text-muted">No Submissions Found</h5>
                <p class="text-muted">No data submissions match your current filters.</p>
                <button class="btn btn-primary" onclick="clearFilters()">
                    <i class="bi bi-funnel me-2"></i>
                    Clear Filters
                </button>
            </div>
        <?php endif; ?>
    </div>
    
    <?php if (isset($pager)): ?>
        <div class="card-footer">
            <?= $pager->links() ?>
        </div>
    <?php endif; ?>
</div>

<!-- Modals for submission details, analytics, etc. would go here -->

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    let selectedSubmissions = [];

    document.addEventListener('DOMContentLoaded', function() {
        initializeTableSelection();
        initializeSubmissionActions();
    });

    function initializeTableSelection() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const rowCheckboxes = document.querySelectorAll('.row-select');

        selectAllCheckbox?.addEventListener('change', function() {
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedSubmissions();
        });

        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedSubmissions);
        });
    }

    function updateSelectedSubmissions() {
        const checkboxes = document.querySelectorAll('.row-select:checked');
        selectedSubmissions = Array.from(checkboxes).map(cb => parseInt(cb.value));
        
        const bulkProcessBtn = document.getElementById('bulkProcessBtn');
        if (selectedSubmissions.length > 0) {
            bulkProcessBtn.style.display = 'block';
            bulkProcessBtn.innerHTML = `<i class="bi bi-gear me-1"></i>Process Selected (${selectedSubmissions.length})`;
        } else {
            bulkProcessBtn.style.display = 'none';
        }
    }

    function selectAll() {
        document.getElementById('selectAllCheckbox').checked = true;
        document.getElementById('selectAllCheckbox').dispatchEvent(new Event('change'));
    }

    function initializeSubmissionActions() {
        document.getElementById('refreshSubmissions').addEventListener('click', function() {
            window.location.reload();
        });

        ['form_id', 'status'].forEach(id => {
            document.getElementById(id)?.addEventListener('change', function() {
                document.getElementById('filterForm').submit();
            });
        });
    }

    function viewSubmission(submissionId) {
        window.location.href = `<?= base_url('admin/kobo/submissions') ?>/${submissionId}`;
    }

    function downloadSubmission(submissionId) {
        window.open(`<?= base_url('admin/kobo/submissions') ?>/${submissionId}/download`, '_blank');
    }

    function deleteSubmission(submissionId) {
        if (confirm('Are you sure you want to delete this submission? This action cannot be undone.')) {
            ajaxRequest(`<?= base_url('admin/kobo/submissions') ?>/${submissionId}`, 'DELETE', null,
                function(data) {
                    if (data.success) {
                        showToast('Submission deleted successfully', 'success');
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showToast('Failed to delete submission: ' + (data.message || 'Unknown error'), 'danger');
                    }
                }
            );
        }
    }

    function bulkAction(action) {
        if (selectedSubmissions.length === 0) {
            showToast('Please select submissions first', 'warning');
            return;
        }

        if (confirm(`Are you sure you want to ${action} ${selectedSubmissions.length} selected submissions?`)) {
            ajaxRequest('<?= base_url('admin/kobo/submissions/bulk') ?>', 'POST', {
                operation: action,
                submission_ids: selectedSubmissions
            },
            function(data) {
                if (data.success) {
                    showToast(data.message, 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast('Bulk operation failed: ' + (data.message || 'Unknown error'), 'danger');
                }
            });
        }
    }

    function exportData(format) {
        const filters = new URLSearchParams(window.location.search);
        window.open(`<?= base_url('admin/kobo/submissions/export') ?>?format=${format}&${filters.toString()}`, '_blank');
    }

    function showStatistics() {
        // Implementation for showing analytics modal
        showToast('Statistics feature coming soon', 'info');
    }

    function clearFilters() {
        window.location.href = '<?= base_url('admin/kobo/submissions') ?>';
    }
</script>
<?= $this->endSection() ?>