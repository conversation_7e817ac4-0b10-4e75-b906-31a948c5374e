<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Kobo\KoboSubmissionModel;
use App\Models\Kobo\KoboFormModel;
use App\Models\Kobo\KoboUserModel;
use App\Models\Kobo\KoboAuditLogModel;
use App\Models\Kobo\KoboMediaFileModel;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Submission Manager Controller
 * 
 * Handles admin submission management operations including viewing,
 * filtering, exporting, and data visualization
 */
class SubmissionManagerController extends BaseController
{
    protected $submissionModel;
    protected $formModel;
    protected $userModel;
    protected $auditModel;
    protected $mediaModel;
    protected $session;

    public function __construct()
    {
        $this->submissionModel = new KoboSubmissionModel();
        $this->formModel = new KoboFormModel();
        $this->userModel = new KoboUserModel();
        $this->auditModel = new KoboAuditLogModel();
        $this->mediaModel = new KoboMediaFileModel();
        $this->session = session();
    }

    /**
     * Display submission management dashboard
     */
    public function index()
    {
        // Check admin permissions
        if (!$this->checkAdminAccess()) {
            return redirect()->to('/auth/login')->with('error', 'Admin access required');
        }

        try {
            // Get submission statistics
            $stats = $this->submissionModel->getSubmissionStats();
            
            // Get form list for filtering
            $forms = $this->formModel->getActiveForms();
            
            // Get submissions with pagination and filtering
            $filters = $this->getFiltersFromRequest();
            $perPage = 20;
            $submissionsData = $this->submissionModel->getSubmissionsWithPagination($perPage, $filters);

            // Get recent submissions for quick view
            $recentSubmissions = $this->submissionModel->getRecentSubmissions(10);

            $data = [
                'title' => 'Submission Management - Kobo Collect Admin',
                'stats' => $stats,
                'forms' => $forms,
                'submissions' => $submissionsData['submissions'],
                'pager' => $submissionsData['pager'],
                'recent_submissions' => $recentSubmissions,
                'filters' => $filters,
                'current_page' => 'submissions'
            ];

            return view('admin/kobo/submissions/index', $data);

        } catch (\Exception $e) {
            log_message('error', 'Submission management index error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load submission management dashboard');
        }
    }

    /**
     * Display submission details
     */
    public function show($id)
    {
        if (!$this->checkAdminAccess()) {
            return redirect()->to('/auth/login')->with('error', 'Admin access required');
        }

        try {
            $submission = $this->submissionModel->find($id);
            if (!$submission) {
                throw new \Exception('Submission not found');
            }

            // Get related form
            $form = $this->formModel->findByFormId($submission['form_id']);
            
            // Get submission media files
            $mediaFiles = $this->mediaModel->getSubmissionMedia($submission['submission_id']);
            
            // Parse submission data
            $submissionData = json_decode($submission['json_data'], true);
            
            // Get submission history/audit logs
            $auditLogs = $this->auditModel->getActivitiesByEntity('submission', $id);

            $data = [
                'title' => 'Submission Details - ' . $submission['submission_id'],
                'submission' => $submission,
                'form' => $form,
                'submission_data' => $submissionData,
                'media_files' => $mediaFiles,
                'audit_logs' => $auditLogs,
                'current_page' => 'submissions'
            ];

            return view('admin/kobo/submissions/show', $data);

        } catch (\Exception $e) {
            log_message('error', 'Submission view error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load submission details');
        }
    }

    /**
     * Update submission status
     */
    public function updateStatus($id)
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            $newStatus = $this->request->getPost('status');
            
            if (!in_array($newStatus, ['pending', 'processing', 'processed', 'error'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid status specified'
                ]);
            }

            $submission = $this->submissionModel->find($id);
            if (!$submission) {
                return $this->response->setJSON(['success' => false, 'message' => 'Submission not found']);
            }

            $oldStatus = $submission['status'];
            $success = $this->submissionModel->updateStatus($id, $newStatus);

            if ($success) {
                // Log status change
                $this->auditModel->logActivity(
                    $this->getCurrentUserId(),
                    'submission',
                    'status_updated',
                    $id,
                    [
                        'old_data' => ['status' => $oldStatus],
                        'new_data' => ['status' => $newStatus]
                    ]
                );

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Submission status updated successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update submission status'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Submission status update error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error occurred'
            ]);
        }
    }

    /**
     * Delete submission
     */
    public function delete($id)
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            $submission = $this->submissionModel->find($id);
            if (!$submission) {
                return $this->response->setJSON(['success' => false, 'message' => 'Submission not found']);
            }

            // Delete associated media files first
            $mediaFiles = $this->mediaModel->getSubmissionMedia($submission['submission_id']);
            foreach ($mediaFiles as $mediaFile) {
                $this->mediaModel->delete($mediaFile['id']);
            }

            $success = $this->submissionModel->delete($id);

            if ($success) {
                // Log submission deletion
                $this->auditModel->logActivity(
                    $this->getCurrentUserId(),
                    'submission',
                    'deleted',
                    $id,
                    ['old_data' => $submission]
                );

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Submission deleted successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to delete submission'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Submission deletion error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error occurred'
            ]);
        }
    }

    /**
     * Bulk operations on submissions
     */
    public function bulkOperation()
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            $operation = $this->request->getPost('operation');
            $submissionIds = $this->request->getPost('submission_ids');

            if (empty($operation) || empty($submissionIds) || !is_array($submissionIds)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid operation or submission selection'
                ]);
            }

            $results = ['success' => 0, 'failed' => 0, 'messages' => []];
            $currentUserId = $this->getCurrentUserId();

            foreach ($submissionIds as $submissionId) {
                switch ($operation) {
                    case 'process':
                        if ($this->submissionModel->updateStatus($submissionId, 'processing')) {
                            $results['success']++;
                            $this->auditModel->logActivity($currentUserId, 'submission', 'bulk_processed', $submissionId);
                        } else {
                            $results['failed']++;
                        }
                        break;

                    case 'approve':
                        if ($this->submissionModel->updateStatus($submissionId, 'processed')) {
                            $results['success']++;
                            $this->auditModel->logActivity($currentUserId, 'submission', 'bulk_approved', $submissionId);
                        } else {
                            $results['failed']++;
                        }
                        break;

                    case 'reject':
                        if ($this->submissionModel->updateStatus($submissionId, 'error')) {
                            $results['success']++;
                            $this->auditModel->logActivity($currentUserId, 'submission', 'bulk_rejected', $submissionId);
                        } else {
                            $results['failed']++;
                        }
                        break;

                    case 'delete':
                        $submission = $this->submissionModel->find($submissionId);
                        if ($submission && $this->submissionModel->delete($submissionId)) {
                            $results['success']++;
                            $this->auditModel->logActivity($currentUserId, 'submission', 'bulk_deleted', $submissionId);
                        } else {
                            $results['failed']++;
                        }
                        break;

                    default:
                        $results['failed']++;
                        $results['messages'][] = "Unknown operation: {$operation}";
                        break;
                }
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => "Bulk operation completed: {$results['success']} successful, {$results['failed']} failed",
                'results' => $results
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Bulk submission operation error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error during bulk operation'
            ]);
        }
    }

    /**
     * Export submissions
     */
    public function export()
    {
        if (!$this->checkAdminAccess()) {
            return redirect()->back()->with('error', 'Admin access required');
        }

        try {
            $format = $this->request->getGet('format') ?? 'csv';
            $formId = $this->request->getGet('form_id');
            $dateFrom = $this->request->getGet('date_from');
            $dateTo = $this->request->getGet('date_to');

            // Build filters
            $filters = [];
            if ($formId) $filters['form_id'] = $formId;
            if ($dateFrom) $filters['date_from'] = $dateFrom;
            if ($dateTo) $filters['date_to'] = $dateTo;

            if ($format === 'csv') {
                return $this->exportCSV($filters);
            } elseif ($format === 'json') {
                return $this->exportJSON($filters);
            } else {
                return $this->response->setJSON(['error' => 'Unsupported export format']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Submission export error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to export submissions');
        }
    }

    /**
     * Export submissions to CSV
     */
    private function exportCSV(array $filters = []): ResponseInterface
    {
        $submissions = $this->submissionModel->getSubmissionsForExport($filters);
        $filename = 'kobo_submissions_' . date('Y-m-d_H-i-s') . '.csv';
        
        // Set headers for CSV download
        $this->response->setHeader('Content-Type', 'text/csv');
        $this->response->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"');
        
        // Create CSV content
        $csv = "ID,Submission ID,Form ID,Status,Device ID,IP Address,Submitted At,Data\n";
        
        foreach ($submissions as $submission) {
            $jsonData = json_decode($submission['json_data'], true);
            $dataString = is_array($jsonData) ? json_encode($jsonData) : $submission['json_data'];
            
            $csv .= sprintf(
                "%d,%s,%s,%s,%s,%s,%s,%s\n",
                $submission['id'],
                $submission['submission_id'],
                $submission['form_id'],
                $submission['status'],
                $submission['device_id'] ?? '',
                $submission['ip_address'] ?? '',
                $submission['created_at'],
                str_replace(["\n", "\r", ','], [' ', ' ', ' '], $dataString)
            );
        }

        // Log export activity
        $this->auditModel->logActivity(
            $this->getCurrentUserId(),
            'submission',
            'exported',
            null,
            ['new_data' => ['format' => 'csv', 'count' => count($submissions), 'filters' => $filters]]
        );

        return $this->response->setBody($csv);
    }

    /**
     * Export submissions to JSON
     */
    private function exportJSON(array $filters = []): ResponseInterface
    {
        $submissions = $this->submissionModel->getSubmissionsForExport($filters);
        
        // Process submissions for JSON export
        $exportData = [];
        foreach ($submissions as $submission) {
            $jsonData = json_decode($submission['json_data'], true);
            
            $exportData[] = [
                'id' => $submission['id'],
                'submission_id' => $submission['submission_id'],
                'form_id' => $submission['form_id'],
                'status' => $submission['status'],
                'device_id' => $submission['device_id'],
                'ip_address' => $submission['ip_address'],
                'submitted_at' => $submission['created_at'],
                'data' => $jsonData
            ];
        }

        $filename = 'kobo_submissions_' . date('Y-m-d_H-i-s') . '.json';
        
        // Set headers for JSON download
        $this->response->setHeader('Content-Type', 'application/json');
        $this->response->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"');

        // Log export activity
        $this->auditModel->logActivity(
            $this->getCurrentUserId(),
            'submission',
            'exported',
            null,
            ['new_data' => ['format' => 'json', 'count' => count($submissions), 'filters' => $filters]]
        );

        return $this->response->setJSON([
            'export_info' => [
                'exported_at' => date('Y-m-d H:i:s'),
                'total_records' => count($exportData),
                'filters_applied' => $filters
            ],
            'submissions' => $exportData
        ]);
    }

    /**
     * Export single submission
     */
    public function exportSingle($id)
    {
        if (!$this->checkAdminAccess()) {
            return redirect()->back()->with('error', 'Admin access required');
        }

        try {
            $submission = $this->submissionModel->find($id);
            if (!$submission) {
                return $this->response->setStatusCode(404)->setJSON(['error' => 'Submission not found']);
            }

            $format = $this->request->getGet('format') ?? 'json';
            
            if ($format === 'json') {
                $jsonData = json_decode($submission['json_data'], true);
                
                $exportData = [
                    'submission_info' => [
                        'id' => $submission['id'],
                        'submission_id' => $submission['submission_id'],
                        'form_id' => $submission['form_id'],
                        'status' => $submission['status'],
                        'device_id' => $submission['device_id'],
                        'submitted_at' => $submission['created_at']
                    ],
                    'data' => $jsonData,
                    'xml_data' => $submission['xml_data']
                ];

                $filename = 'submission_' . $submission['submission_id'] . '.json';
                $this->response->setHeader('Content-Type', 'application/json');
                $this->response->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"');
                
                return $this->response->setJSON($exportData);
            } elseif ($format === 'xml') {
                $filename = 'submission_' . $submission['submission_id'] . '.xml';
                $this->response->setHeader('Content-Type', 'application/xml');
                $this->response->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"');
                
                return $this->response->setBody($submission['xml_data']);
            } else {
                return $this->response->setStatusCode(400)->setJSON(['error' => 'Unsupported format']);
            }

        } catch (\Exception $e) {
            log_message('error', 'Single submission export error: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON(['error' => 'Export failed']);
        }
    }

    /**
     * Get submission statistics for charts
     */
    public function getStatistics()
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            $period = $this->request->getGet('period') ?? '30_days';
            
            // Get submission statistics by form
            $submissionsByForm = $this->submissionModel->getSubmissionsByForm();
            
            // Get submissions over time
            $submissionsOverTime = $this->submissionModel->getSubmissionsOverTime($period);
            
            // Get status distribution
            $statusDistribution = $this->submissionModel->getStatusDistribution();
            
            // Get device statistics
            $deviceStats = $this->submissionModel->getDeviceStatistics();

            return $this->response->setJSON([
                'success' => true,
                'statistics' => [
                    'by_form' => $submissionsByForm,
                    'over_time' => $submissionsOverTime,
                    'status_distribution' => $statusDistribution,
                    'device_stats' => $deviceStats
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Statistics error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to load statistics'
            ]);
        }
    }

    /**
     * Search submissions with AJAX
     */
    public function search()
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            $query = $this->request->getGet('q') ?? '';
            $formId = $this->request->getGet('form_id');
            $status = $this->request->getGet('status');
            $page = (int)($this->request->getGet('page') ?? 1);
            $perPage = 10;

            $filters = ['search' => $query];
            if ($formId) $filters['form_id'] = $formId;
            if ($status) $filters['status'] = $status;

            $submissionsData = $this->submissionModel->getSubmissionsWithPagination($perPage, $filters);
            
            return $this->response->setJSON([
                'success' => true,
                'submissions' => $submissionsData['submissions'],
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'has_more' => count($submissionsData['submissions']) === $perPage
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Submission search error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Search failed'
            ]);
        }
    }

    /**
     * Get submission data for visualization
     */
    public function getDataVisualization($id)
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            $submission = $this->submissionModel->find($id);
            if (!$submission) {
                return $this->response->setJSON(['success' => false, 'message' => 'Submission not found']);
            }

            $jsonData = json_decode($submission['json_data'], true);
            
            // Process data for visualization
            $visualData = $this->processDataForVisualization($jsonData);

            return $this->response->setJSON([
                'success' => true,
                'submission_id' => $submission['submission_id'],
                'form_id' => $submission['form_id'],
                'visualization_data' => $visualData
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Data visualization error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to process data for visualization'
            ]);
        }
    }

    /**
     * Process submission data for visualization
     */
    private function processDataForVisualization(array $data): array
    {
        $processed = [
            'fields' => [],
            'geopoints' => [],
            'media' => [],
            'metadata' => []
        ];

        foreach ($data as $field => $value) {
            if (is_array($value)) {
                $processed['metadata'][$field] = $value;
            } elseif (preg_match('/^-?\d+\.?\d*\s+-?\d+\.?\d*(\s+-?\d+\.?\d*)?(\s+\d+\.?\d*)?$/', $value)) {
                // Geopoint data
                $processed['geopoints'][$field] = $this->parseGeopoint($value);
            } elseif (preg_match('/\.(jpg|jpeg|png|gif|mp4|mp3|wav)$/i', $value)) {
                // Media file
                $processed['media'][$field] = $value;
            } else {
                // Regular field
                $processed['fields'][$field] = $value;
            }
        }

        return $processed;
    }

    /**
     * Parse geopoint string
     */
    private function parseGeopoint(string $geopoint): array
    {
        $parts = explode(' ', trim($geopoint));
        
        return [
            'latitude' => (float)($parts[0] ?? 0),
            'longitude' => (float)($parts[1] ?? 0),
            'altitude' => isset($parts[2]) ? (float)$parts[2] : null,
            'accuracy' => isset($parts[3]) ? (float)$parts[3] : null
        ];
    }

    /**
     * Get filters from request
     */
    private function getFiltersFromRequest(): array
    {
        return [
            'form_id' => $this->request->getGet('form_id'),
            'status' => $this->request->getGet('status'),
            'device_id' => $this->request->getGet('device_id'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to'),
            'search' => $this->request->getGet('search')
        ];
    }

    // Private helper methods

    /**
     * Check if current user has admin access
     */
    private function checkAdminAccess(): bool
    {
        // Implement your authentication logic here
        // For now, return true for development
        return true;
        
        // Example implementation:
        // $userId = $this->getCurrentUserId();
        // return $userId && $this->userModel->isAdmin($userId);
    }

    /**
     * Get current user ID from session/JWT
     */
    private function getCurrentUserId(): ?int
    {
        // Implement your session/JWT logic here
        // For now, return 1 for development
        return 1;
        
        // Example implementation:
        // return $this->session->get('user_id');
    }
}