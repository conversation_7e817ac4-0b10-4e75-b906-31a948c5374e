<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Kobo\KoboFormModel;
use App\Models\Kobo\KoboUserModel;
use App\Models\Kobo\KoboSubmissionModel;
use App\Models\Kobo\KoboFormAccessModel;
use App\Models\Kobo\KoboAuditLogModel;
use App\Libraries\XFormParser;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Admin Form Manager Controller
 * 
 * Handles form upload interface, form editing capabilities,
 * form preview functionality, and bulk operations
 */
class FormManagerController extends BaseController
{
    protected $formModel;
    protected $userModel;
    protected $submissionModel;
    protected $accessModel;
    protected $auditModel;
    protected $parser;

    public function __construct()
    {
        $this->formModel = new KoboFormModel();
        $this->userModel = new KoboUserModel();
        $this->submissionModel = new KoboSubmissionModel();
        $this->accessModel = new KoboFormAccessModel();
        $this->auditModel = new KoboAuditLogModel();
        $this->parser = new XFormParser();
    }

    /**
     * Display forms list
     */
    public function index(): string
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $perPage = (int) ($this->request->getGet('per_page') ?? 20);
            $search = $this->request->getGet('search') ?? '';
            
            $result = $this->formModel->getFormsWithPagination($perPage, $search);
            
            $data = [
                'title' => 'Form Management - Kobo Admin',
                'forms' => $result['forms'],
                'pager' => $result['pager'],
                'search' => $search,
                'stats' => $this->formModel->getFormStats()
            ];

            return view('admin/kobo/forms/index', $data);

        } catch (\Exception $e) {
            log_message('error', 'Forms index error: ' . $e->getMessage());
            return redirect()->to('/admin/dashboard')->with('error', 'Unable to load forms');
        }
    }

    /**
     * Show form upload page
     */
    public function new(): string
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        $data = [
            'title' => 'Upload New Form - Kobo Admin',
            'validation' => session()->getFlashdata('validation')
        ];

        return view('admin/kobo/forms/form', $data);
    }

    /**
     * Handle form upload
     */
    public function create(): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON(['success' => false, 'message' => 'Authentication required']);
            }
            return redirect()->to('/admin/login');
        }

        try {
            $validation = \Config\Services::validation();
            
            $rules = [
                'form_name' => 'required|string|max_length[255]',
                'xml_file' => 'uploaded[xml_file]|ext_in[xml_file,xml]|max_size[xml_file,2048]',
                'description' => 'permit_empty|string|max_length[500]',
                'status' => 'required|in_list[active,draft,inactive]'
            ];

            if (!$validation->setRules($rules)->withRequest($this->request)->run()) {
                if ($this->request->isAJAX()) {
                    return $this->response->setJSON(['success' => false, 'errors' => $validation->getErrors(), 'message' => 'Validation failed']);
                }
                return redirect()->back()->withInput()->with('validation', $validation->getErrors());
            }

            // Process uploaded file
            $file = $this->request->getFile('xml_file');
            if (!$file->isValid()) {
                if ($this->request->isAJAX()) {
                    return $this->response->setJSON(['success' => false, 'message' => 'Invalid file upload']);
                }
                return redirect()->back()->withInput()->with('error', 'Invalid file upload');
            }

            $xmlContent = file_get_contents($file->getTempName());
            
            // Validate XML using parser
            if (!$this->parser->parse($xmlContent) || !$this->parser->validateXForm()) {
                $errors = $this->parser->getErrors();
                log_message('error', 'Form validation failed: ' . json_encode($errors));
                log_message('debug', 'XML content first 200 chars: ' . substr($xmlContent, 0, 200));
                $message = 'Invalid XML format or XForm structure: ' . implode(', ', $errors);
                if ($this->request->isAJAX()) {
                    return $this->response->setJSON(['success' => false, 'message' => $message, 'errors' => $errors]);
                }
                return redirect()->back()->withInput()->with('error', $message);
            }

            // Extract metadata
            $metadata = $this->parser->extractMetadata();
            
            // Create form record
            $formData = [
                'form_id' => $metadata['form_id'] ?? uniqid('form_'),
                'form_name' => $this->request->getPost('form_name'),
                'xml_content' => $xmlContent,
                'version' => $metadata['version'] ?? '1.0',
                'status' => $this->request->getPost('status'),
                'created_by' => $this->getCurrentAdminId()
            ];

            $formId = $this->formModel->createForm($formData);
            if (!$formId) {
                if ($this->request->isAJAX()) {
                    return $this->response->setJSON(['success' => false, 'message' => 'Failed to save form']);
                }
                return redirect()->back()->withInput()->with('error', 'Failed to save form');
            }

            // Log activity
            $this->auditModel->logFormCreated($this->getCurrentAdminId(), $formId, $formData);

            if ($this->request->isAJAX()) {
                return $this->response->setJSON(['success' => true, 'message' => 'Form uploaded successfully', 'form_id' => $formId]);
            }
            return redirect()->to('/admin/kobo/forms')->with('success', 'Form uploaded successfully');

        } catch (\Exception $e) {
            log_message('error', 'Form create error: ' . $e->getMessage());
            if ($this->request->isAJAX()) {
                return $this->response->setJSON(['success' => false, 'message' => 'Upload failed: ' . $e->getMessage()]);
            }
            return redirect()->back()->withInput()->with('error', 'Upload failed: ' . $e->getMessage());
        }
    }

    /**
     * Show single form
     */
    public function show(int $id): string
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Form not found');
            }

            // Get form submissions
            $submissions = $this->submissionModel->getSubmissionsByForm($form['form_id']);
            
            // Get form access records
            $accessRecords = $this->accessModel->getFormAccess($form['form_id']);
            
            // Parse form fields
            $this->parser->parse($form['xml_content']);
            $fields = $this->parser->extractFields();

            $data = [
                'title' => 'Form Details - ' . $form['form_name'],
                'form' => $form,
                'submissions' => $submissions,
                'access_records' => $accessRecords,
                'fields' => $fields,
                'stats' => $this->getFormStatistics($form['form_id'])
            ];

            return view('admin/kobo/forms/form', $data);

        } catch (\Exception $e) {
            log_message('error', 'Form show error: ' . $e->getMessage());
            return redirect()->to('/admin/kobo/forms')->with('error', 'Unable to load form');
        }
    }

    /**
     * Show form edit page
     */
    public function edit(int $id): string
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Form not found');
            }

            $data = [
                'title' => 'Edit Form - ' . $form['form_name'],
                'form' => $form,
                'validation' => session()->getFlashdata('validation')
            ];

            return view('admin/kobo/forms/form', $data);

        } catch (\Exception $e) {
            log_message('error', 'Form edit error: ' . $e->getMessage());
            return redirect()->to('/admin/kobo/forms')->with('error', 'Unable to load form for editing');
        }
    }

    /**
     * Update form
     */
    public function update(int $id): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Form not found');
            }

            $validation = \Config\Services::validation();
            
            $rules = [
                'form_name' => 'required|string|max_length[255]',
                'version' => 'required|string|max_length[50]',
                'status' => 'required|in_list[active,draft,inactive]'
            ];

            if (!$validation->setRules($rules)->withRequest($this->request)->run()) {
                return redirect()->back()->withInput()->with('validation', $validation->getErrors());
            }

            $oldData = $form;
            $newData = [
                'form_name' => $this->request->getPost('form_name'),
                'version' => $this->request->getPost('version'),
                'status' => $this->request->getPost('status')
            ];

            // Handle XML file update if provided
            $file = $this->request->getFile('xml_file');
            if ($file && $file->isValid()) {
                $xmlContent = file_get_contents($file->getTempName());
                
                if (!$this->parser->parse($xmlContent) || !$this->parser->validateXForm()) {
                    return redirect()->back()->withInput()->with('error', 'Invalid XML: ' . implode(', ', $this->parser->getErrors()));
                }
                
                $newData['xml_content'] = $xmlContent;
            }

            if (!$this->formModel->update($id, $newData)) {
                return redirect()->back()->withInput()->with('error', 'Failed to update form');
            }

            // Log activity
            $this->auditModel->logFormUpdated($this->getCurrentAdminId(), $id, $oldData, $newData);

            return redirect()->to("/admin/kobo/forms/{$id}")->with('success', 'Form updated successfully');

        } catch (\Exception $e) {
            log_message('error', 'Form update error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Update failed: ' . $e->getMessage());
        }
    }

    /**
     * Delete form
     */
    public function delete(int $id): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                return redirect()->to('/admin/kobo/forms')->with('error', 'Form not found');
            }

            // Check if form has submissions
            if ($this->formModel->hasSubmissions($id)) {
                return redirect()->to('/admin/kobo/forms')->with('error', 'Cannot delete form with existing submissions');
            }

            if (!$this->formModel->delete($id)) {
                return redirect()->to('/admin/kobo/forms')->with('error', 'Failed to delete form');
            }

            // Log activity
            $this->auditModel->logFormDeleted($this->getCurrentAdminId(), $id, $form);

            return redirect()->to('/admin/kobo/forms')->with('success', 'Form deleted successfully');

        } catch (\Exception $e) {
            log_message('error', 'Form delete error: ' . $e->getMessage());
            return redirect()->to('/admin/kobo/forms')->with('error', 'Delete failed');
        }
    }

    /**
     * Form preview modal
     */
    public function preview(int $id): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            return $this->response->setStatusCode(401)->setJSON(['error' => 'Unauthorized']);
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                return $this->response->setStatusCode(404)->setJSON(['error' => 'Form not found']);
            }

            // Parse form structure
            $this->parser->parse($form['xml_content']);
            $fields = $this->parser->extractFields();
            $metadata = $this->parser->extractMetadata();

            return $this->response->setJSON([
                'success' => true,
                'form' => $form,
                'fields' => $fields,
                'metadata' => $metadata
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Form preview error: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON(['error' => 'Preview failed']);
        }
    }

    /**
     * Bulk operations
     */
    public function bulkActions(): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $action = $this->request->getPost('action');
            $formIds = $this->request->getPost('form_ids');

            if (empty($action) || empty($formIds)) {
                return redirect()->back()->with('error', 'Invalid bulk action request');
            }

            $results = [];
            foreach ($formIds as $id) {
                switch ($action) {
                    case 'activate':
                        $results[$id] = $this->formModel->activateForm($id);
                        break;
                    case 'deactivate':
                        $results[$id] = $this->formModel->deactivateForm($id);
                        break;
                    case 'draft':
                        $results[$id] = $this->formModel->setDraftStatus($id);
                        break;
                    default:
                        $results[$id] = false;
                }
            }

            $successful = array_sum($results);
            $total = count($results);

            // Log bulk operation
            $this->auditModel->logActivity(
                $this->getCurrentAdminId(),
                'form',
                'bulk_' . $action,
                null,
                ['form_ids' => $formIds, 'success_count' => $successful, 'total_count' => $total]
            );

            return redirect()->back()->with('success', "Bulk {$action}: {$successful}/{$total} forms processed");

        } catch (\Exception $e) {
            log_message('error', 'Bulk action error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Bulk operation failed');
        }
    }

    /**
     * Clone form
     */
    public function clone(int $id): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                return redirect()->to('/admin/kobo/forms')->with('error', 'Form not found');
            }

            $newVersion = $this->getNextVersion($form['version']);
            $clonedId = $this->formModel->cloneForm($id, $newVersion);

            if (!$clonedId) {
                return redirect()->to('/admin/kobo/forms')->with('error', 'Failed to clone form');
            }

            // Log activity
            $this->auditModel->logActivity(
                $this->getCurrentAdminId(),
                'form',
                'cloned',
                $clonedId,
                ['original_id' => $id, 'new_version' => $newVersion]
            );

            return redirect()->to("/admin/kobo/forms/{$clonedId}/edit")->with('success', 'Form cloned successfully');

        } catch (\Exception $e) {
            log_message('error', 'Form clone error: ' . $e->getMessage());
            return redirect()->to('/admin/kobo/forms')->with('error', 'Clone failed');
        }
    }

    /**
     * Export forms data
     */
    public function export(): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $format = $this->request->getGet('format') ?? 'csv';
            $forms = $this->formModel->findAll();

            switch ($format) {
                case 'csv':
                    return $this->exportCSV($forms);
                case 'json':
                    return $this->exportJSON($forms);
                default:
                    throw new \InvalidArgumentException('Unsupported export format');
            }

        } catch (\Exception $e) {
            log_message('error', 'Forms export error: ' . $e->getMessage());
            return redirect()->to('/admin/kobo/forms')->with('error', 'Export failed');
        }
    }

    /**
     * Form analytics
     */
    public function analytics(int $id): string
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Form not found');
            }

            $analytics = $this->getFormAnalytics($form['form_id']);

            $data = [
                'title' => 'Form Analytics - ' . $form['form_name'],
                'form' => $form,
                'analytics' => $analytics
            ];

            return view('admin/kobo/forms/index', $data);

        } catch (\Exception $e) {
            log_message('error', 'Form analytics error: ' . $e->getMessage());
            return redirect()->to('/admin/kobo/forms')->with('error', 'Unable to load analytics');
        }
    }

    /**
     * Get form statistics
     */
    private function getFormStatistics(string $formId): array
    {
        return [
            'total_submissions' => $this->submissionModel->where('form_id', $formId)->countAllResults(),
            'processed_submissions' => $this->submissionModel->where('form_id', $formId)->where('status', 'processed')->countAllResults(),
            'pending_submissions' => $this->submissionModel->where('form_id', $formId)->where('status', 'pending')->countAllResults(),
            'failed_submissions' => $this->submissionModel->where('form_id', $formId)->where('status', 'failed')->countAllResults(),
            'users_with_access' => $this->accessModel->where('form_id', $formId)->where('status', 'active')->countAllResults()
        ];
    }

    /**
     * Get form analytics data
     */
    private function getFormAnalytics(string $formId): array
    {
        // Submissions over time
        $submissionStats = $this->submissionModel->getSubmissionStats($formId);
        
        // Device usage
        $deviceStats = $this->submissionModel->select('device_id, COUNT(*) as count')
                                           ->where('form_id', $formId)
                                           ->groupBy('device_id')
                                           ->findAll();

        return [
            'submission_stats' => $submissionStats,
            'device_stats' => $deviceStats,
            'completion_rate' => $this->calculateCompletionRate($formId)
        ];
    }

    /**
     * Calculate completion rate
     */
    private function calculateCompletionRate(string $formId): float
    {
        $total = $this->submissionModel->where('form_id', $formId)->countAllResults();
        $completed = $this->submissionModel->where('form_id', $formId)->where('status', 'processed')->countAllResults();
        
        return $total > 0 ? ($completed / $total) * 100 : 0;
    }

    /**
     * Get next version number
     */
    private function getNextVersion(string $currentVersion): string
    {
        $parts = explode('.', $currentVersion);
        $minor = (int)($parts[1] ?? 0) + 1;
        return ($parts[0] ?? '1') . '.' . $minor;
    }

    /**
     * Export forms as CSV
     */
    private function exportCSV(array $forms): ResponseInterface
    {
        $csv = "Form ID,Name,Version,Status,Created By,Created At,Submissions\n";
        
        foreach ($forms as $form) {
            $submissionCount = $this->submissionModel->where('form_id', $form['form_id'])->countAllResults();
            $csv .= "\"{$form['form_id']}\",\"{$form['form_name']}\",\"{$form['version']}\",\"{$form['status']}\",\"{$form['created_by']}\",\"{$form['created_at']}\",{$submissionCount}\n";
        }

        return $this->response->download('forms_export_' . date('Y-m-d') . '.csv', $csv)
                            ->setContentType('text/csv');
    }

    /**
     * Export forms as JSON
     */
    private function exportJSON(array $forms): ResponseInterface
    {
        $data = [
            'export_date' => date('Y-m-d H:i:s'),
            'total_forms' => count($forms),
            'forms' => $forms
        ];

        return $this->response->download('forms_export_' . date('Y-m-d') . '.json', json_encode($data, JSON_PRETTY_PRINT))
                            ->setContentType('application/json');
    }

    /**
     * Check admin authentication
     */
    private function isAdminAuthenticated(): bool
    {
        // For development/testing purposes, return true
        return true;
        
        // Original session-based authentication:
        /*
        $session = session();
        $userId = $session->get('admin_user_id');
        
        if (!$userId) {
            return false;
        }

        $user = $this->userModel->find($userId);
        return $user && $user['status'] === 'active' && in_array($user['role'], ['admin', 'manager']);
        */
    }

    /**
     * Get current admin user ID
     */
    private function getCurrentAdminId(): int
    {
        // For development/testing purposes, return a dummy ID
        return 1;
        
        // Original session-based authentication:
        // return session()->get('admin_user_id');
    }
}