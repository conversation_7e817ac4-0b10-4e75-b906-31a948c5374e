<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Kobo\KoboFormModel;
use App\Models\Kobo\KoboUserModel;
use App\Models\Kobo\KoboSubmissionModel;
use App\Models\Kobo\KoboFormAccessModel;
use App\Models\Kobo\KoboAuditLogModel;

use CodeIgniter\HTTP\ResponseInterface;

/**
 * Admin Form Manager Controller
 * 
 * Handles form upload interface, form editing capabilities,
 * form preview functionality, and bulk operations
 */
class FormManagerController extends BaseController
{
    protected $formModel;
    protected $userModel;
    protected $submissionModel;
    protected $accessModel;
    protected $auditModel;

    public function __construct()
    {
        $this->formModel = new KoboFormModel();
        $this->userModel = new KoboUserModel();
        $this->submissionModel = new KoboSubmissionModel();
        $this->accessModel = new KoboFormAccessModel();
        $this->auditModel = new KoboAuditLogModel();
    }

    /**
     * Display forms list
     */
    public function index(): string
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $perPage = (int) ($this->request->getGet('per_page') ?? 20);
            $search = $this->request->getGet('search') ?? '';
            
            $result = $this->formModel->getFormsWithPagination($perPage, $search);
            
            $data = [
                'title' => 'Form Management - Kobo Admin',
                'forms' => $result['forms'],
                'pager' => $result['pager'],
                'search' => $search,
                'stats' => $this->formModel->getFormStats()
            ];

            return view('admin/kobo/forms/index', $data);

        } catch (\Exception $e) {
            log_message('error', 'Forms index error: ' . $e->getMessage());
            return redirect()->to('/admin/dashboard')->with('error', 'Unable to load forms');
        }
    }

    /**
     * Show form upload page
     */
    public function new(): string
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        $data = [
            'title' => 'Upload New Form - Kobo Admin',
            'validation' => session()->getFlashdata('validation')
        ];

        return view('admin/kobo/forms/form_simple', $data);
    }

    /**
     * Handle form upload - Simplified version
     */
    public function create(): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON(['success' => false, 'message' => 'Authentication required']);
            }
            return redirect()->to('/admin/login');
        }

        try {
            // Basic validation
            $formName = $this->request->getPost('form_name');
            $status = $this->request->getPost('status') ?? 'draft';

            if (empty($formName)) {
                $message = 'Form name is required';
                if ($this->request->isAJAX()) {
                    return $this->response->setJSON(['success' => false, 'message' => $message]);
                }
                return redirect()->back()->withInput()->with('error', $message);
            }

            // Process uploaded file
            $file = $this->request->getFile('xml_file');
            if (!$file || !$file->isValid()) {
                $message = 'Please select a valid XML file';
                if ($this->request->isAJAX()) {
                    return $this->response->setJSON(['success' => false, 'message' => $message]);
                }
                return redirect()->back()->withInput()->with('error', $message);
            }

            // Read XML content
            $xmlContent = file_get_contents($file->getTempName());
            if (empty($xmlContent)) {
                $message = 'XML file is empty or could not be read';
                if ($this->request->isAJAX()) {
                    return $this->response->setJSON(['success' => false, 'message' => $message]);
                }
                return redirect()->back()->withInput()->with('error', $message);
            }

            // Simple XML validation - just check if it's valid XML
            $xml = simplexml_load_string($xmlContent);
            if ($xml === false) {
                $message = 'Invalid XML format';
                if ($this->request->isAJAX()) {
                    return $this->response->setJSON(['success' => false, 'message' => $message]);
                }
                return redirect()->back()->withInput()->with('error', $message);
            }

            // Extract basic metadata
            $formId = 'form' . time(); // Use timestamp instead of uniqid to avoid underscores
            $version = '1.0';

            // Try to extract form ID from XML if available
            if (isset($xml->head->model->instance)) {
                $instance = $xml->head->model->instance->children();
                if ($instance->count() > 0) {
                    $rootElement = $instance[0];
                    $extractedId = $rootElement->getName();
                    // Clean the extracted ID to match validation rules
                    $formId = preg_replace('/[^a-zA-Z0-9\s]/', '', $extractedId);
                    if (empty($formId)) {
                        $formId = 'form' . time();
                    }
                }
            }

            // Create form record
            $formData = [
                'form_id' => $formId,
                'form_name' => $formName,
                'xml_content' => $xmlContent,
                'version' => $version,
                'status' => $status,
                'created_by' => null  // Temporarily set to null to avoid foreign key issues
            ];

            log_message('debug', 'Form data to insert: ' . json_encode($formData));

            // Temporarily disable validation to debug
            $this->formModel->skipValidation(true);
            $newFormId = $this->formModel->insert($formData);

            if (!$newFormId) {
                $errors = $this->formModel->errors();
                log_message('error', 'Form model validation errors: ' . json_encode($errors));
                log_message('error', 'Database error: ' . $this->formModel->db->error());
                $message = 'Failed to save form to database: ' . implode(', ', $errors);
                if ($this->request->isAJAX()) {
                    return $this->response->setJSON(['success' => false, 'message' => $message]);
                }
                return redirect()->back()->withInput()->with('error', $message);
            }

            // Log activity - Temporarily disabled
            // $this->auditModel->logFormCreated($this->getCurrentAdminId(), $newFormId, $formData);

            if ($this->request->isAJAX()) {
                return $this->response->setJSON(['success' => true, 'message' => 'Form uploaded successfully', 'form_id' => $newFormId]);
            }
            return redirect()->to('/admin/kobo/forms')->with('success', 'Form uploaded successfully');

        } catch (\Exception $e) {
            log_message('error', 'Form create error: ' . $e->getMessage());
            $message = 'Upload failed: ' . $e->getMessage();
            if ($this->request->isAJAX()) {
                return $this->response->setJSON(['success' => false, 'message' => $message]);
            }
            return redirect()->back()->withInput()->with('error', $message);
        }
    }

    /**
     * Show single form
     */
    public function show(int $id): string
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Form not found');
            }

            // Get form submissions
            $submissions = $this->submissionModel->getSubmissionsByForm($form['form_id']);
            
            // Get form access records
            $accessRecords = $this->accessModel->getFormAccess($form['form_id']);
            
            // Parse form fields (with error handling)
            $fields = [];
            try {
                if ($this->parser->parse($form['xml_content'])) {
                    $fields = $this->parser->extractFields();
                } else {
                    log_message('debug', 'XFormParser failed to parse XML: ' . implode(', ', $this->parser->getErrors()));
                    // Continue without fields if parsing fails
                }
            } catch (\Exception $e) {
                log_message('error', 'XFormParser exception: ' . $e->getMessage());
                // Continue without fields if parsing fails
            }

            $data = [
                'title' => 'Form Details - ' . $form['form_name'],
                'form' => $form,
                'submissions' => $submissions,
                'access_records' => $accessRecords,
                'fields' => $fields,
                'stats' => $this->getFormStatistics($form['form_id'])
            ];

            return view('admin/kobo/forms/form', $data);

        } catch (\Exception $e) {
            log_message('error', 'Form show error: ' . $e->getMessage());
            return redirect()->to('/admin/kobo/forms')->with('error', 'Unable to load form');
        }
    }

    /**
     * Show form edit page
     */
    public function edit(int $id): string
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Form not found');
            }

            $data = [
                'title' => 'Edit Form - ' . $form['form_name'],
                'form' => $form,
                'validation' => session()->getFlashdata('validation')
            ];

            return view('admin/kobo/forms/form_simple', $data);

        } catch (\Exception $e) {
            log_message('error', 'Form edit error: ' . $e->getMessage());
            return redirect()->to('/admin/kobo/forms')->with('error', 'Unable to load form for editing');
        }
    }

    /**
     * Update form
     */
    public function update(int $id): ResponseInterface
    {
        log_message('debug', 'Update method called for form ID: ' . $id);
        log_message('debug', 'POST data: ' . json_encode($this->request->getPost()));

        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Form not found');
            }

            // Simple validation - only check required fields
            $formName = $this->request->getPost('form_name');
            $status = $this->request->getPost('status');

            if (empty($formName)) {
                return redirect()->back()->withInput()->with('error', 'Form name is required');
            }

            if (empty($status)) {
                return redirect()->back()->withInput()->with('error', 'Status is required');
            }

            $oldData = $form;
            $newData = [
                'form_name' => $formName,
                'status' => $status
            ];

            // Add version if provided, otherwise keep existing
            $version = $this->request->getPost('version');
            if (!empty($version)) {
                $newData['version'] = $version;
            }

            log_message('debug', 'Update data: ' . json_encode($newData));

            // Handle XML file update if provided
            $file = $this->request->getFile('xml_file');
            if ($file && $file->isValid()) {
                $xmlContent = file_get_contents($file->getTempName());

                if (empty($xmlContent)) {
                    return redirect()->back()->withInput()->with('error', 'XML file is empty or could not be read');
                }

                // Simple XML validation
                $xml = simplexml_load_string($xmlContent);
                if ($xml === false) {
                    return redirect()->back()->withInput()->with('error', 'Invalid XML format');
                }

                $newData['xml_content'] = $xmlContent;
            }

            log_message('debug', 'Attempting to update form ID: ' . $id);

            // Temporarily disable validation for update
            $this->formModel->skipValidation(true);
            $updateResult = $this->formModel->update($id, $newData);

            log_message('debug', 'Update result: ' . ($updateResult ? 'success' : 'failed'));

            if (!$updateResult) {
                $errors = $this->formModel->errors();
                log_message('error', 'Form update errors: ' . json_encode($errors));
                return redirect()->back()->withInput()->with('error', 'Failed to update form: ' . implode(', ', $errors));
            }

            // Log activity - Temporarily disabled
            // $this->auditModel->logFormUpdated($this->getCurrentAdminId(), $id, $oldData, $newData);

            return redirect()->to("/admin/kobo/forms/{$id}")->with('success', 'Form updated successfully');

        } catch (\Exception $e) {
            log_message('error', 'Form update error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Update failed: ' . $e->getMessage());
        }
    }

    /**
     * Delete form
     */
    public function delete(int $id): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                return redirect()->to('/admin/kobo/forms')->with('error', 'Form not found');
            }

            // Check if form has submissions
            if ($this->formModel->hasSubmissions($id)) {
                return redirect()->to('/admin/kobo/forms')->with('error', 'Cannot delete form with existing submissions');
            }

            if (!$this->formModel->delete($id)) {
                return redirect()->to('/admin/kobo/forms')->with('error', 'Failed to delete form');
            }

            // Log activity - Temporarily disabled
            // $this->auditModel->logFormDeleted($this->getCurrentAdminId(), $id, $form);

            return redirect()->to('/admin/kobo/forms')->with('success', 'Form deleted successfully');

        } catch (\Exception $e) {
            log_message('error', 'Form delete error: ' . $e->getMessage());
            return redirect()->to('/admin/kobo/forms')->with('error', 'Delete failed');
        }
    }

    /**
     * Form preview modal
     */
    public function preview(int $id): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            return $this->response->setStatusCode(401)->setJSON(['error' => 'Unauthorized']);
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                return $this->response->setStatusCode(404)->setJSON(['error' => 'Form not found']);
            }

            // Parse form structure
            $this->parser->parse($form['xml_content']);
            $fields = $this->parser->extractFields();
            $metadata = $this->parser->extractMetadata();

            return $this->response->setJSON([
                'success' => true,
                'form' => $form,
                'fields' => $fields,
                'metadata' => $metadata
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Form preview error: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON(['error' => 'Preview failed']);
        }
    }

    /**
     * Bulk operations
     */
    public function bulkActions(): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $action = $this->request->getPost('action');
            $formIds = $this->request->getPost('form_ids');

            if (empty($action) || empty($formIds)) {
                return redirect()->back()->with('error', 'Invalid bulk action request');
            }

            $results = [];
            foreach ($formIds as $id) {
                switch ($action) {
                    case 'activate':
                        $results[$id] = $this->formModel->activateForm($id);
                        break;
                    case 'deactivate':
                        $results[$id] = $this->formModel->deactivateForm($id);
                        break;
                    case 'draft':
                        $results[$id] = $this->formModel->setDraftStatus($id);
                        break;
                    default:
                        $results[$id] = false;
                }
            }

            $successful = array_sum($results);
            $total = count($results);

            // Log bulk operation - Temporarily disabled
            /*
            $this->auditModel->logActivity(
                $this->getCurrentAdminId(),
                'form',
                'bulk_' . $action,
                null,
                ['form_ids' => $formIds, 'success_count' => $successful, 'total_count' => $total]
            );
            */

            return redirect()->back()->with('success', "Bulk {$action}: {$successful}/{$total} forms processed");

        } catch (\Exception $e) {
            log_message('error', 'Bulk action error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Bulk operation failed');
        }
    }

    /**
     * Clone form
     */
    public function clone(int $id): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                return redirect()->to('/admin/kobo/forms')->with('error', 'Form not found');
            }

            $newVersion = $this->getNextVersion($form['version']);
            $clonedId = $this->formModel->cloneForm($id, $newVersion);

            if (!$clonedId) {
                return redirect()->to('/admin/kobo/forms')->with('error', 'Failed to clone form');
            }

            // Log activity - Temporarily disabled
            /*
            $this->auditModel->logActivity(
                $this->getCurrentAdminId(),
                'form',
                'cloned',
                $clonedId,
                ['original_id' => $id, 'new_version' => $newVersion]
            );
            */

            return redirect()->to("/admin/kobo/forms/{$clonedId}/edit")->with('success', 'Form cloned successfully');

        } catch (\Exception $e) {
            log_message('error', 'Form clone error: ' . $e->getMessage());
            return redirect()->to('/admin/kobo/forms')->with('error', 'Clone failed');
        }
    }

    /**
     * Export forms data
     */
    public function export(): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $format = $this->request->getGet('format') ?? 'csv';
            $forms = $this->formModel->findAll();

            switch ($format) {
                case 'csv':
                    return $this->exportCSV($forms);
                case 'json':
                    return $this->exportJSON($forms);
                default:
                    throw new \InvalidArgumentException('Unsupported export format');
            }

        } catch (\Exception $e) {
            log_message('error', 'Forms export error: ' . $e->getMessage());
            return redirect()->to('/admin/kobo/forms')->with('error', 'Export failed');
        }
    }

    /**
     * Form analytics
     */
    public function analytics(int $id): string
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $form = $this->formModel->find($id);
            if (!$form) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Form not found');
            }

            $analytics = $this->getFormAnalytics($form['form_id']);

            $data = [
                'title' => 'Form Analytics - ' . $form['form_name'],
                'form' => $form,
                'analytics' => $analytics
            ];

            return view('admin/kobo/forms/index', $data);

        } catch (\Exception $e) {
            log_message('error', 'Form analytics error: ' . $e->getMessage());
            return redirect()->to('/admin/kobo/forms')->with('error', 'Unable to load analytics');
        }
    }

    /**
     * Get form statistics
     */
    private function getFormStatistics(string $formId): array
    {
        return [
            'total_submissions' => $this->submissionModel->where('form_id', $formId)->countAllResults(),
            'processed_submissions' => $this->submissionModel->where('form_id', $formId)->where('status', 'processed')->countAllResults(),
            'pending_submissions' => $this->submissionModel->where('form_id', $formId)->where('status', 'pending')->countAllResults(),
            'failed_submissions' => $this->submissionModel->where('form_id', $formId)->where('status', 'failed')->countAllResults(),
            'users_with_access' => $this->accessModel->where('form_id', $formId)->where('status', 'active')->countAllResults()
        ];
    }

    /**
     * Get form analytics data
     */
    private function getFormAnalytics(string $formId): array
    {
        // Submissions over time
        $submissionStats = $this->submissionModel->getSubmissionStats($formId);
        
        // Device usage
        $deviceStats = $this->submissionModel->select('device_id, COUNT(*) as count')
                                           ->where('form_id', $formId)
                                           ->groupBy('device_id')
                                           ->findAll();

        return [
            'submission_stats' => $submissionStats,
            'device_stats' => $deviceStats,
            'completion_rate' => $this->calculateCompletionRate($formId)
        ];
    }

    /**
     * Calculate completion rate
     */
    private function calculateCompletionRate(string $formId): float
    {
        $total = $this->submissionModel->where('form_id', $formId)->countAllResults();
        $completed = $this->submissionModel->where('form_id', $formId)->where('status', 'processed')->countAllResults();
        
        return $total > 0 ? ($completed / $total) * 100 : 0;
    }

    /**
     * Get next version number
     */
    private function getNextVersion(string $currentVersion): string
    {
        $parts = explode('.', $currentVersion);
        $minor = (int)($parts[1] ?? 0) + 1;
        return ($parts[0] ?? '1') . '.' . $minor;
    }

    /**
     * Export forms as CSV
     */
    private function exportCSV(array $forms): ResponseInterface
    {
        $csv = "Form ID,Name,Version,Status,Created By,Created At,Submissions\n";
        
        foreach ($forms as $form) {
            $submissionCount = $this->submissionModel->where('form_id', $form['form_id'])->countAllResults();
            $csv .= "\"{$form['form_id']}\",\"{$form['form_name']}\",\"{$form['version']}\",\"{$form['status']}\",\"{$form['created_by']}\",\"{$form['created_at']}\",{$submissionCount}\n";
        }

        return $this->response->download('forms_export_' . date('Y-m-d') . '.csv', $csv)
                            ->setContentType('text/csv');
    }

    /**
     * Export forms as JSON
     */
    private function exportJSON(array $forms): ResponseInterface
    {
        $data = [
            'export_date' => date('Y-m-d H:i:s'),
            'total_forms' => count($forms),
            'forms' => $forms
        ];

        return $this->response->download('forms_export_' . date('Y-m-d') . '.json', json_encode($data, JSON_PRETTY_PRINT))
                            ->setContentType('application/json');
    }

    /**
     * Check admin authentication
     */
    private function isAdminAuthenticated(): bool
    {
        // For development/testing purposes, return true
        return true;
        
        // Original session-based authentication:
        /*
        $session = session();
        $userId = $session->get('admin_user_id');
        
        if (!$userId) {
            return false;
        }

        $user = $this->userModel->find($userId);
        return $user && $user['status'] === 'active' && in_array($user['role'], ['admin', 'manager']);
        */
    }

    /**
     * Get current admin user ID
     */
    private function getCurrentAdminId(): int
    {
        // For development/testing purposes, return a dummy ID
        return 1;
        
        // Original session-based authentication:
        // return session()->get('admin_user_id');
    }
}