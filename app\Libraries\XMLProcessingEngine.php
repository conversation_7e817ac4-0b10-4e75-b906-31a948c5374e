<?php

namespace App\Libraries;

use App\Libraries\SecurityValidator;

/**
 * XML Processing Engine
 * 
 * Advanced XML processing, transformation, and conversion engine
 */
class XMLProcessingEngine
{
    protected $securityValidator;
    protected $transformationRules = [];
    protected $namespaces = [];
    protected $validationSchemas = [];

    public function __construct()
    {
        $this->securityValidator = new SecurityValidator();
        $this->loadDefaultTransformationRules();
    }

    /**
     * Process XML content with transformation rules
     */
    public function processXML(string $xmlContent, array $options = []): array
    {
        try {
            // Validate XML security
            $securityCheck = $this->securityValidator->validateXML($xmlContent);
            if (!$securityCheck['valid']) {
                throw new \Exception('XML security validation failed: ' . implode(', ', $securityCheck['errors']));
            }

            // Parse XML document
            $dom = $this->parseXMLDocument($xmlContent);
            
            // Apply transformations
            $transformedData = $this->applyTransformations($dom, $options);
            
            // Convert to desired format
            $outputFormat = $options['output_format'] ?? 'json';
            $convertedData = $this->convertToFormat($transformedData, $outputFormat);

            return [
                'success' => true,
                'data' => $convertedData,
                'metadata' => $this->extractMetadata($dom),
                'statistics' => $this->generateStatistics($transformedData)
            ];

        } catch (\Exception $e) {
            log_message('error', 'XML Processing failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Parse XML document with enhanced error handling
     */
    protected function parseXMLDocument(string $xmlContent): \DOMDocument
    {
        $dom = new \DOMDocument('1.0', 'UTF-8');
        $dom->preserveWhiteSpace = false;
        $dom->formatOutput = true;
        
        // Disable external entity loading for security
        $previousValue = libxml_disable_entity_loader(true);
        
        try {
            libxml_use_internal_errors(true);
            $success = $dom->loadXML($xmlContent, LIBXML_NOCDATA | LIBXML_NOBLANKS);
            
            if (!$success) {
                $errors = libxml_get_errors();
                $errorMessages = array_map(function($error) {
                    return "Line {$error->line}: {$error->message}";
                }, $errors);
                throw new \Exception('XML parsing failed: ' . implode('; ', $errorMessages));
            }

            return $dom;

        } finally {
            libxml_disable_entity_loader($previousValue);
            libxml_clear_errors();
        }
    }

    /**
     * Apply transformation rules to XML document
     */
    protected function applyTransformations(\DOMDocument $dom, array $options): array
    {
        $transformedData = [];
        $xpath = new \DOMXPath($dom);
        
        // Register namespaces
        foreach ($this->namespaces as $prefix => $uri) {
            $xpath->registerNamespace($prefix, $uri);
        }

        // Get transformation rules
        $rules = $options['transformation_rules'] ?? $this->transformationRules;
        
        foreach ($rules as $rule) {
            $ruleResult = $this->applyTransformationRule($xpath, $rule);
            if ($ruleResult !== null) {
                $transformedData = array_merge_recursive($transformedData, $ruleResult);
            }
        }

        return $transformedData;
    }

    /**
     * Apply individual transformation rule
     */
    protected function applyTransformationRule(\DOMXPath $xpath, array $rule): ?array
    {
        try {
            $queryPath = $rule['xpath'] ?? '';
            $targetField = $rule['target_field'] ?? '';
            $dataType = $rule['data_type'] ?? 'string';
            $transformation = $rule['transformation'] ?? null;
            
            if (empty($queryPath) || empty($targetField)) {
                return null;
            }

            $nodes = $xpath->query($queryPath);
            $result = [];

            foreach ($nodes as $node) {
                $value = $this->extractNodeValue($node, $dataType);
                
                // Apply transformation if specified
                if ($transformation && is_callable($transformation)) {
                    $value = $transformation($value);
                } elseif (is_string($transformation)) {
                    $value = $this->applyBuiltInTransformation($value, $transformation);
                }

                // Handle array vs single value
                if ($rule['multiple'] ?? false) {
                    $result[$targetField][] = $value;
                } else {
                    $result[$targetField] = $value;
                }
            }

            return $result;

        } catch (\Exception $e) {
            log_message('warning', 'Transformation rule failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract value from DOM node with type conversion
     */
    protected function extractNodeValue(\DOMNode $node, string $dataType)
    {
        $value = trim($node->nodeValue);
        
        switch ($dataType) {
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'date':
                return $this->parseDate($value);
            case 'array':
                return explode(',', $value);
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }

    /**
     * Apply built-in transformations
     */
    protected function applyBuiltInTransformation($value, string $transformation)
    {
        switch ($transformation) {
            case 'uppercase':
                return strtoupper($value);
            case 'lowercase':
                return strtolower($value);
            case 'trim':
                return trim($value);
            case 'capitalize':
                return ucwords($value);
            case 'slug':
                return $this->createSlug($value);
            case 'clean_phone':
                return preg_replace('/[^0-9+]/', '', $value);
            case 'clean_email':
                return filter_var($value, FILTER_SANITIZE_EMAIL);
            case 'extract_numbers':
                return preg_replace('/[^0-9]/', '', $value);
            default:
                return $value;
        }
    }

    /**
     * Convert processed data to specified format
     */
    protected function convertToFormat(array $data, string $format): mixed
    {
        switch (strtolower($format)) {
            case 'json':
                return $data;
            case 'xml':
                return $this->arrayToXML($data);
            case 'csv':
                return $this->arrayToCSV($data);
            case 'flatten':
                return $this->flattenArray($data);
            default:
                return $data;
        }
    }

    /**
     * Convert array to XML
     */
    protected function arrayToXML(array $data, string $rootElement = 'data'): string
    {
        $xml = new \SimpleXMLElement("<{$rootElement}></{$rootElement}>");
        $this->arrayToXMLRecursive($data, $xml);
        return $xml->asXML();
    }

    /**
     * Recursively convert array to XML
     */
    protected function arrayToXMLRecursive(array $data, \SimpleXMLElement $xml): void
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $subnode = $xml->addChild($key);
                $this->arrayToXMLRecursive($value, $subnode);
            } else {
                $xml->addChild($key, htmlspecialchars($value));
            }
        }
    }

    /**
     * Convert array to CSV format
     */
    protected function arrayToCSV(array $data): string
    {
        if (empty($data)) {
            return '';
        }

        $output = fopen('php://temp', 'r+');
        
        // Get headers from first row
        $firstRow = reset($data);
        if (is_array($firstRow)) {
            fputcsv($output, array_keys($firstRow));
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
        } else {
            // Single dimensional array
            fputcsv($output, array_keys($data));
            fputcsv($output, array_values($data));
        }
        
        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);
        
        return $csv;
    }

    /**
     * Flatten nested array
     */
    protected function flattenArray(array $array, string $prefix = ''): array
    {
        $result = [];
        
        foreach ($array as $key => $value) {
            $newKey = $prefix === '' ? $key : $prefix . '.' . $key;
            
            if (is_array($value)) {
                $result = array_merge($result, $this->flattenArray($value, $newKey));
            } else {
                $result[$newKey] = $value;
            }
        }
        
        return $result;
    }

    /**
     * Extract metadata from XML document
     */
    protected function extractMetadata(\DOMDocument $dom): array
    {
        $metadata = [
            'version' => $dom->version,
            'encoding' => $dom->encoding,
            'element_count' => 0,
            'namespaces' => [],
            'form_info' => []
        ];

        // Count elements
        $xpath = new \DOMXPath($dom);
        $allElements = $xpath->query('//*');
        $metadata['element_count'] = $allElements->length;

        // Extract namespaces
        $rootElement = $dom->documentElement;
        if ($rootElement) {
            foreach ($xpath->query('namespace::*', $rootElement) as $node) {
                $metadata['namespaces'][$node->localName] = $node->nodeValue;
            }
        }

        // Extract form-specific information
        $formInfo = $this->extractFormMetadata($xpath);
        if (!empty($formInfo)) {
            $metadata['form_info'] = $formInfo;
        }

        return $metadata;
    }

    /**
     * Extract form-specific metadata
     */
    protected function extractFormMetadata(\DOMXPath $xpath): array
    {
        $formInfo = [];

        // Try to extract common form elements
        $formQueries = [
            'title' => '//h:title | //title',
            'form_id' => '//@id | //h:head/h:model/@id',
            'version' => '//@version | //h:head/h:model/@version',
            'questions' => 'count(//input | //select | //textarea)',
            'groups' => 'count(//group)',
            'repeats' => 'count(//repeat)'
        ];

        foreach ($formQueries as $key => $query) {
            try {
                $result = $xpath->evaluate($query);
                if ($result !== false && $result !== '') {
                    $formInfo[$key] = $result;
                }
            } catch (\Exception $e) {
                // Ignore xpath errors for optional metadata
            }
        }

        return $formInfo;
    }

    /**
     * Generate processing statistics
     */
    protected function generateStatistics(array $data): array
    {
        return [
            'total_fields' => $this->countFields($data),
            'data_types' => $this->analyzeDataTypes($data),
            'processing_time' => microtime(true) - (defined('PROCESSING_START') ? PROCESSING_START : microtime(true)),
            'memory_usage' => memory_get_usage(true),
            'has_nested_data' => $this->hasNestedData($data)
        ];
    }

    /**
     * Count total fields in processed data
     */
    protected function countFields(array $data): int
    {
        $count = 0;
        foreach ($data as $value) {
            if (is_array($value)) {
                $count += $this->countFields($value);
            } else {
                $count++;
            }
        }
        return $count;
    }

    /**
     * Analyze data types in processed data
     */
    protected function analyzeDataTypes(array $data): array
    {
        $types = [];
        foreach ($data as $value) {
            if (is_array($value)) {
                $nestedTypes = $this->analyzeDataTypes($value);
                $types = array_merge_recursive($types, $nestedTypes);
            } else {
                $type = gettype($value);
                $types[$type] = ($types[$type] ?? 0) + 1;
            }
        }
        return $types;
    }

    /**
     * Check if data has nested arrays
     */
    protected function hasNestedData(array $data): bool
    {
        foreach ($data as $value) {
            if (is_array($value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Load default transformation rules
     */
    protected function loadDefaultTransformationRules(): void
    {
        $this->transformationRules = [
            // XForm specific transformations
            [
                'xpath' => '//h:head/h:title | //title',
                'target_field' => 'form_title',
                'data_type' => 'string',
                'transformation' => 'trim'
            ],
            [
                'xpath' => '//input[@type="text"]/@ref | //input/@ref',
                'target_field' => 'text_inputs',
                'data_type' => 'string',
                'multiple' => true
            ],
            [
                'xpath' => '//select/@ref',
                'target_field' => 'select_inputs',
                'data_type' => 'string',
                'multiple' => true
            ],
            [
                'xpath' => '//group/@ref',
                'target_field' => 'groups',
                'data_type' => 'string',
                'multiple' => true
            ],
            [
                'xpath' => '//repeat/@nodeset',
                'target_field' => 'repeats',
                'data_type' => 'string',
                'multiple' => true
            ]
        ];

        // Register common XForm namespaces
        $this->namespaces = [
            'h' => 'http://www.w3.org/1999/xhtml',
            'xf' => 'http://www.w3.org/2002/xforms',
            'jr' => 'http://openrosa.org/javarosa'
        ];
    }

    /**
     * Add custom transformation rule
     */
    public function addTransformationRule(array $rule): self
    {
        $this->transformationRules[] = $rule;
        return $this;
    }

    /**
     * Set transformation rules
     */
    public function setTransformationRules(array $rules): self
    {
        $this->transformationRules = $rules;
        return $this;
    }

    /**
     * Add namespace for XPath queries
     */
    public function addNamespace(string $prefix, string $uri): self
    {
        $this->namespaces[$prefix] = $uri;
        return $this;
    }

    /**
     * Validate XML against schema
     */
    public function validateAgainstSchema(string $xmlContent, string $schemaPath): array
    {
        try {
            $dom = $this->parseXMLDocument($xmlContent);
            
            if (!file_exists($schemaPath)) {
                throw new \Exception("Schema file not found: {$schemaPath}");
            }

            libxml_use_internal_errors(true);
            $isValid = $dom->schemaValidate($schemaPath);
            $errors = libxml_get_errors();

            return [
                'valid' => $isValid,
                'errors' => array_map(function($error) {
                    return "Line {$error->line}: {$error->message}";
                }, $errors)
            ];

        } catch (\Exception $e) {
            return [
                'valid' => false,
                'errors' => [$e->getMessage()]
            ];
        } finally {
            libxml_clear_errors();
        }
    }

    /**
     * Helper methods
     */
    protected function parseDate(string $dateString): ?string
    {
        try {
            $date = new \DateTime($dateString);
            return $date->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            return null;
        }
    }

    protected function createSlug(string $text): string
    {
        $text = strtolower($text);
        $text = preg_replace('/[^a-z0-9]+/', '-', $text);
        return trim($text, '-');
    }

    /**
     * Process submission XML to standardized format
     */
    public function processSubmissionXML(string $xmlContent): array
    {
        // Define submission-specific transformation rules
        $submissionRules = [
            [
                'xpath' => '//@instanceID | //meta/instanceID',
                'target_field' => 'instance_id',
                'data_type' => 'string'
            ],
            [
                'xpath' => '//meta/timeStart',
                'target_field' => 'start_time',
                'data_type' => 'date'
            ],
            [
                'xpath' => '//meta/timeEnd',
                'target_field' => 'end_time',
                'data_type' => 'date'
            ],
            [
                'xpath' => '//meta/deviceID',
                'target_field' => 'device_id',
                'data_type' => 'string'
            ]
        ];

        return $this->processXML($xmlContent, [
            'transformation_rules' => $submissionRules,
            'output_format' => 'json'
        ]);
    }
}