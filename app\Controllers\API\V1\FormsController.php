<?php

namespace App\Controllers\API\V1;

use App\Controllers\BaseController;
use App\Models\Kobo\KoboFormModel;
use App\Models\Kobo\KoboUserModel;
use App\Models\Kobo\KoboFormAccessModel;
use App\Models\Kobo\KoboAuditLogModel;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Forms API Controller
 * 
 * OpenRosa compliant API controller for form management
 * Handles form list, form download, and form manifest requests
 */
class FormsController extends BaseController
{
    protected $formModel;
    protected $userModel;
    protected $accessModel;
    protected $auditModel;

    public function __construct()
    {
        $this->formModel = new KoboFormModel();
        $this->userModel = new KoboUserModel();
        $this->accessModel = new KoboFormAccessModel();
        $this->auditModel = new KoboAuditLogModel();
    }

    /**
     * Get form list (OpenRosa compliant)
     * GET /api/v1/formList
     */
    public function getFormList(): ResponseInterface
    {
        try {
            // Authenticate user
            $user = $this->authenticateUser();
            if (!$user) {
                return $this->respond([
                    'error' => 'Authentication required'
                ], 401, 'Unauthorized');
            }

            // Get accessible forms for user
            $forms = $this->formModel->getOpenRosaFormList($user['id']);
            
            // Build OpenRosa XML response
            $xml = $this->buildFormListXML($forms);
            
            // Log activity
            $this->auditModel->logActivity(
                $user['id'], 
                'api', 
                'form_list_requested',
                null,
                ['form_count' => count($forms)]
            );

            return $this->respond($xml, 200)
                        ->setContentType('text/xml; charset=utf-8')
                        ->setHeader('X-OpenRosa-Version', '1.0')
                        ->setHeader('X-OpenRosa-Accept-Content-Length', '10485760');

        } catch (\Exception $e) {
            log_message('error', 'Form list error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get specific form
     * GET /api/v1/forms/{formId}
     */
    public function getForm(string $formId): ResponseInterface
    {
        try {
            // Authenticate user
            $user = $this->authenticateUser();
            if (!$user) {
                return $this->respond([
                    'error' => 'Authentication required'
                ], 401);
            }

            // Check form access
            if (!$this->accessModel->hasAccess($user['id'], $formId)) {
                $this->auditModel->logSecurityEvent(
                    'unauthorized_form_access_attempt',
                    $user['id'],
                    ['form_id' => $formId]
                );
                
                return $this->respond([
                    'error' => 'Access denied'
                ], 403);
            }

            // Get form
            $form = $this->formModel->findByFormId($formId);
            if (!$form) {
                return $this->respond([
                    'error' => 'Form not found'
                ], 404);
            }

            // Log activity
            $this->auditModel->logActivity(
                $user['id'],
                'form',
                'downloaded',
                $form['id'],
                ['form_id' => $formId]
            );

            return $this->respond($form['xml_content'], 200)
                        ->setContentType('text/xml; charset=utf-8')
                        ->setHeader('X-OpenRosa-Version', '1.0');

        } catch (\Exception $e) {
            log_message('error', 'Form download error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get form manifest
     * GET /api/v1/forms/{formId}/manifest
     */
    public function getFormManifest(string $formId): ResponseInterface
    {
        try {
            // Authenticate user
            $user = $this->authenticateUser();
            if (!$user) {
                return $this->respond([
                    'error' => 'Authentication required'
                ], 401);
            }

            // Check form access
            if (!$this->accessModel->hasAccess($user['id'], $formId)) {
                return $this->respond([
                    'error' => 'Access denied'
                ], 403);
            }

            // Get form manifest
            $manifest = $this->formModel->getFormManifest($formId);
            
            // Build manifest XML
            $xml = $this->buildManifestXML($manifest);
            
            // Log activity
            $this->auditModel->logActivity(
                $user['id'],
                'form',
                'manifest_requested',
                null,
                ['form_id' => $formId, 'media_count' => count($manifest)]
            );

            return $this->respond($xml, 200)
                        ->setContentType('text/xml; charset=utf-8')
                        ->setHeader('X-OpenRosa-Version', '1.0');

        } catch (\Exception $e) {
            log_message('error', 'Form manifest error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Upload new form (Admin only)
     * POST /api/v1/forms
     */
    public function uploadForm(): ResponseInterface
    {
        try {
            // Authenticate user
            $user = $this->authenticateUser();
            if (!$user) {
                return $this->respond([
                    'error' => 'Authentication required'
                ], 401);
            }

            // Check admin permissions
            if (!$this->userModel->isAdmin($user['id'])) {
                return $this->respond([
                    'error' => 'Admin access required'
                ], 403);
            }

            // Get form data
            $xmlContent = $this->request->getPost('xml_content');
            $formName = $this->request->getPost('form_name');
            
            if (!$xmlContent || !$formName) {
                return $this->respond([
                    'error' => 'XML content and form name are required'
                ], 400);
            }

            // Extract metadata from XML
            $metadata = $this->formModel->extractFormMetadata($xmlContent);
            if (!$metadata) {
                return $this->respond([
                    'error' => 'Invalid XML format'
                ], 400);
            }

            // Create form
            $formData = [
                'form_id' => $metadata['form_id'] ?? uniqid('form_'),
                'form_name' => $formName,
                'xml_content' => $xmlContent,
                'version' => $metadata['version'] ?? '1.0',
                'status' => 'draft',
                'created_by' => $user['id']
            ];

            $formId = $this->formModel->createForm($formData);
            if (!$formId) {
                return $this->respond([
                    'error' => 'Failed to create form'
                ], 500);
            }

            // Log activity
            $this->auditModel->logFormCreated($user['id'], $formId, $formData);

            return $this->respond([
                'success' => true,
                'form_id' => $formId,
                'message' => 'Form uploaded successfully'
            ], 201);

        } catch (\Exception $e) {
            log_message('error', 'Form upload error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Update form status
     * PUT /api/v1/forms/{formId}/status
     */
    public function updateFormStatus(int $formId): ResponseInterface
    {
        try {
            // Authenticate user
            $user = $this->authenticateUser();
            if (!$user) {
                return $this->respond([
                    'error' => 'Authentication required'
                ], 401);
            }

            // Check admin permissions
            if (!$this->userModel->isAdmin($user['id']) && !$this->userModel->isManager($user['id'])) {
                return $this->respond([
                    'error' => 'Manager or admin access required'
                ], 403);
            }

            $status = $this->request->getJSON(true)['status'] ?? '';
            if (!in_array($status, ['active', 'inactive', 'draft'])) {
                return $this->respond([
                    'error' => 'Invalid status. Must be active, inactive, or draft'
                ], 400);
            }

            // Get old form data for audit
            $oldForm = $this->formModel->find($formId);
            if (!$oldForm) {
                return $this->respond([
                    'error' => 'Form not found'
                ], 404);
            }

            // Update status
            $success = false;
            switch ($status) {
                case 'active':
                    $success = $this->formModel->activateForm($formId);
                    break;
                case 'inactive':
                    $success = $this->formModel->deactivateForm($formId);
                    break;
                case 'draft':
                    $success = $this->formModel->setDraftStatus($formId);
                    break;
            }

            if (!$success) {
                return $this->respond([
                    'error' => 'Failed to update form status'
                ], 500);
            }

            // Log activity
            $this->auditModel->logFormUpdated(
                $user['id'],
                $formId,
                ['status' => $oldForm['status']],
                ['status' => $status]
            );

            return $this->respond([
                'success' => true,
                'message' => "Form status updated to {$status}"
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Form status update error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Delete form
     * DELETE /api/v1/forms/{formId}
     */
    public function deleteForm(int $formId): ResponseInterface
    {
        try {
            // Authenticate user
            $user = $this->authenticateUser();
            if (!$user) {
                return $this->respond([
                    'error' => 'Authentication required'
                ], 401);
            }

            // Check admin permissions
            if (!$this->userModel->isAdmin($user['id'])) {
                return $this->respond([
                    'error' => 'Admin access required'
                ], 403);
            }

            // Get form for audit
            $form = $this->formModel->find($formId);
            if (!$form) {
                return $this->respond([
                    'error' => 'Form not found'
                ], 404);
            }

            // Check if form has submissions
            if ($this->formModel->hasSubmissions($formId)) {
                return $this->respond([
                    'error' => 'Cannot delete form with existing submissions'
                ], 409);
            }

            // Delete form
            if (!$this->formModel->delete($formId)) {
                return $this->respond([
                    'error' => 'Failed to delete form'
                ], 500);
            }

            // Log activity
            $this->auditModel->logFormDeleted($user['id'], $formId, $form);

            return $this->respond([
                'success' => true,
                'message' => 'Form deleted successfully'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Form deletion error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Authenticate user using Basic Auth or JWT
     */
    private function authenticateUser(): array|false
    {
        // Try JWT authentication first
        $jwt = $this->getJWTFromHeader();
        if ($jwt) {
            return $this->validateJWT($jwt);
        }

        // Fall back to Basic Authentication
        $authHeader = $this->request->getHeader('Authorization');
        if (!$authHeader || !str_starts_with($authHeader->getValue(), 'Basic ')) {
            return false;
        }

        $credentials = base64_decode(substr($authHeader->getValue(), 6));
        $parts = explode(':', $credentials, 2);
        
        if (count($parts) !== 2) {
            return false;
        }

        [$username, $password] = $parts;
        return $this->userModel->authenticateUser($username, $password);
    }

    /**
     * Get JWT token from Authorization header
     */
    private function getJWTFromHeader(): string|false
    {
        $authHeader = $this->request->getHeader('Authorization');
        if (!$authHeader || !str_starts_with($authHeader->getValue(), 'Bearer ')) {
            return false;
        }

        return substr($authHeader->getValue(), 7);
    }

    /**
     * Validate JWT token (placeholder - will be implemented in AuthController)
     */
    private function validateJWT(string $token): array|false
    {
        // TODO: Implement JWT validation
        // This will be properly implemented when we create the JWT authentication system
        return false;
    }

    /**
     * Build OpenRosa compliant form list XML
     */
    private function buildFormListXML(array $forms): string
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><xforms xmlns="http://openrosa.org/xforms/xformsList"></xforms>');
        
        foreach ($forms as $form) {
            $xform = $xml->addChild('xform');
            $xform->addChild('formID', htmlspecialchars($form['formID']));
            $xform->addChild('name', htmlspecialchars($form['name']));
            $xform->addChild('version', htmlspecialchars($form['version']));
            $xform->addChild('hash', htmlspecialchars($form['hash']));
            $xform->addChild('downloadUrl', htmlspecialchars($form['downloadUrl']));
            
            if (!empty($form['manifestUrl'])) {
                $xform->addChild('manifestUrl', htmlspecialchars($form['manifestUrl']));
            }
        }

        return $xml->asXML();
    }

    /**
     * Build manifest XML
     */
    private function buildManifestXML(array $manifest): string
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><manifest xmlns="http://openrosa.org/xforms/xformsManifest"></manifest>');
        
        foreach ($manifest as $media) {
            $mediaFile = $xml->addChild('mediaFile');
            $mediaFile->addChild('filename', htmlspecialchars($media['stored_filename']));
            $mediaFile->addChild('hash', 'md5:' . md5_file($media['file_path']));
            $mediaFile->addChild('downloadUrl', base_url("api/v1/media/download/{$media['id']}"));
        }

        return $xml->asXML();
    }
}