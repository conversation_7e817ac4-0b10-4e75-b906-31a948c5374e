<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

// Default route - Dashboard landing page
$routes->get('/', 'Dashboard::index');

// Dashboard routes
$routes->get('/dashboard', 'Dashboard::index');

// Documentation routes
$routes->get('/docs', 'Documentation::index');
$routes->get('/modules', 'Modules::index');

// Module Routes
$routes->group('modules', ['namespace' => 'App\Controllers\Modules'], function($routes) {
    // Kobo Collect Module
    $routes->get('kobo-collect', 'KoboCollect::index');
    $routes->get('kobo-collect/admin', 'KoboCollect::admin');
    
    // Other modules (coming soon)
    $routes->get('database-testing', 'DatabaseTesting::index');
    $routes->get('auth-testing', 'AuthTesting::index');
    $routes->get('api-testing', 'ApiTesting::index');
    $routes->get('file-testing', 'FileTesting::index');
    $routes->get('email-testing', 'EmailTesting::index');
    $routes->get('performance-testing', 'PerformanceTesting::index');
    $routes->get('testing-tools', 'TestingTools::index');
});

// Kobo Collect API Routes - OpenRosa Compliant
$routes->group('api/v1', ['namespace' => 'App\Controllers\API\V1'], function($routes) {
    // Authentication endpoints
    $routes->post('auth/login', 'AuthController::login');
    $routes->post('auth/logout', 'AuthController::logout');
    $routes->post('auth/register', 'AuthController::register');
    $routes->post('auth/refresh', 'AuthController::refresh');
    $routes->get('auth/me', 'AuthController::me');
    $routes->post('auth/change-password', 'AuthController::changePassword');
    
    // Form management endpoints (OpenRosa)
    $routes->get('formList', 'FormsController::getFormList');
    $routes->get('forms/(:segment)', 'FormsController::getForm/$1');
    $routes->get('forms/(:segment)/manifest', 'FormsController::getFormManifest/$1');
    $routes->post('forms', 'FormsController::uploadForm');
    $routes->put('forms/(:num)/status', 'FormsController::updateFormStatus/$1');
    $routes->delete('forms/(:num)', 'FormsController::deleteForm/$1');
    
    // Data submission endpoints (OpenRosa)
    $routes->post('submission', 'SubmissionsController::submit');
    $routes->post('submissions/(:segment)', 'SubmissionsController::submitToForm/$1');
    $routes->get('submissions', 'SubmissionsController::getSubmissions');
    $routes->get('submissions/(:num)', 'SubmissionsController::getSubmission/$1');
    $routes->put('submissions/(:num)/status', 'SubmissionsController::updateSubmissionStatus/$1');
    $routes->post('submissions/batch', 'SubmissionsController::batchProcess');
    $routes->get('submissions/export', 'SubmissionsController::export');
    
    // JSON API Export endpoints with pagination and real-time sync
    $routes->get('export/submissions', 'ExportController::submissions');
    $routes->get('export/forms', 'ExportController::forms');
    $routes->get('export/statistics', 'ExportController::statistics');
    $routes->get('export/sync', 'ExportController::sync');
    $routes->get('export/docs', 'ExportController::docs');
    $routes->get('export/health', 'ExportController::health');
    
    // CORS preflight handling
    $routes->options('(:any)', function() {
        $handler = new \App\Libraries\KoboAPIHandler();
        return $handler->handleCORSPreflight();
    });
});

// Admin Panel Routes
$routes->group('admin', function($routes) {
    // Kobo Collect Admin (legacy support)
    $routes->group('kobo', ['namespace' => 'App\Controllers\Admin'], function($routes) {
        $routes->get('/', 'DashboardController::index');
        $routes->get('dashboard', 'DashboardController::index'); // Additional route for /dashboard
        
        // Forms routes - explicit mapping to avoid conflicts
        $routes->get('forms', 'FormManagerController::index');
        $routes->get('forms/new', 'FormManagerController::new');
        $routes->get('forms/create', 'FormManagerController::new'); // Alias for create -> new
        $routes->post('forms', 'FormManagerController::create');
        $routes->get('forms/(:num)', 'FormManagerController::show/$1');
        $routes->get('forms/(:num)/edit', 'FormManagerController::edit/$1');
        $routes->put('forms/(:num)', 'FormManagerController::update/$1');
        $routes->post('forms/(:num)', 'FormManagerController::update/$1'); // Allow POST for form updates
        $routes->delete('forms/(:num)', 'FormManagerController::delete/$1');
        
        $routes->resource('users', ['controller' => 'UserManagerController']);
        
        // Submission management routes
        $routes->get('submissions', 'SubmissionManagerController::index');
        $routes->get('submissions/(:num)', 'SubmissionManagerController::show/$1');
        $routes->post('submissions/(:num)/status', 'SubmissionManagerController::updateStatus/$1');
        $routes->delete('submissions/(:num)', 'SubmissionManagerController::delete/$1');
        $routes->get('submissions/(:num)/export', 'SubmissionManagerController::exportSingle/$1');
        $routes->post('submissions/bulk', 'SubmissionManagerController::bulkOperation');
        $routes->get('submissions/export', 'SubmissionManagerController::export');
        $routes->get('submissions/statistics', 'SubmissionManagerController::getStatistics');
        $routes->get('submissions/search', 'SubmissionManagerController::search');
        $routes->get('submissions/(:num)/visualization', 'SubmissionManagerController::getDataVisualization/$1');
        
        $routes->get('analytics', 'AnalyticsController::index');
        $routes->get('settings', 'SettingsController::index');
    });
    
    // CodiTest Admin Panel Routes 
    $routes->group('', ['namespace' => 'App\Controllers\Admin'], function($routes) {
        $routes->get('export-api', 'DashboardController::exportApi');
    });
});

// Legacy route for backward compatibility
$routes->get('/home', 'Home::index');
