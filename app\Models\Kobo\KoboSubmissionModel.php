<?php

namespace App\Models\Kobo;

use CodeIgniter\Model;

/**
 * Kobo Submission Model
 * 
 * Handles CRUD operations for kobo_submissions table
 * Provides XML/JSON conversion and submission validation
 */
class KoboSubmissionModel extends Model
{
    protected $table = 'kobo_submissions';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'submission_id',
        'form_id',
        'user_id',
        'device_id',
        'xml_data',
        'json_data',
        'status',
        'submitted_at',
        'processed_at',
        'created_at',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'submission_id' => 'required|string|max_length[255]|is_unique[kobo_submissions.submission_id,id,{id}]',
        'form_id' => 'required|string|max_length[255]',
        'device_id' => 'permit_empty|string|max_length[255]',
        'xml_data' => 'required',
        'status' => 'required|in_list[pending,processed,failed,duplicate]'
    ];

    protected $validationMessages = [
        'submission_id' => [
            'required' => 'Submission ID is required',
            'string' => 'Submission ID must be a valid string',
            'max_length' => 'Submission ID cannot exceed 255 characters',
            'is_unique' => 'Submission ID already exists'
        ],
        'form_id' => [
            'required' => 'Form ID is required',
            'string' => 'Form ID must be a valid string',
            'max_length' => 'Form ID cannot exceed 255 characters'
        ],
        'device_id' => [
            'string' => 'Device ID must be a valid string',
            'max_length' => 'Device ID cannot exceed 255 characters'
        ],
        'xml_data' => [
            'required' => 'XML data is required'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be pending, processed, failed, or duplicate'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['processSubmissionData'];
    protected $beforeUpdate = ['processSubmissionData'];

    /**
     * Process submission data before saving
     */
    protected function processSubmissionData(array $data)
    {
        if (isset($data['data']['xml_data'])) {
            // Validate XML
            if (!$this->isValidXml($data['data']['xml_data'])) {
                throw new \CodeIgniter\Database\Exceptions\DataException('Invalid XML data provided');
            }

            // Convert XML to JSON if not already set
            if (!isset($data['data']['json_data']) || empty($data['data']['json_data'])) {
                $data['data']['json_data'] = $this->xmlToJson($data['data']['xml_data']);
            }

            // Generate submission ID if not set
            if (!isset($data['data']['submission_id']) || empty($data['data']['submission_id'])) {
                $data['data']['submission_id'] = $this->generateSubmissionId();
            }

            // Set submitted_at if not set
            if (!isset($data['data']['submitted_at'])) {
                $data['data']['submitted_at'] = date('Y-m-d H:i:s');
            }
        }

        return $data;
    }

    /**
     * Create a new submission
     */
    public function createSubmission(array $submissionData): int|false
    {
        // Set default values
        $submissionData['status'] = $submissionData['status'] ?? 'pending';
        
        // Validate XML data
        if (!$this->isValidXml($submissionData['xml_data'])) {
            return false;
        }

        // Check for duplicates
        if ($this->isDuplicateSubmission($submissionData)) {
            $submissionData['status'] = 'duplicate';
        }

        return $this->insert($submissionData);
    }

    /**
     * Find submission by submission ID
     */
    public function findBySubmissionId(string $submissionId): array|null
    {
        return $this->where('submission_id', $submissionId)->first();
    }

    /**
     * Get submissions by form ID
     */
    public function getSubmissionsByForm(string $formId): array
    {
        return $this->where('form_id', $formId)
                    ->orderBy('submitted_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get submissions by user ID
     */
    public function getSubmissionsByUser(int $userId): array
    {
        return $this->where('user_id', $userId)
                    ->orderBy('submitted_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get submissions by device ID
     */
    public function getSubmissionsByDevice(string $deviceId): array
    {
        return $this->where('device_id', $deviceId)
                    ->orderBy('submitted_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get submissions by status
     */
    public function getSubmissionsByStatus(string $status): array
    {
        return $this->where('status', $status)
                    ->orderBy('submitted_at', 'DESC')
                    ->findAll();
    }

    /**
     * Mark submission as processed
     */
    public function markAsProcessed(int $submissionId): bool
    {
        return $this->update($submissionId, [
            'status' => 'processed',
            'processed_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Mark submission as failed
     */
    public function markAsFailed(int $submissionId): bool
    {
        return $this->update($submissionId, ['status' => 'failed']);
    }

    /**
     * Check if XML is valid
     */
    public function isValidXml(string $xmlData): bool
    {
        libxml_use_internal_errors(true);
        $doc = simplexml_load_string($xmlData);
        
        if ($doc === false) {
            libxml_clear_errors();
            return false;
        }

        return true;
    }

    /**
     * Convert XML to JSON
     */
    public function xmlToJson(string $xmlData): string|false
    {
        try {
            $xml = simplexml_load_string($xmlData);
            if ($xml === false) {
                return false;
            }

            // Convert to array first to handle XML properly
            $array = $this->xmlToArray($xml);
            return json_encode($array, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Convert XML object to array
     */
    private function xmlToArray($xmlObject): array
    {
        $array = [];
        
        foreach ($xmlObject as $key => $value) {
            if (count($value->children()) > 0) {
                $array[$key] = $this->xmlToArray($value);
            } else {
                $array[$key] = (string)$value;
            }
        }

        return $array;
    }

    /**
     * Convert JSON back to XML
     */
    public function jsonToXml(string $jsonData, string $rootElement = 'data'): string|false
    {
        try {
            $data = json_decode($jsonData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return false;
            }

            $xml = new \SimpleXMLElement("<{$rootElement}></{$rootElement}>");
            $this->arrayToXml($data, $xml);
            
            return $xml->asXML();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Convert array to XML
     */
    private function arrayToXml(array $data, \SimpleXMLElement $xml): void
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $subnode = $xml->addChild($key);
                $this->arrayToXml($value, $subnode);
            } else {
                $xml->addChild($key, htmlspecialchars($value));
            }
        }
    }

    /**
     * Generate unique submission ID
     */
    public function generateSubmissionId(): string
    {
        return 'uuid:' . $this->generateUuid();
    }

    /**
     * Generate UUID
     */
    private function generateUuid(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Check for duplicate submissions
     */
    public function isDuplicateSubmission(array $submissionData): bool
    {
        // Check by submission ID if provided
        if (isset($submissionData['submission_id'])) {
            $existing = $this->where('submission_id', $submissionData['submission_id'])->first();
            if ($existing) {
                return true;
            }
        }

        // Check by content hash for form and device
        if (isset($submissionData['form_id']) && isset($submissionData['device_id'])) {
            $contentHash = md5($submissionData['xml_data']);
            $existing = $this->where('form_id', $submissionData['form_id'])
                             ->where('device_id', $submissionData['device_id'])
                             ->where('MD5(xml_data)', $contentHash)
                             ->first();
            
            if ($existing) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get submission statistics
     */
    public function getSubmissionStats(string $formId = null): array
    {
        $db = \Config\Database::connect();
        
        $whereClause = $formId ? "WHERE form_id = '{$formId}'" : '';
        
        $query = $db->query("
            SELECT 
                status,
                COUNT(*) as count,
                DATE(submitted_at) as submission_date
            FROM kobo_submissions 
            {$whereClause}
            GROUP BY status, DATE(submitted_at)
            ORDER BY submission_date DESC, status
        ");

        $results = $query->getResultArray();
        
        // Get total counts
        $totalQuery = $db->query("
            SELECT status, COUNT(*) as total
            FROM kobo_submissions 
            {$whereClause}
            GROUP BY status
        ");

        $totals = [];
        foreach ($totalQuery->getResultArray() as $row) {
            $totals[$row['status']] = $row['total'];
        }

        return [
            'totals' => $totals,
            'daily_stats' => $results,
            'grand_total' => array_sum($totals)
        ];
    }

    /**
     * Get submissions with pagination and filters
     */
    public function getSubmissionsWithPagination(int $perPage = 20, array $filters = []): array
    {
        $builder = $this->select('kobo_submissions.*, kobo_users.username')
                        ->join('kobo_users', 'kobo_users.id = kobo_submissions.user_id', 'left');
        
        // Apply filters
        if (!empty($filters['form_id'])) {
            $builder->where('kobo_submissions.form_id', $filters['form_id']);
        }

        if (!empty($filters['status'])) {
            $builder->where('kobo_submissions.status', $filters['status']);
        }

        if (!empty($filters['device_id'])) {
            $builder->where('kobo_submissions.device_id', $filters['device_id']);
        }

        if (!empty($filters['date_from'])) {
            $builder->where('kobo_submissions.submitted_at >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $builder->where('kobo_submissions.submitted_at <=', $filters['date_to']);
        }

        if (!empty($filters['search'])) {
            $builder->like('kobo_submissions.submission_id', $filters['search']);
        }

        $builder->orderBy('kobo_submissions.submitted_at', 'DESC');

        return [
            'submissions' => $builder->paginate($perPage),
            'pager' => $this->pager
        ];
    }

    /**
     * Validate submission data against form schema
     */
    public function validateSubmissionData(string $xmlData, string $formId): array
    {
        $errors = [];

        // Basic XML validation
        if (!$this->isValidXml($xmlData)) {
            $errors[] = 'Invalid XML format';
            return $errors;
        }

        // Get form definition
        $formModel = new \App\Models\Kobo\KoboFormModel();
        $form = $formModel->findByFormId($formId);

        if (!$form) {
            $errors[] = 'Form not found';
            return $errors;
        }

        // TODO: Implement schema validation against form definition
        // This would require parsing the form's XML schema and validating
        // the submission data against it

        return $errors;
    }

    /**
     * Process pending submissions
     */
    public function processPendingSubmissions(): array
    {
        $pendingSubmissions = $this->getSubmissionsByStatus('pending');
        $processed = 0;
        $failed = 0;

        foreach ($pendingSubmissions as $submission) {
            try {
                // Validate submission data
                $errors = $this->validateSubmissionData($submission['xml_data'], $submission['form_id']);
                
                if (empty($errors)) {
                    $this->markAsProcessed($submission['id']);
                    $processed++;
                } else {
                    $this->markAsFailed($submission['id']);
                    $failed++;
                }
            } catch (\Exception $e) {
                $this->markAsFailed($submission['id']);
                $failed++;
            }
        }

        return [
            'total_processed' => $processed,
            'total_failed' => $failed,
            'total_pending' => count($pendingSubmissions)
        ];
    }

    /**
     * Export submissions to CSV
     */
    public function exportToCSV(string $formId = null): string
    {
        $builder = $this->select('submission_id, form_id, device_id, status, submitted_at, processed_at');
        
        if ($formId) {
            $builder->where('form_id', $formId);
        }

        $submissions = $builder->orderBy('submitted_at', 'DESC')->findAll();
        
        // Create CSV content
        $csv = "Submission ID,Form ID,Device ID,Status,Submitted At,Processed At\n";
        
        foreach ($submissions as $submission) {
            $csv .= sprintf(
                "%s,%s,%s,%s,%s,%s\n",
                $submission['submission_id'],
                $submission['form_id'],
                $submission['device_id'] ?? '',
                $submission['status'],
                $submission['submitted_at'],
                $submission['processed_at'] ?? ''
            );
        }

        return $csv;
    }

    /**
     * Get recent submissions
     */
    public function getRecentSubmissions(int $limit = 10): array
    {
        return $this->select('kobo_submissions.*, kobo_users.username')
                    ->join('kobo_users', 'kobo_users.id = kobo_submissions.user_id', 'left')
                    ->orderBy('kobo_submissions.submitted_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Clean up old submissions
     */
    public function cleanupOldSubmissions(int $daysOld = 365): int
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysOld} days"));
        
        $oldSubmissions = $this->where('submitted_at <', $cutoffDate)
                               ->where('status', 'processed')
                               ->findAll();

        $deletedCount = 0;
        foreach ($oldSubmissions as $submission) {
            if ($this->delete($submission['id'])) {
                $deletedCount++;
            }
        }

        return $deletedCount;
    }
}