<?php

namespace App\Libraries;

/**
 * File Security Scanner
 * 
 * Provides comprehensive file security scanning and validation
 */
class FileSecurityScanner
{
    protected $allowedMimeTypes = [
        'xml' => [
            'text/xml',
            'application/xml',
            'text/plain'
        ],
        'json' => [
            'application/json',
            'text/json',
            'text/plain'
        ],
        'image' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp'
        ]
    ];

    protected $dangerousExtensions = [
        'php', 'php3', 'php4', 'php5', 'phtml', 'phps',
        'asp', 'aspx', 'jsp', 'jspx',
        'exe', 'com', 'bat', 'cmd', 'scr',
        'sh', 'bash', 'zsh', 'fish',
        'js', 'vbs', 'ps1',
        'sql', 'db', 'sqlite'
    ];

    protected $malwareSignatures = [
        // PHP malware patterns
        '/\$[_A-Z]+\s*=\s*[\'"][^\'"]*[\'"]\s*;\s*eval\s*\(/i',
        '/base64_decode\s*\(\s*[\'"][A-Za-z0-9+\/=]+[\'"]\s*\)/i',
        '/eval\s*\(\s*gzinflate\s*\(/i',
        '/system\s*\(\s*\$[_A-Z]+/i',
        '/exec\s*\(\s*\$[_A-Z]+/i',
        '/shell_exec\s*\(\s*\$[_A-Z]+/i',
        '/passthru\s*\(\s*\$[_A-Z]+/i',
        
        // Script injection patterns
        '/<script[^>]*>.*?<\/script>/is',
        '/javascript:[^"\']*["\']*/i',
        '/vbscript:[^"\']*["\']*/i',
        '/onload\s*=/i',
        '/onerror\s*=/i',
        '/onclick\s*=/i',
        
        // Server-side includes
        '/<!--\s*#exec\s+cmd\s*=/i',
        '/<!--\s*#include\s+file\s*=/i',
        
        // SQL injection patterns
        '/UNION\s+SELECT/i',
        '/DROP\s+TABLE/i',
        '/INSERT\s+INTO/i',
        '/DELETE\s+FROM/i'
    ];

    /**
     * Scan uploaded file for security threats
     */
    public function scanFile(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        $results = [
            'safe' => true,
            'threats' => [],
            'file_info' => $this->getFileInfo($file)
        ];

        try {
            // Basic file validation
            if (!$file->isValid()) {
                $results['safe'] = false;
                $results['threats'][] = 'File upload error: ' . $file->getErrorString();
                return $results;
            }

            // Check file extension
            $extensionCheck = $this->checkFileExtension($file);
            if (!$extensionCheck['safe']) {
                $results['safe'] = false;
                $results['threats'] = array_merge($results['threats'], $extensionCheck['threats']);
            }

            // Check MIME type
            $mimeCheck = $this->checkMimeType($file);
            if (!$mimeCheck['safe']) {
                $results['safe'] = false;
                $results['threats'] = array_merge($results['threats'], $mimeCheck['threats']);
            }

            // Check file size
            $sizeCheck = $this->checkFileSize($file);
            if (!$sizeCheck['safe']) {
                $results['safe'] = false;
                $results['threats'] = array_merge($results['threats'], $sizeCheck['threats']);
            }

            // Scan file content
            $contentCheck = $this->scanFileContent($file);
            if (!$contentCheck['safe']) {
                $results['safe'] = false;
                $results['threats'] = array_merge($results['threats'], $contentCheck['threats']);
            }

            // Check for embedded executables
            $embeddedCheck = $this->checkEmbeddedExecutables($file);
            if (!$embeddedCheck['safe']) {
                $results['safe'] = false;
                $results['threats'] = array_merge($results['threats'], $embeddedCheck['threats']);
            }

            // Virus scan (if available)
            $virusCheck = $this->virusScan($file);
            if (!$virusCheck['safe']) {
                $results['safe'] = false;
                $results['threats'] = array_merge($results['threats'], $virusCheck['threats']);
            }

        } catch (\Exception $e) {
            $results['safe'] = false;
            $results['threats'][] = 'File scanning error: ' . $e->getMessage();
            log_message('error', 'File security scan failed: ' . $e->getMessage());
        }

        return $results;
    }

    /**
     * Get detailed file information
     */
    protected function getFileInfo(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        return [
            'name' => $file->getName(),
            'original_name' => $file->getClientName(),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'extension' => $file->getClientExtension(),
            'temp_name' => $file->getTempName()
        ];
    }

    /**
     * Check file extension against dangerous list
     */
    protected function checkFileExtension(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        $extension = strtolower($file->getClientExtension());
        $threats = [];

        if (in_array($extension, $this->dangerousExtensions)) {
            $threats[] = "Dangerous file extension detected: {$extension}";
        }

        // Check for double extensions (e.g., file.php.txt)
        $fullName = $file->getClientName();
        if (preg_match('/\.(' . implode('|', $this->dangerousExtensions) . ')\./i', $fullName)) {
            $threats[] = "Double extension detected in filename: {$fullName}";
        }

        return [
            'safe' => empty($threats),
            'threats' => $threats
        ];
    }

    /**
     * Check MIME type
     */
    protected function checkMimeType(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        $mimeType = $file->getMimeType();
        $extension = strtolower($file->getClientExtension());
        $threats = [];

        // Get allowed MIME types for this extension
        $allowedMimes = [];
        foreach ($this->allowedMimeTypes as $category => $mimes) {
            if (in_array($extension, $this->getExtensionsForCategory($category))) {
                $allowedMimes = array_merge($allowedMimes, $mimes);
            }
        }

        if (!empty($allowedMimes) && !in_array($mimeType, $allowedMimes)) {
            $threats[] = "MIME type mismatch: {$mimeType} not allowed for .{$extension} files";
        }

        // Check for dangerous MIME types
        $dangerousMimes = [
            'application/x-php',
            'application/x-httpd-php',
            'text/x-php',
            'application/x-sh',
            'application/x-executable'
        ];

        if (in_array($mimeType, $dangerousMimes)) {
            $threats[] = "Dangerous MIME type detected: {$mimeType}";
        }

        return [
            'safe' => empty($threats),
            'threats' => $threats
        ];
    }

    /**
     * Check file size limits
     */
    protected function checkFileSize(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        $size = $file->getSize();
        $maxSize = env('MAX_UPLOAD_SIZE', 10485760); // 10MB default
        $threats = [];

        if ($size > $maxSize) {
            $threats[] = "File size exceeds limit: {$size} bytes > {$maxSize} bytes";
        }

        if ($size === 0) {
            $threats[] = "Empty file detected";
        }

        return [
            'safe' => empty($threats),
            'threats' => $threats
        ];
    }

    /**
     * Scan file content for malicious patterns
     */
    protected function scanFileContent(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        $threats = [];

        try {
            $content = file_get_contents($file->getTempName());
            
            if ($content === false) {
                $threats[] = "Unable to read file content";
                return ['safe' => false, 'threats' => $threats];
            }

            // Check for malware signatures
            foreach ($this->malwareSignatures as $pattern) {
                if (preg_match($pattern, $content)) {
                    $threats[] = "Malicious pattern detected in file content";
                    break; // Don't reveal exact pattern to potential attacker
                }
            }

            // Check for null bytes (often used to bypass filters)
            if (strpos($content, "\0") !== false) {
                $threats[] = "Null byte detected in file content";
            }

            // Check for suspicious PHP tags in non-PHP files
            $extension = strtolower($file->getClientExtension());
            if ($extension !== 'php' && preg_match('/<\?(?:php)?\s/i', $content)) {
                $threats[] = "PHP code detected in non-PHP file";
            }

            // Check for suspicious XML/HTML content
            if (in_array($extension, ['xml', 'html', 'htm'])) {
                if (preg_match('/<!DOCTYPE[^>]*\[.*ENTITY/is', $content)) {
                    $threats[] = "XML External Entity (XXE) attack pattern detected";
                }
            }

            // Check for encoded content that might hide malicious code
            if (preg_match('/(?:eval|exec|system)\s*\(\s*(?:base64_decode|gzinflate|str_rot13)/i', $content)) {
                $threats[] = "Obfuscated malicious code detected";
            }

        } catch (\Exception $e) {
            $threats[] = "Error scanning file content: " . $e->getMessage();
        }

        return [
            'safe' => empty($threats),
            'threats' => $threats
        ];
    }

    /**
     * Check for embedded executables
     */
    protected function checkEmbeddedExecutables(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        $threats = [];

        try {
            $content = file_get_contents($file->getTempName());
            
            // Check for PE (Windows executable) header
            if (substr($content, 0, 2) === 'MZ') {
                $threats[] = "Windows executable (PE) header detected";
            }

            // Check for ELF (Linux executable) header
            if (substr($content, 0, 4) === "\x7fELF") {
                $threats[] = "Linux executable (ELF) header detected";
            }

            // Check for Mach-O (macOS executable) header
            if (in_array(substr($content, 0, 4), ["\xfe\xed\xfa\xce", "\xce\xfa\xed\xfe", "\xfe\xed\xfa\xcf", "\xcf\xfa\xed\xfe"])) {
                $threats[] = "macOS executable (Mach-O) header detected";
            }

            // Check for ZIP/Archive headers that might contain executables
            if (substr($content, 0, 4) === "PK\x03\x04") {
                $threats[] = "ZIP archive detected - potential executable container";
            }

        } catch (\Exception $e) {
            log_message('error', 'Error checking for embedded executables: ' . $e->getMessage());
        }

        return [
            'safe' => empty($threats),
            'threats' => $threats
        ];
    }

    /**
     * Virus scan (basic implementation - could integrate with ClamAV)
     */
    protected function virusScan(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        $threats = [];

        // Check if ClamAV is available
        if ($this->isClamAVAvailable()) {
            try {
                $result = $this->scanWithClamAV($file->getTempName());
                if (!$result['clean']) {
                    $threats[] = "Virus detected: " . $result['virus_name'];
                }
            } catch (\Exception $e) {
                log_message('error', 'ClamAV scan failed: ' . $e->getMessage());
            }
        }

        // Basic hash-based detection (for known malware samples)
        $fileHash = hash_file('sha256', $file->getTempName());
        if ($this->isKnownMalwareHash($fileHash)) {
            $threats[] = "Known malware hash detected";
        }

        return [
            'safe' => empty($threats),
            'threats' => $threats
        ];
    }

    /**
     * Check if ClamAV is available
     */
    protected function isClamAVAvailable(): bool
    {
        return file_exists('/usr/bin/clamscan') || file_exists('/usr/local/bin/clamscan');
    }

    /**
     * Scan file with ClamAV
     */
    protected function scanWithClamAV(string $filePath): array
    {
        $clamavPath = file_exists('/usr/bin/clamscan') ? '/usr/bin/clamscan' : '/usr/local/bin/clamscan';
        $command = escapeshellcmd($clamavPath) . ' --no-summary --infected ' . escapeshellarg($filePath);
        
        $output = [];
        $returnCode = 0;
        
        exec($command, $output, $returnCode);
        
        return [
            'clean' => $returnCode === 0,
            'virus_name' => $returnCode === 1 ? implode(' ', $output) : null
        ];
    }

    /**
     * Check against known malware hashes
     */
    protected function isKnownMalwareHash(string $hash): bool
    {
        // This would typically check against a database of known malware hashes
        // For demo purposes, we'll use a simple array
        $knownMalwareHashes = [
            // Add known malware SHA256 hashes here
        ];

        return in_array($hash, $knownMalwareHashes);
    }

    /**
     * Get file extensions for MIME category
     */
    protected function getExtensionsForCategory(string $category): array
    {
        $extensions = [
            'xml' => ['xml'],
            'json' => ['json'],
            'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp']
        ];

        return $extensions[$category] ?? [];
    }

    /**
     * Quarantine suspicious file
     */
    public function quarantineFile(\CodeIgniter\HTTP\Files\UploadedFile $file, array $threats): bool
    {
        try {
            $quarantineDir = WRITEPATH . 'quarantine/';
            
            if (!is_dir($quarantineDir)) {
                mkdir($quarantineDir, 0755, true);
            }

            $quarantineFile = $quarantineDir . date('Y-m-d_H-i-s') . '_' . $file->getClientName();
            
            if (move_uploaded_file($file->getTempName(), $quarantineFile)) {
                // Log quarantine event
                log_message('warning', 'File quarantined: ' . $quarantineFile . ' Threats: ' . implode(', ', $threats));
                
                // Create quarantine metadata
                file_put_contents($quarantineFile . '.meta', json_encode([
                    'original_name' => $file->getClientName(),
                    'quarantine_time' => date('Y-m-d H:i:s'),
                    'threats' => $threats,
                    'file_info' => $this->getFileInfo($file)
                ]));

                return true;
            }

        } catch (\Exception $e) {
            log_message('error', 'Failed to quarantine file: ' . $e->getMessage());
        }

        return false;
    }

    /**
     * Clean and prepare file for safe use
     */
    public function sanitizeFile(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        $result = [
            'success' => false,
            'sanitized_path' => null,
            'modifications' => []
        ];

        try {
            $content = file_get_contents($file->getTempName());
            $originalContent = $content;
            
            // Remove potentially dangerous content
            $content = $this->removeDangerousContent($content);
            
            // Generate safe filename
            $safeFilename = $this->generateSafeFilename($file->getClientName());
            $sanitizedPath = WRITEPATH . 'uploads/' . $safeFilename;
            
            // Ensure upload directory exists
            $uploadDir = dirname($sanitizedPath);
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Save sanitized content
            if (file_put_contents($sanitizedPath, $content) !== false) {
                $result['success'] = true;
                $result['sanitized_path'] = $sanitizedPath;
                
                if ($content !== $originalContent) {
                    $result['modifications'][] = 'Dangerous content removed';
                }
            }

        } catch (\Exception $e) {
            log_message('error', 'File sanitization failed: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * Remove dangerous content from file
     */
    protected function removeDangerousContent(string $content): string
    {
        // Remove PHP tags
        $content = preg_replace('/<\?(?:php)?\s.*?\?>/is', '', $content);
        
        // Remove script tags
        $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi', '', $content);
        
        // Remove dangerous HTML attributes
        $dangerousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus'];
        foreach ($dangerousAttrs as $attr) {
            $content = preg_replace('/' . $attr . '\s*=\s*["\'][^"\']*["\']/i', '', $content);
        }
        
        // Remove null bytes
        $content = str_replace("\0", '', $content);
        
        return $content;
    }

    /**
     * Generate safe filename
     */
    protected function generateSafeFilename(string $originalName): string
    {
        // Remove directory separators and dangerous characters
        $filename = basename($originalName);
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
        
        // Add timestamp to prevent conflicts
        $pathInfo = pathinfo($filename);
        $name = $pathInfo['filename'] ?? 'file';
        $extension = $pathInfo['extension'] ?? 'txt';
        
        return $name . '_' . date('YmdHis') . '.' . $extension;
    }
}