<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateKoboMediaFilesTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'submission_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'file_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'original_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'file_path' => [
                'type' => 'TEXT',
            ],
            'file_size' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'mime_type' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'file_type' => [
                'type' => 'ENUM',
                'constraint' => ['image', 'audio', 'video', 'document'],
            ],
            'uploaded_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
        ]);
        
        $this->forge->addPrimaryKey('id');
        $this->forge->addKey('submission_id');
        $this->forge->addKey('file_type');
        $this->forge->addForeignKey('submission_id', 'kobo_submissions', 'submission_id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('kobo_media_files');
    }

    public function down()
    {
        $this->forge->dropTable('kobo_media_files');
    }
}
