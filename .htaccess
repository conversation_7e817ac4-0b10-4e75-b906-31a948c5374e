# Disable directory browsing
Options -Indexes

# ----------------------------------------------------------------------
# Rewrite engine
# ----------------------------------------------------------------------

<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle Angular and other front-end routes
    # This section will redirect all requests to the public folder
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ public/$1 [L]
    
    # If the request is for a directory, append index.php
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^(.*)$ public/$1/index.php [L]
</IfModule>

<IfModule !mod_rewrite.c>
    # If we don't have mod_rewrite installed, all 404's
    # can be sent to public/index.php
    ErrorDocument 404 /coditests/public/index.php
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    # Prevent access to sensitive files
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Deny access to sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.json">
    Order allow,deny  
    Deny from all
</Files>

<Files "composer.lock">
    Order allow,deny
    Deny from all
</Files>

# Deny access to directories that shouldn't be publicly accessible
RedirectMatch 403 ^/coditests/(app|tests|writable)/.*$