<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use App\Models\Kobo\KoboUserModel;
use App\Models\Kobo\KoboAuditLogModel;

/**
 * JWT Authentication Filter
 * 
 * Validates JWT tokens for API authentication and sets user context
 */
class JWTAuthFilter implements FilterInterface
{
    protected $userModel;
    protected $auditModel;
    protected $jwtSecret;
    protected $jwtAlgorithm = 'HS256';
    
    public function __construct()
    {
        $this->userModel = new KoboUserModel();
        $this->auditModel = new KoboAuditLogModel();
        $this->jwtSecret = env('JWT_SECRET', 'kobo-collect-secret-key-2024');
    }

    /**
     * Before method - validates JW<PERSON> token
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Skip authentication for certain routes
        $skipRoutes = [
            'api/v1/auth/login',
            'api/v1/auth/register',
            'api/v1/auth/refresh'
        ];

        $uri = $request->getUri();
        $path = trim($uri->getPath(), '/');
        
        if (in_array($path, $skipRoutes)) {
            return;
        }

        try {
            // Extract token from Authorization header or query parameter
            $token = $this->extractToken($request);
            
            if (!$token) {
                return $this->respondUnauthorized('Missing authentication token');
            }

            // Decode and validate token
            $decodedToken = $this->validateToken($token);
            
            if (!$decodedToken) {
                return $this->respondUnauthorized('Invalid authentication token');
            }

            // Load user data and set context
            $user = $this->loadUser($decodedToken->sub);
            
            if (!$user || $user['status'] !== 'active') {
                return $this->respondUnauthorized('User account not found or inactive');
            }

            // Set user context in request
            $request->user = $user;
            $request->user_id = $user['id'];
            $request->user_role = $user['role'];

            // Log authentication event
            $this->logAuthEvent($user['id'], 'api_access', $request);

            // Validate user permissions for the requested resource
            if ($arguments && !$this->validatePermissions($user, $arguments, $request)) {
                return $this->respondForbidden('Insufficient permissions');
            }

        } catch (ExpiredException $e) {
            return $this->respondUnauthorized('Token has expired');
        } catch (SignatureInvalidException $e) {
            return $this->respondUnauthorized('Token signature invalid');
        } catch (\Exception $e) {
            log_message('error', 'JWT Authentication Error: ' . $e->getMessage());
            return $this->respondUnauthorized('Authentication failed');
        }
    }

    /**
     * After method - cleanup if needed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Log API response if needed
        if (isset($request->user_id)) {
            $this->logApiResponse($request, $response);
        }
    }

    /**
     * Extract JWT token from request
     */
    protected function extractToken(RequestInterface $request): ?string
    {
        // Check Authorization header first (Bearer token)
        $authHeader = $request->getHeaderLine('Authorization');
        if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return $matches[1];
        }

        // Check query parameter as fallback
        $token = $request->getGet('token');
        if ($token) {
            return $token;
        }

        // Check POST parameter for form submissions
        $token = $request->getPost('token');
        if ($token) {
            return $token;
        }

        return null;
    }

    /**
     * Validate JWT token and return decoded payload
     */
    protected function validateToken(string $token): ?\stdClass
    {
        try {
            $decoded = JWT::decode($token, new Key($this->jwtSecret, $this->jwtAlgorithm));
            
            // Validate token structure
            if (!isset($decoded->sub, $decoded->iat, $decoded->exp)) {
                throw new \Exception('Invalid token structure');
            }

            // Check if token is not expired
            if ($decoded->exp < time()) {
                throw new ExpiredException('Token expired');
            }

            // Validate issuer if set
            if (isset($decoded->iss) && $decoded->iss !== base_url()) {
                throw new \Exception('Invalid token issuer');
            }

            return $decoded;

        } catch (\Exception $e) {
            log_message('error', 'Token validation failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Load user data from database
     */
    protected function loadUser(int $userId): ?array
    {
        try {
            return $this->userModel->find($userId);
        } catch (\Exception $e) {
            log_message('error', 'Failed to load user: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Validate user permissions for requested resource
     */
    protected function validatePermissions(array $user, array $permissions, RequestInterface $request): bool
    {
        // Admin users have all permissions
        if ($user['role'] === 'admin') {
            return true;
        }

        // Check specific permissions based on the endpoint
        $uri = $request->getUri();
        $path = trim($uri->getPath(), '/');
        $method = strtoupper($request->getMethod());

        // Define permission rules
        $permissionRules = [
            'api/v1/forms' => [
                'GET' => ['admin', 'enumerator', 'viewer'],
                'POST' => ['admin'],
                'PUT' => ['admin'],
                'DELETE' => ['admin']
            ],
            'api/v1/submission' => [
                'POST' => ['admin', 'enumerator'],
                'GET' => ['admin', 'enumerator', 'viewer']
            ]
        ];

        // Extract base path for permission checking
        $basePath = $this->extractBasePath($path);
        
        if (isset($permissionRules[$basePath][$method])) {
            $allowedRoles = $permissionRules[$basePath][$method];
            return in_array($user['role'], $allowedRoles);
        }

        // Default to allowing if no specific rule found (can be changed for stricter security)
        return true;
    }

    /**
     * Extract base path from full path for permission checking
     */
    protected function extractBasePath(string $path): string
    {
        $parts = explode('/', $path);
        
        // For API paths like api/v1/forms/123, return api/v1/forms
        if (count($parts) >= 3 && $parts[0] === 'api' && $parts[1] === 'v1') {
            return implode('/', array_slice($parts, 0, 3));
        }
        
        return $path;
    }

    /**
     * Log authentication events
     */
    protected function logAuthEvent(int $userId, string $action, RequestInterface $request): void
    {
        try {
            $this->auditModel->logActivity([
                'user_id' => $userId,
                'action' => $action,
                'resource_type' => 'authentication',
                'details' => json_encode([
                    'ip_address' => $request->getIPAddress(),
                    'user_agent' => $request->getUserAgent(),
                    'endpoint' => $request->getUri()->getPath(),
                    'method' => $request->getMethod()
                ])
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Failed to log auth event: ' . $e->getMessage());
        }
    }

    /**
     * Log API responses for audit purposes
     */
    protected function logApiResponse(RequestInterface $request, ResponseInterface $response): void
    {
        // Only log certain response codes or sensitive endpoints
        $statusCode = $response->getStatusCode();
        
        if ($statusCode >= 400 || $this->isSensitiveEndpoint($request)) {
            try {
                $this->auditModel->logActivity([
                    'user_id' => $request->user_id,
                    'action' => 'api_response',
                    'resource_type' => 'api',
                    'details' => json_encode([
                        'endpoint' => $request->getUri()->getPath(),
                        'method' => $request->getMethod(),
                        'status_code' => $statusCode,
                        'ip_address' => $request->getIPAddress()
                    ])
                ]);
            } catch (\Exception $e) {
                log_message('error', 'Failed to log API response: ' . $e->getMessage());
            }
        }
    }

    /**
     * Check if endpoint is sensitive and should be logged
     */
    protected function isSensitiveEndpoint(RequestInterface $request): bool
    {
        $sensitivePatterns = [
            '/api\/v1\/submission/',
            '/api\/v1\/forms\/.*\/delete/',
            '/api\/v1\/auth\/.*/'
        ];

        $path = $request->getUri()->getPath();
        
        foreach ($sensitivePatterns as $pattern) {
            if (preg_match($pattern, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Return 401 Unauthorized response
     */
    protected function respondUnauthorized(string $message = 'Unauthorized'): ResponseInterface
    {
        $response = service('response');
        return $response->setStatusCode(401)
                       ->setJSON([
                           'success' => false,
                           'message' => $message,
                           'error_code' => 'UNAUTHORIZED'
                       ]);
    }

    /**
     * Return 403 Forbidden response
     */
    protected function respondForbidden(string $message = 'Forbidden'): ResponseInterface
    {
        $response = service('response');
        return $response->setStatusCode(403)
                       ->setJSON([
                           'success' => false,
                           'message' => $message,
                           'error_code' => 'FORBIDDEN'
                       ]);
    }
}