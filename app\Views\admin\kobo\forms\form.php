<?= $this->extend('admin/layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2 fw-bold">
        <i class="bi bi-plus-circle text-primary me-2"></i>
        <?= isset($form) ? 'Edit Form' : 'Upload New Form' ?>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="<?= base_url('admin/kobo/forms') ?>" class="btn btn-sm btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i>
            Back to Forms
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Form Upload Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-file-earmark-text me-2"></i>
                    <?= isset($form) ? 'Form Details' : 'Form Upload' ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="post" action="<?= isset($form) ? base_url('admin/kobo/forms/'.$form['id']) : base_url('admin/kobo/forms') ?>" enctype="multipart/form-data">
                    <?= csrf_field() ?>
                    <?php if (isset($form)): ?>
                        <input type="hidden" name="form_id" value="<?= $form['id'] ?>">
                    <?php endif; ?>

                    <div class="row g-3">
                        <div class="col-md-8">
                            <label for="form_name" class="form-label">Form Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="form_name" name="form_name"
                                   value="<?= $form['form_name'] ?? '' ?>" required>
                        </div>

                        <div class="col-md-4">
                            <label for="form_status" class="form-label">Status</label>
                            <select class="form-select" id="form_status" name="status">
                                <option value="draft" <?= ($form['status'] ?? 'draft') === 'draft' ? 'selected' : '' ?>>Draft</option>
                                <option value="active" <?= ($form['status'] ?? '') === 'active' ? 'selected' : '' ?>>Active</option>
                                <option value="inactive" <?= ($form['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>

                        <?php if (!isset($form)): ?>
                        <div class="col-12">
                            <label for="xml_file" class="form-label">XForm XML File <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="xml_file" name="xml_file"
                                   accept=".xml" required>
                            <div class="form-text">
                                Upload an XForm XML file (.xml)
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (isset($form)): ?>
                        <div class="col-12">
                            <label for="xml_content" class="form-label">XML Content</label>
                            <textarea class="form-control" id="xml_content" name="xml_content" rows="10" 
                                      style="font-family: 'Courier New', monospace; font-size: 12px;"><?= esc($form['xml_content'] ?? '') ?></textarea>
                            <div class="form-text">
                                You can edit the XML content directly. Changes will be validated before saving.
                            </div>
                            <div class="invalid-feedback" id="xml_content_error"></div>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary me-2" id="submitBtn">
                                <i class="bi bi-save me-1"></i>
                                <?= isset($form) ? 'Update Form' : 'Upload Form' ?>
                            </button>
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                Reset
                            </button>
                            <?php if (isset($form)): ?>
                            <button type="button" class="btn btn-outline-info me-2" onclick="previewForm()">
                                <i class="bi bi-eye me-1"></i>
                                Preview
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="validateXML()">
                                <i class="bi bi-check-circle me-1"></i>
                                Validate XML
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Upload Guidelines -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Upload Guidelines
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check text-success me-2"></i>
                        <strong>File Format:</strong> XML files only (.xml)
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check text-success me-2"></i>
                        <strong>Max Size:</strong> 10MB per file
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check text-success me-2"></i>
                        <strong>Structure:</strong> Valid XForm format
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check text-success me-2"></i>
                        <strong>Encoding:</strong> UTF-8 recommended
                    </li>
                    <li class="mb-0">
                        <i class="bi bi-check text-success me-2"></i>
                        <strong>Validation:</strong> Automatic XML validation
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Form Tools -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-tools me-2"></i>
                    Form Tools
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="https://xlsform.org/" target="_blank" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-box-arrow-up-right me-2"></i>
                        XLSForm Online
                    </a>
                    <a href="https://build.getodk.org/" target="_blank" class="btn btn-outline-info btn-sm">
                        <i class="bi bi-box-arrow-up-right me-2"></i>
                        ODK Build
                    </a>
                    <button class="btn btn-outline-success btn-sm" onclick="downloadSample()">
                        <i class="bi bi-download me-2"></i>
                        Sample Form
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="showXMLEditor()">
                        <i class="bi bi-code me-2"></i>
                        XML Editor
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Recent Forms -->
        <?php if (!empty($recent_forms)): ?>
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    Recent Forms
                </h6>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <?php foreach (array_slice($recent_forms, 0, 5) as $recent): ?>
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1"><?= esc($recent['form_name']) ?></h6>
                                <small><?= date('M j', strtotime($recent['created_at'])) ?></small>
                            </div>
                            <p class="mb-1 small text-muted">
                                ID: <?= esc($recent['form_id']) ?>
                            </p>
                            <small class="text-muted">
                                <span class="badge bg-<?= $recent['status'] === 'active' ? 'success' : 'secondary' ?> me-1">
                                    <?= ucfirst($recent['status']) ?>
                                </span>
                                v<?= $recent['version'] ?>
                            </small>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- XML Editor Modal -->
<div class="modal fade" id="xmlEditorModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-code me-2"></i>
                    XML Editor
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <textarea id="xmlEditor" class="form-control" rows="20" 
                              style="font-family: 'Courier New', monospace; font-size: 12px;"
                              placeholder="Paste your XForm XML content here..."></textarea>
                </div>
                <div class="d-flex justify-content-between">
                    <div>
                        <button type="button" class="btn btn-outline-success me-2" onclick="validateEditorXML()">
                            <i class="bi bi-check-circle me-1"></i>
                            Validate
                        </button>
                        <button type="button" class="btn btn-outline-info me-2" onclick="formatXML()">
                            <i class="bi bi-code me-1"></i>
                            Format
                        </button>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-secondary me-2" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="useEditorXML()">
                            <i class="bi bi-check me-1"></i>
                            Use This XML
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        initializeFormUpload();
        initializeFileValidation();
    });

    function initializeFormUpload() {
        const form = document.getElementById('formUploadForm');
        form.addEventListener('submit', handleFormSubmit);
    }

    function initializeFileValidation() {
        const fileInput = document.getElementById('xml_file');
        if (fileInput) {
            fileInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    // Validate file type
                    if (!file.name.toLowerCase().endsWith('.xml')) {
                        showToast('Please select an XML file', 'warning');
                        this.value = '';
                        return;
                    }
                    
                    // Validate file size (10MB)
                    if (file.size > 10 * 1024 * 1024) {
                        showToast('File size must be less than 10MB', 'warning');
                        this.value = '';
                        return;
                    }
                    
                    // Preview file content
                    previewXMLFile(file);
                }
            });
        }
    }

    function previewXMLFile(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // Basic XML validation
            try {
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(e.target.result, "text/xml");
                
                if (xmlDoc.getElementsByTagName("parsererror").length > 0) {
                    showToast('Invalid XML file', 'danger');
                    return;
                }
                
                // Extract form name from XML if possible
                const titleElement = xmlDoc.querySelector('title');
                if (titleElement && !document.getElementById('form_name').value) {
                    document.getElementById('form_name').value = titleElement.textContent;
                }
                
                showToast('XML file validated successfully', 'success');
            } catch (error) {
                showToast('Error reading XML file', 'danger');
            }
        };
        reader.readAsText(file);
    }

    function handleFormSubmit(e) {
        e.preventDefault();
        
        const submitBtn = document.getElementById('submitBtn');
        const originalText = showLoading(submitBtn);
        
        const formData = new FormData(e.target);
        const url = <?= isset($form) ? "'".base_url('admin/kobo/forms/'.$form['id'])."'" : "'".base_url('admin/kobo/forms')."'" ?>;
        const method = <?= isset($form) ? "'PUT'" : "'POST'" ?>;
        
        fetch(url, {
            method: method,
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoading(submitBtn, originalText);
            
            if (data.success) {
                showToast(data.message || 'Form saved successfully', 'success');
                setTimeout(() => {
                    window.location.href = '<?= base_url('admin/kobo/forms') ?>';
                }, 1000);
            } else {
                showFormErrors(data.errors || {});
                showToast(data.message || 'Form submission failed', 'danger');
            }
        })
        .catch(error => {
            hideLoading(submitBtn, originalText);
            console.error('Error:', error);
            showToast('An error occurred while saving the form', 'danger');
        });
    }

    function showFormErrors(errors) {
        // Clear previous errors
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
        
        // Show new errors
        Object.keys(errors).forEach(field => {
            const input = document.getElementById(field) || document.querySelector(`[name="${field}"]`);
            const errorDiv = document.getElementById(field + '_error');
            
            if (input) {
                input.classList.add('is-invalid');
            }
            
            if (errorDiv) {
                errorDiv.textContent = Array.isArray(errors[field]) ? errors[field][0] : errors[field];
            }
        });
    }

    function resetForm() {
        if (confirm('Are you sure you want to reset the form? All changes will be lost.')) {
            document.getElementById('formUploadForm').reset();
            document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
            document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
        }
    }

    function previewForm() {
        <?php if (isset($form)): ?>
        window.open('<?= base_url('admin/kobo/forms/'.$form['id'].'/preview') ?>', '_blank');
        <?php endif; ?>
    }

    function validateXML() {
        const xmlContent = document.getElementById('xml_content').value;
        
        if (!xmlContent.trim()) {
            showToast('No XML content to validate', 'warning');
            return;
        }
        
        ajaxRequest('<?= base_url('admin/kobo/forms/validate-xml') ?>', 'POST', {
            xml_content: xmlContent
        },
        function(data) {
            if (data.valid) {
                showToast('XML is valid!', 'success');
            } else {
                showToast('XML validation failed: ' + (data.errors?.join(', ') || 'Unknown error'), 'danger');
            }
        },
        function(error) {
            showToast('Validation request failed', 'danger');
        });
    }

    function showXMLEditor() {
        const modal = new bootstrap.Modal(document.getElementById('xmlEditorModal'));
        const xmlContent = document.getElementById('xml_content');
        const editor = document.getElementById('xmlEditor');
        
        if (xmlContent) {
            editor.value = xmlContent.value;
        }
        
        modal.show();
    }

    function validateEditorXML() {
        const xmlContent = document.getElementById('xmlEditor').value;
        
        try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xmlContent, "text/xml");
            
            if (xmlDoc.getElementsByTagName("parsererror").length > 0) {
                showToast('Invalid XML syntax', 'danger');
            } else {
                showToast('XML syntax is valid', 'success');
            }
        } catch (error) {
            showToast('XML parsing error', 'danger');
        }
    }

    function formatXML() {
        const editor = document.getElementById('xmlEditor');
        const xml = editor.value;
        
        try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xml, "text/xml");
            const serializer = new XMLSerializer();
            const formatted = formatXMLString(serializer.serializeToString(xmlDoc));
            editor.value = formatted;
            showToast('XML formatted', 'success');
        } catch (error) {
            showToast('Error formatting XML', 'danger');
        }
    }

    function formatXMLString(xml) {
        const formatted = [];
        const regex = /(>)(<)(\/*)/g;
        xml = xml.replace(regex, '$1\r\n$2$3');
        let pad = 0;
        
        xml.split('\r\n').forEach(line => {
            let indent = 0;
            if (line.match(/.+<\/\w[^>]*>$/)) {
                indent = 0;
            } else if (line.match(/^<\/\w/)) {
                if (pad !== 0) {
                    pad -= 1;
                }
            } else if (line.match(/^<\w[^>]*[^\/]>.*$/)) {
                indent = 1;
            }
            
            const padding = '  '.repeat(pad);
            formatted.push(padding + line);
            pad += indent;
        });
        
        return formatted.join('\r\n');
    }

    function useEditorXML() {
        const editorContent = document.getElementById('xmlEditor').value;
        const xmlContentField = document.getElementById('xml_content');
        
        if (xmlContentField) {
            xmlContentField.value = editorContent;
            showToast('XML content updated', 'success');
        }
        
        bootstrap.Modal.getInstance(document.getElementById('xmlEditorModal')).hide();
    }

    function downloadSample() {
        window.open('<?= base_url('admin/kobo/forms/sample') ?>', '_blank');
    }
</script>
<?= $this->endSection() ?>