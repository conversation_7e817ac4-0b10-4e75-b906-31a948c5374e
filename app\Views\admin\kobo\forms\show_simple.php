<?= $this->extend('admin/layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2 fw-bold">
        <i class="bi bi-file-earmark-text text-primary me-2"></i>
        <?= esc($form['form_name']) ?>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="<?= base_url('admin/kobo/forms/'.$form['id'].'/edit') ?>" class="btn btn-sm btn-primary me-2">
            <i class="bi bi-pencil me-1"></i>
            Edit Form
        </a>
        <a href="<?= base_url('admin/kobo/forms') ?>" class="btn btn-sm btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i>
            Back to Forms
        </a>
    </div>
</div>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?= session()->getFlashdata('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Form Details
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Form ID:</label>
                        <p class="form-control-plaintext"><?= esc($form['form_id']) ?></p>
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Status:</label>
                        <p class="form-control-plaintext">
                            <span class="badge bg-<?= $form['status'] === 'active' ? 'success' : ($form['status'] === 'draft' ? 'warning' : 'secondary') ?>">
                                <?= ucfirst($form['status']) ?>
                            </span>
                        </p>
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Version:</label>
                        <p class="form-control-plaintext"><?= esc($form['version']) ?></p>
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Created:</label>
                        <p class="form-control-plaintext"><?= date('Y-m-d H:i:s', strtotime($form['created_at'])) ?></p>
                    </div>
                    
                    <div class="col-12">
                        <label class="form-label fw-bold">XML Content:</label>
                        <textarea class="form-control" rows="15" readonly style="font-family: monospace; font-size: 0.85em;"><?= esc($form['xml_content']) ?></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-gear me-1"></i>
                    Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= base_url('admin/kobo/forms/'.$form['id'].'/edit') ?>" class="btn btn-primary">
                        <i class="bi bi-pencil me-1"></i>
                        Edit Form
                    </a>
                    
                    <button type="button" class="btn btn-outline-danger" onclick="confirmDelete()">
                        <i class="bi bi-trash me-1"></i>
                        Delete Form
                    </button>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle me-1"></i>
                    Form Information
                </h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <strong>Form ID:</strong> <?= esc($form['form_id']) ?><br>
                    <strong>Version:</strong> <?= esc($form['version']) ?><br>
                    <strong>Status:</strong> <?= ucfirst($form['status']) ?><br>
                    <strong>Created:</strong> <?= date('M j, Y', strtotime($form['created_at'])) ?><br>
                    <?php if ($form['updated_at']): ?>
                    <strong>Updated:</strong> <?= date('M j, Y', strtotime($form['updated_at'])) ?>
                    <?php endif; ?>
                </small>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    function confirmDelete() {
        if (confirm('Are you sure you want to delete this form? This action cannot be undone.')) {
            // Create a form to submit DELETE request
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '<?= base_url('admin/kobo/forms/'.$form['id']) ?>';
            
            // Add CSRF token
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '<?= csrf_token() ?>';
            csrfInput.value = '<?= csrf_hash() ?>';
            form.appendChild(csrfInput);
            
            // Add method override for DELETE
            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';
            form.appendChild(methodInput);
            
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
<?= $this->endSection() ?>
