<?xml version="1.0" encoding="UTF-8"?>
<h:html xmlns="http://www.w3.org/2002/xforms"
        xmlns:h="http://www.w3.org/1999/xhtml"
        xmlns:jr="http://openrosa.org/javarosa"
        xmlns:orx="http://openrosa.org/xforms"
        xmlns:odk="http://www.opendatakit.org/xforms">
  <h:head>
    <h:title>Simple Registration Form</h:title>
    <model>
      <!-- Instance: holds the form data -->
      <instance>
        <registration id="simple-form" version="2024083101">
          <start/>
          <end/>
          <today/>
          <deviceid/>
          <subscriberid/>
          <simid/>
          <phonenumber/>
          <username/>
          <duration/>
          <caseid/>
          <instanceID/>
          <instanceName/>
          <name/>
          <age/>
          <dobirth/>
          <address/>
          <meta>
            <instanceID/>
            <timeStart/>
            <timeEnd/>
            <userID/>
            <deviceID/>
            <deprecatedID/>
          </meta>
        </registration>
      </instance>
      
      <!-- Bindings: define data types and constraints -->
      <bind nodeset="/registration/start" type="dateTime" jr:preload="timestamp" jr:preloadParams="start"/>
      <bind nodeset="/registration/end" type="dateTime" jr:preload="timestamp" jr:preloadParams="end"/>
      <bind nodeset="/registration/today" type="date" jr:preload="date" jr:preloadParams="today"/>
      <bind nodeset="/registration/deviceid" type="string" jr:preload="property" jr:preloadParams="deviceid"/>
      <bind nodeset="/registration/subscriberid" type="string" jr:preload="property" jr:preloadParams="subscriberid"/>
      <bind nodeset="/registration/simid" type="string" jr:preload="property" jr:preloadParams="simid"/>
      <bind nodeset="/registration/phonenumber" type="string" jr:preload="property" jr:preloadParams="phonenumber"/>
      <bind nodeset="/registration/username" type="string" jr:preload="property" jr:preloadParams="username"/>
      <bind nodeset="/registration/duration" type="string"/>
      <bind nodeset="/registration/caseid" type="string"/>
      <bind nodeset="/registration/instanceID" type="string" jr:preload="uid"/>
      <bind nodeset="/registration/instanceName" type="string" calculate="concat('Registration: ', /registration/name)"/>
      
      <!-- User input bindings -->
      <bind nodeset="/registration/name" type="string" required="true()"/>
      <bind nodeset="/registration/age" type="int" constraint=". &gt;= 0 and . &lt;= 120" 
            jr:constraintMsg="Age must be between 0 and 120" required="true()"/>
      <bind nodeset="/registration/dobirth" type="date" required="true()" 
            constraint=". &lt;= today()" jr:constraintMsg="Date of birth cannot be in the future"/>
      <bind nodeset="/registration/address" type="string"/>
      
      <!-- Meta bindings -->
      <bind nodeset="/registration/meta/instanceID" type="string" calculate="../instanceID"/>
      <bind nodeset="/registration/meta/timeStart" type="dateTime" jr:preload="timestamp" jr:preloadParams="start"/>
      <bind nodeset="/registration/meta/timeEnd" type="dateTime" jr:preload="timestamp" jr:preloadParams="end"/>
      <bind nodeset="/registration/meta/userID" type="string" jr:preload="property" jr:preloadParams="username"/>
      <bind nodeset="/registration/meta/deviceID" type="string" jr:preload="property" jr:preloadParams="deviceid"/>
      <bind nodeset="/registration/meta/deprecatedID" type="string" calculate="../instanceID"/>
    </model>
  </h:head>
  
  <h:body>
    <!-- Form Questions -->
    <group>
      <label>Personal Information</label>
      
      <input ref="/registration/name">
        <label>What is your full name?</label>
        <hint>Enter your complete name (first and last name)</hint>
      </input>
      
      <input ref="/registration/age">
        <label>What is your age?</label>
        <hint>Enter your age in years</hint>
      </input>
      
      <input ref="/registration/dobirth">
        <label>What is your date of birth?</label>
        <hint>Select your birth date from the calendar</hint>
      </input>
      
      <input ref="/registration/address">
        <label>What is your address?</label>
        <hint>Enter your complete address (optional)</hint>
      </input>
    </group>
  </h:body>
</h:html>