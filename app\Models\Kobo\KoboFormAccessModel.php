<?php

namespace App\Models\Kobo;

use CodeIgniter\Model;

/**
 * Kobo Form Access Model
 * 
 * Handles access control management for forms
 * Provides permission checking and device-based restrictions
 */
class KoboFormAccessModel extends Model
{
    protected $table = 'kobo_form_access';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id',
        'form_id',
        'device_id',
        'access_type',
        'granted_by',
        'granted_at',
        'expires_at',
        'status',
        'created_at',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'user_id' => 'permit_empty|integer',
        'form_id' => 'required|string|max_length[255]',
        'device_id' => 'permit_empty|string|max_length[255]',
        'access_type' => 'required|in_list[read,write,full]',
        'granted_by' => 'required|integer',
        'status' => 'required|in_list[active,revoked,expired]'
    ];

    protected $validationMessages = [
        'user_id' => [
            'integer' => 'User ID must be a valid integer'
        ],
        'form_id' => [
            'required' => 'Form ID is required',
            'string' => 'Form ID must be a valid string',
            'max_length' => 'Form ID cannot exceed 255 characters'
        ],
        'device_id' => [
            'string' => 'Device ID must be a valid string',
            'max_length' => 'Device ID cannot exceed 255 characters'
        ],
        'access_type' => [
            'required' => 'Access type is required',
            'in_list' => 'Access type must be read, write, or full'
        ],
        'granted_by' => [
            'required' => 'Granted by user ID is required',
            'integer' => 'Granted by must be a valid user ID'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be active, revoked, or expired'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['setDefaultValues'];

    /**
     * Set default values before inserting
     */
    protected function setDefaultValues(array $data)
    {
        if (!isset($data['data']['granted_at'])) {
            $data['data']['granted_at'] = date('Y-m-d H:i:s');
        }

        if (!isset($data['data']['status'])) {
            $data['data']['status'] = 'active';
        }

        return $data;
    }

    /**
     * Grant access to a form
     */
    public function grantAccess(array $accessData): int|false
    {
        // Check if access already exists
        $existingAccess = $this->checkExistingAccess($accessData);
        
        if ($existingAccess) {
            // Update existing access
            return $this->update($existingAccess['id'], [
                'access_type' => $accessData['access_type'],
                'expires_at' => $accessData['expires_at'] ?? null,
                'status' => 'active',
                'granted_by' => $accessData['granted_by'],
                'granted_at' => date('Y-m-d H:i:s')
            ]) ? $existingAccess['id'] : false;
        }

        return $this->insert($accessData);
    }

    /**
     * Check if user has access to form
     */
    public function hasAccess(int $userId = null, string $formId, string $deviceId = null, string $accessType = 'read'): bool
    {
        $builder = $this->where('form_id', $formId)
                        ->where('status', 'active');

        // Check expiration
        $builder->groupStart()
                ->where('expires_at IS NULL')
                ->orWhere('expires_at >', date('Y-m-d H:i:s'))
                ->groupEnd();

        // User-based access
        if ($userId) {
            $builder->groupStart()
                    ->where('user_id', $userId);

            // Device-specific access for the user
            if ($deviceId) {
                $builder->orWhere('device_id', $deviceId);
            }

            $builder->groupEnd();
        }
        // Device-only access
        elseif ($deviceId) {
            $builder->where('device_id', $deviceId)
                    ->where('user_id IS NULL');
        }
        else {
            return false; // No user or device specified
        }

        $access = $builder->first();

        if (!$access) {
            return false;
        }

        // Check access type level
        return $this->hasAccessLevel($access['access_type'], $accessType);
    }

    /**
     * Check if access type has sufficient permissions
     */
    private function hasAccessLevel(string $grantedAccess, string $requiredAccess): bool
    {
        $accessLevels = [
            'read' => 1,
            'write' => 2,
            'full' => 3
        ];

        $grantedLevel = $accessLevels[$grantedAccess] ?? 0;
        $requiredLevel = $accessLevels[$requiredAccess] ?? 0;

        return $grantedLevel >= $requiredLevel;
    }

    /**
     * Revoke access to a form
     */
    public function revokeAccess(int $accessId): bool
    {
        return $this->update($accessId, ['status' => 'revoked']);
    }

    /**
     * Revoke user access to form
     */
    public function revokeUserAccess(int $userId, string $formId): bool
    {
        $accesses = $this->where('user_id', $userId)
                         ->where('form_id', $formId)
                         ->where('status', 'active')
                         ->findAll();

        $success = true;
        foreach ($accesses as $access) {
            if (!$this->revokeAccess($access['id'])) {
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Revoke device access to form
     */
    public function revokeDeviceAccess(string $deviceId, string $formId): bool
    {
        $accesses = $this->where('device_id', $deviceId)
                         ->where('form_id', $formId)
                         ->where('status', 'active')
                         ->findAll();

        $success = true;
        foreach ($accesses as $access) {
            if (!$this->revokeAccess($access['id'])) {
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Get user's accessible forms
     */
    public function getUserAccessibleForms(int $userId): array
    {
        $accesses = $this->where('user_id', $userId)
                         ->where('status', 'active')
                         ->groupStart()
                         ->where('expires_at IS NULL')
                         ->orWhere('expires_at >', date('Y-m-d H:i:s'))
                         ->groupEnd()
                         ->findAll();

        return array_column($accesses, 'form_id');
    }

    /**
     * Get device's accessible forms
     */
    public function getDeviceAccessibleForms(string $deviceId): array
    {
        $accesses = $this->where('device_id', $deviceId)
                         ->where('status', 'active')
                         ->groupStart()
                         ->where('expires_at IS NULL')
                         ->orWhere('expires_at >', date('Y-m-d H:i:s'))
                         ->groupEnd()
                         ->findAll();

        return array_column($accesses, 'form_id');
    }

    /**
     * Get form access list
     */
    public function getFormAccess(string $formId): array
    {
        return $this->select('kobo_form_access.*, kobo_users.username, granter.username as granted_by_name')
                    ->join('kobo_users', 'kobo_users.id = kobo_form_access.user_id', 'left')
                    ->join('kobo_users as granter', 'granter.id = kobo_form_access.granted_by', 'left')
                    ->where('kobo_form_access.form_id', $formId)
                    ->where('kobo_form_access.status', 'active')
                    ->orderBy('kobo_form_access.granted_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get user's form accesses
     */
    public function getUserFormAccesses(int $userId): array
    {
        return $this->select('kobo_form_access.*, kobo_forms.form_name, granter.username as granted_by_name')
                    ->join('kobo_forms', 'kobo_forms.form_id = kobo_form_access.form_id', 'left')
                    ->join('kobo_users as granter', 'granter.id = kobo_form_access.granted_by', 'left')
                    ->where('kobo_form_access.user_id', $userId)
                    ->where('kobo_form_access.status', 'active')
                    ->orderBy('kobo_form_access.granted_at', 'DESC')
                    ->findAll();
    }

    /**
     * Check existing access
     */
    private function checkExistingAccess(array $accessData): array|null
    {
        $builder = $this->where('form_id', $accessData['form_id']);

        if (isset($accessData['user_id']) && !empty($accessData['user_id'])) {
            $builder->where('user_id', $accessData['user_id']);
        } else {
            $builder->where('user_id IS NULL');
        }

        if (isset($accessData['device_id']) && !empty($accessData['device_id'])) {
            $builder->where('device_id', $accessData['device_id']);
        } else {
            $builder->where('device_id IS NULL');
        }

        return $builder->where('status', 'active')->first();
    }

    /**
     * Grant bulk access to multiple users
     */
    public function grantBulkUserAccess(array $userIds, string $formId, string $accessType, int $grantedBy, string $expiresAt = null): array
    {
        $results = [];
        
        foreach ($userIds as $userId) {
            $accessData = [
                'user_id' => $userId,
                'form_id' => $formId,
                'access_type' => $accessType,
                'granted_by' => $grantedBy,
                'expires_at' => $expiresAt
            ];

            $result = $this->grantAccess($accessData);
            $results[$userId] = $result !== false;
        }

        return $results;
    }

    /**
     * Grant bulk access to multiple devices
     */
    public function grantBulkDeviceAccess(array $deviceIds, string $formId, string $accessType, int $grantedBy, string $expiresAt = null): array
    {
        $results = [];
        
        foreach ($deviceIds as $deviceId) {
            $accessData = [
                'device_id' => $deviceId,
                'form_id' => $formId,
                'access_type' => $accessType,
                'granted_by' => $grantedBy,
                'expires_at' => $expiresAt
            ];

            $result = $this->grantAccess($accessData);
            $results[$deviceId] = $result !== false;
        }

        return $results;
    }

    /**
     * Update access expiration
     */
    public function updateExpiration(int $accessId, string $expiresAt = null): bool
    {
        return $this->update($accessId, ['expires_at' => $expiresAt]);
    }

    /**
     * Extend access duration
     */
    public function extendAccess(int $accessId, int $days): bool
    {
        $access = $this->find($accessId);
        if (!$access) {
            return false;
        }

        $currentExpiry = $access['expires_at'] ?? date('Y-m-d H:i:s');
        $newExpiry = date('Y-m-d H:i:s', strtotime($currentExpiry . " +{$days} days"));

        return $this->update($accessId, ['expires_at' => $newExpiry]);
    }

    /**
     * Get access statistics
     */
    public function getAccessStats(string $formId = null): array
    {
        $db = \Config\Database::connect();
        
        $whereClause = $formId ? "WHERE form_id = '{$formId}'" : '';
        
        $query = $db->query("
            SELECT 
                access_type,
                status,
                COUNT(*) as count
            FROM kobo_form_access 
            {$whereClause}
            GROUP BY access_type, status
            ORDER BY access_type, status
        ");

        $results = $query->getResultArray();
        
        // Get device vs user access counts
        $deviceQuery = $db->query("
            SELECT 
                CASE 
                    WHEN device_id IS NOT NULL AND user_id IS NULL THEN 'device_only'
                    WHEN device_id IS NULL AND user_id IS NOT NULL THEN 'user_only'
                    WHEN device_id IS NOT NULL AND user_id IS NOT NULL THEN 'user_device'
                    ELSE 'unknown'
                END as access_method,
                COUNT(*) as count
            FROM kobo_form_access
            {$whereClause}
            WHERE status = 'active'
            GROUP BY access_method
        ");

        $accessMethods = [];
        foreach ($deviceQuery->getResultArray() as $row) {
            $accessMethods[$row['access_method']] = $row['count'];
        }

        return [
            'access_by_type_status' => $results,
            'access_methods' => $accessMethods,
            'total_active' => $this->where('status', 'active')->countAllResults()
        ];
    }

    /**
     * Clean up expired access entries
     */
    public function cleanupExpiredAccess(): int
    {
        $expiredAccess = $this->where('expires_at <', date('Y-m-d H:i:s'))
                              ->where('status', 'active')
                              ->findAll();

        $updatedCount = 0;
        foreach ($expiredAccess as $access) {
            if ($this->update($access['id'], ['status' => 'expired'])) {
                $updatedCount++;
            }
        }

        return $updatedCount;
    }

    /**
     * Get access with pagination and filters
     */
    public function getAccessWithPagination(int $perPage = 20, array $filters = []): array
    {
        $builder = $this->select('kobo_form_access.*, kobo_users.username, kobo_forms.form_name, granter.username as granted_by_name')
                        ->join('kobo_users', 'kobo_users.id = kobo_form_access.user_id', 'left')
                        ->join('kobo_forms', 'kobo_forms.form_id = kobo_form_access.form_id', 'left')
                        ->join('kobo_users as granter', 'granter.id = kobo_form_access.granted_by', 'left');

        // Apply filters
        if (!empty($filters['form_id'])) {
            $builder->where('kobo_form_access.form_id', $filters['form_id']);
        }

        if (!empty($filters['user_id'])) {
            $builder->where('kobo_form_access.user_id', $filters['user_id']);
        }

        if (!empty($filters['device_id'])) {
            $builder->where('kobo_form_access.device_id', $filters['device_id']);
        }

        if (!empty($filters['access_type'])) {
            $builder->where('kobo_form_access.access_type', $filters['access_type']);
        }

        if (!empty($filters['status'])) {
            $builder->where('kobo_form_access.status', $filters['status']);
        }

        if (!empty($filters['search'])) {
            $builder->groupStart()
                    ->like('kobo_users.username', $filters['search'])
                    ->orLike('kobo_forms.form_name', $filters['search'])
                    ->orLike('kobo_form_access.device_id', $filters['search'])
                    ->groupEnd();
        }

        $builder->orderBy('kobo_form_access.granted_at', 'DESC');

        return [
            'access_records' => $builder->paginate($perPage),
            'pager' => $this->pager
        ];
    }

    /**
     * Check if user can grant access (admin/manager check)
     */
    public function canGrantAccess(int $userId): bool
    {
        $userModel = new \App\Models\Kobo\KoboUserModel();
        return $userModel->isAdmin($userId) || $userModel->isManager($userId);
    }

    /**
     * Audit access changes
     */
    public function auditAccessChange(int $accessId, string $action, int $performedBy, array $details = []): void
    {
        $auditModel = new \App\Models\Kobo\KoboAuditLogModel();
        
        $auditModel->logActivity(
            $performedBy,
            'form_access',
            $action,
            $accessId,
            json_encode($details)
        );
    }
}