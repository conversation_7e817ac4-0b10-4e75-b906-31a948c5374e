<?php

namespace App\Models\Kobo;

use CodeIgniter\Model;

/**
 * Kobo User Model
 * 
 * Handles CRUD operations for kobo_users table
 * Provides user authentication and role management functionality
 */
class KoboUserModel extends Model
{
    protected $table = 'kobo_users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'username',
        'email', 
        'password_hash',
        'role',
        'status',
        'created_at',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'username' => 'required|alpha_numeric_punct|min_length[3]|max_length[100]|is_unique[kobo_users.username,id,{id}]',
        'email' => 'required|valid_email|max_length[255]|is_unique[kobo_users.email,id,{id}]',
        'password_hash' => 'required|min_length[60]',
        'role' => 'required|in_list[admin,manager,enumerator]',
        'status' => 'required|in_list[active,inactive]'
    ];

    protected $validationMessages = [
        'username' => [
            'required' => 'Username is required',
            'alpha_numeric_punct' => 'Username can only contain alphanumeric characters and punctuation',
            'min_length' => 'Username must be at least 3 characters long',
            'max_length' => 'Username cannot exceed 100 characters',
            'is_unique' => 'Username already exists'
        ],
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Email must be a valid email address',
            'max_length' => 'Email cannot exceed 255 characters',
            'is_unique' => 'Email already exists'
        ],
        'password_hash' => [
            'required' => 'Password is required',
            'min_length' => 'Invalid password hash format'
        ],
        'role' => [
            'required' => 'User role is required',
            'in_list' => 'Role must be admin, manager, or enumerator'
        ],
        'status' => [
            'required' => 'User status is required',
            'in_list' => 'Status must be active or inactive'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    /**
     * Hash password before saving to database
     */
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password']) && !empty($data['data']['password'])) {
            $data['data']['password_hash'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
            unset($data['data']['password']);
        }
        return $data;
    }

    /**
     * Create a new user account
     */
    public function createUser(array $userData): int|false
    {
        // Set default values
        $userData['status'] = $userData['status'] ?? 'active';
        $userData['role'] = $userData['role'] ?? 'enumerator';
        
        return $this->insert($userData);
    }

    /**
     * Authenticate user with username/email and password
     */
    public function authenticateUser(string $login, string $password): array|false
    {
        // Find user by username or email
        $user = $this->where('username', $login)
                     ->orWhere('email', $login)
                     ->where('status', 'active')
                     ->first();

        if (!$user) {
            return false;
        }

        // Verify password
        if (password_verify($password, $user['password_hash'])) {
            // Remove sensitive data
            unset($user['password_hash']);
            return $user;
        }

        return false;
    }

    /**
     * Find user by username
     */
    public function findByUsername(string $username): array|null
    {
        return $this->where('username', $username)->first();
    }

    /**
     * Find user by email
     */
    public function findByEmail(string $email): array|null
    {
        return $this->where('email', $email)->first();
    }

    /**
     * Get users by role
     */
    public function getUsersByRole(string $role): array
    {
        return $this->where('role', $role)
                    ->where('status', 'active')
                    ->findAll();
    }

    /**
     * Update user password
     */
    public function updatePassword(int $userId, string $newPassword): bool
    {
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        return $this->update($userId, ['password_hash' => $hashedPassword]);
    }

    /**
     * Activate user account
     */
    public function activateUser(int $userId): bool
    {
        return $this->update($userId, ['status' => 'active']);
    }

    /**
     * Deactivate user account
     */
    public function deactivateUser(int $userId): bool
    {
        return $this->update($userId, ['status' => 'inactive']);
    }

    /**
     * Check if user has specific role
     */
    public function hasRole(int $userId, string $role): bool
    {
        $user = $this->find($userId);
        return $user && $user['role'] === $role;
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(int $userId): bool
    {
        return $this->hasRole($userId, 'admin');
    }

    /**
     * Check if user is manager
     */
    public function isManager(int $userId): bool
    {
        return $this->hasRole($userId, 'manager');
    }

    /**
     * Check if user is enumerator
     */
    public function isEnumerator(int $userId): bool
    {
        return $this->hasRole($userId, 'enumerator');
    }

    /**
     * Get active users count
     */
    public function getActiveUsersCount(): int
    {
        return $this->where('status', 'active')->countAllResults();
    }

    /**
     * Get users with pagination and search
     */
    public function getUsersWithPagination(int $perPage = 20, string $search = ''): array
    {
        $builder = $this->select('id, username, email, role, status, created_at, updated_at');
        
        if (!empty($search)) {
            $builder->groupStart()
                    ->like('username', $search)
                    ->orLike('email', $search)
                    ->groupEnd();
        }

        return [
            'users' => $builder->paginate($perPage),
            'pager' => $this->pager
        ];
    }

    /**
     * Update user role
     */
    public function updateRole(int $userId, string $role): bool
    {
        $allowedRoles = ['admin', 'manager', 'enumerator'];
        if (!in_array($role, $allowedRoles)) {
            return false;
        }

        return $this->update($userId, ['role' => $role]);
    }

    /**
     * Get user statistics by role
     */
    public function getUserStats(): array
    {
        $db = \Config\Database::connect();
        $query = $db->query("
            SELECT 
                role,
                status,
                COUNT(*) as count
            FROM kobo_users 
            GROUP BY role, status
            ORDER BY role, status
        ");

        $results = $query->getResultArray();
        $stats = [
            'total' => $this->countAll(),
            'active' => $this->where('status', 'active')->countAllResults(),
            'inactive' => $this->where('status', 'inactive')->countAllResults(),
            'by_role' => []
        ];

        foreach ($results as $result) {
            if (!isset($stats['by_role'][$result['role']])) {
                $stats['by_role'][$result['role']] = ['active' => 0, 'inactive' => 0];
            }
            $stats['by_role'][$result['role']][$result['status']] = $result['count'];
        }

        return $stats;
    }

    /**
     * Validate password strength
     */
    public function validatePasswordStrength(string $password): array
    {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        }

        return $errors;
    }

    /**
     * Get recent user activities (requires audit log)
     */
    public function getRecentActivities(int $userId, int $limit = 10): array
    {
        $auditModel = new \App\Models\Kobo\KoboAuditLogModel();
        return $auditModel->where('user_id', $userId)
                          ->orderBy('created_at', 'DESC')
                          ->limit($limit)
                          ->findAll();
    }
}