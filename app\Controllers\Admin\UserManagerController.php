<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Kobo\KoboUserModel;
use App\Models\Kobo\KoboAuditLogModel;
use App\Models\Kobo\KoboFormAccessModel;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * User Manager Controller
 * 
 * Handles admin user management operations including CRUD,
 * role assignment, bulk operations, and activity monitoring
 */
class UserManagerController extends BaseController
{
    protected $userModel;
    protected $auditModel;
    protected $accessModel;
    protected $session;

    public function __construct()
    {
        $this->userModel = new KoboUserModel();
        $this->auditModel = new KoboAuditLogModel();
        $this->accessModel = new KoboFormAccessModel();
        $this->session = session();
    }

    /**
     * Display user management dashboard
     */
    public function index()
    {
        // Check admin permissions
        if (!$this->checkAdminAccess()) {
            return redirect()->to('/auth/login')->with('error', 'Admin access required');
        }

        try {
            // Get user statistics
            $stats = $this->userModel->getUserStats();
            
            // Get recent user activities
            $recentActivities = $this->auditModel->getRecentActivities(20);
            
            // Get users with pagination
            $search = $this->request->getGet('search') ?? '';
            $perPage = 20;
            $usersData = $this->userModel->getUsersWithPagination($perPage, $search);

            $data = [
                'title' => 'User Management - Kobo Collect Admin',
                'stats' => $stats,
                'users' => $usersData['users'],
                'pager' => $usersData['pager'],
                'recent_activities' => $recentActivities,
                'search' => $search,
                'current_page' => 'users'
            ];

            return view('admin/kobo/users/index', $data);

        } catch (\Exception $e) {
            log_message('error', 'User management index error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load user management dashboard');
        }
    }

    /**
     * Display user creation form
     */
    public function create()
    {
        if (!$this->checkAdminAccess()) {
            return redirect()->to('/auth/login')->with('error', 'Admin access required');
        }

        $data = [
            'title' => 'Create New User - Kobo Admin',
            'current_page' => 'users',
            'roles' => ['admin', 'manager', 'enumerator'],
            'user' => null
        ];

        return view('admin/kobo/users/form', $data);
    }

    /**
     * Store new user
     */
    public function store()
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            // Validate input
            $validationRules = [
                'username' => 'required|alpha_numeric_punct|min_length[3]|max_length[100]|is_unique[kobo_users.username]',
                'email' => 'required|valid_email|max_length[255]|is_unique[kobo_users.email]',
                'password' => 'required|min_length[8]',
                'role' => 'required|in_list[admin,manager,enumerator]',
                'status' => 'required|in_list[active,inactive]'
            ];

            if (!$this->validate($validationRules)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $this->validator->getErrors()
                ]);
            }

            $userData = [
                'username' => $this->request->getPost('username'),
                'email' => $this->request->getPost('email'),
                'password' => $this->request->getPost('password'),
                'role' => $this->request->getPost('role'),
                'status' => $this->request->getPost('status')
            ];

            // Validate password strength
            $passwordErrors = $this->userModel->validatePasswordStrength($userData['password']);
            if (!empty($passwordErrors)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Password does not meet requirements',
                    'errors' => ['password' => $passwordErrors]
                ]);
            }

            $userId = $this->userModel->createUser($userData);

            if ($userId) {
                // Log user creation
                $this->auditModel->logActivity(
                    $this->getCurrentUserId(),
                    'user',
                    'created',
                    $userId,
                    ['new_data' => array_diff_key($userData, ['password' => ''])]
                );

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'User created successfully',
                    'user_id' => $userId
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to create user'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'User creation error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error occurred'
            ]);
        }
    }

    /**
     * Display user details
     */
    public function show($id)
    {
        if (!$this->checkAdminAccess()) {
            return redirect()->to('/auth/login')->with('error', 'Admin access required');
        }

        try {
            $user = $this->userModel->find($id);
            if (!$user) {
                throw new \Exception('User not found');
            }

            // Get user's recent activities
            $activities = $this->auditModel->getUserActivities($id, 50);
            
            // Get user's form access permissions
            $formAccess = $this->accessModel->getUserAccessibleForms($id);
            
            // Get user statistics
            $userStats = [
                'total_activities' => count($activities),
                'last_login' => $this->getLastLoginDate($id),
                'forms_accessible' => count($formAccess)
            ];

            $data = [
                'title' => 'User Details - ' . $user['username'],
                'user' => $user,
                'activities' => $activities,
                'form_access' => $formAccess,
                'user_stats' => $userStats,
                'current_page' => 'users'
            ];

            return view('admin/kobo/users/show', $data);

        } catch (\Exception $e) {
            log_message('error', 'User view error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load user details');
        }
    }

    /**
     * Display user edit form
     */
    public function edit($id)
    {
        if (!$this->checkAdminAccess()) {
            return redirect()->to('/auth/login')->with('error', 'Admin access required');
        }

        try {
            $user = $this->userModel->find($id);
            if (!$user) {
                throw new \Exception('User not found');
            }

            $data = [
                'title' => 'Edit User - ' . $user['username'],
                'user' => $user,
                'roles' => ['admin', 'manager', 'enumerator'],
                'current_page' => 'users'
            ];

            return view('admin/kobo/users/form', $data);

        } catch (\Exception $e) {
            log_message('error', 'User edit form error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to load user edit form');
        }
    }

    /**
     * Update user
     */
    public function update($id)
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            $user = $this->userModel->find($id);
            if (!$user) {
                return $this->response->setJSON(['success' => false, 'message' => 'User not found']);
            }

            // Validation rules (username and email uniqueness checked excluding current user)
            $validationRules = [
                'username' => "required|alpha_numeric_punct|min_length[3]|max_length[100]|is_unique[kobo_users.username,id,{$id}]",
                'email' => "required|valid_email|max_length[255]|is_unique[kobo_users.email,id,{$id}]",
                'role' => 'required|in_list[admin,manager,enumerator]',
                'status' => 'required|in_list[active,inactive]'
            ];

            if (!$this->validate($validationRules)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $this->validator->getErrors()
                ]);
            }

            $oldData = $user;
            $updateData = [
                'username' => $this->request->getPost('username'),
                'email' => $this->request->getPost('email'),
                'role' => $this->request->getPost('role'),
                'status' => $this->request->getPost('status')
            ];

            // Handle password update if provided
            $newPassword = $this->request->getPost('password');
            if (!empty($newPassword)) {
                $passwordErrors = $this->userModel->validatePasswordStrength($newPassword);
                if (!empty($passwordErrors)) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Password does not meet requirements',
                        'errors' => ['password' => $passwordErrors]
                    ]);
                }
                $updateData['password'] = $newPassword;
            }

            $success = $this->userModel->update($id, $updateData);

            if ($success) {
                // Log user update
                $this->auditModel->logFormUpdated(
                    $this->getCurrentUserId(),
                    $id,
                    array_diff_key($oldData, ['password_hash' => '']),
                    array_diff_key($updateData, ['password' => ''])
                );

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'User updated successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update user'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'User update error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error occurred'
            ]);
        }
    }

    /**
     * Delete user
     */
    public function delete($id)
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            $user = $this->userModel->find($id);
            if (!$user) {
                return $this->response->setJSON(['success' => false, 'message' => 'User not found']);
            }

            // Prevent self-deletion
            if ($id == $this->getCurrentUserId()) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Cannot delete your own account'
                ]);
            }

            $success = $this->userModel->delete($id);

            if ($success) {
                // Log user deletion
                $this->auditModel->logActivity(
                    $this->getCurrentUserId(),
                    'user',
                    'deleted',
                    $id,
                    ['old_data' => array_diff_key($user, ['password_hash' => ''])]
                );

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'User deleted successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to delete user'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'User deletion error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error occurred'
            ]);
        }
    }

    /**
     * Bulk operations on users
     */
    public function bulkOperation()
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            $operation = $this->request->getPost('operation');
            $userIds = $this->request->getPost('user_ids');

            if (empty($operation) || empty($userIds) || !is_array($userIds)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid operation or user selection'
                ]);
            }

            $results = ['success' => 0, 'failed' => 0, 'messages' => []];
            $currentUserId = $this->getCurrentUserId();

            foreach ($userIds as $userId) {
                // Skip if trying to operate on self for certain operations
                if (in_array($operation, ['delete', 'deactivate']) && $userId == $currentUserId) {
                    $results['failed']++;
                    $results['messages'][] = "Cannot {$operation} your own account (ID: {$userId})";
                    continue;
                }

                switch ($operation) {
                    case 'activate':
                        if ($this->userModel->activateUser($userId)) {
                            $results['success']++;
                            $this->auditModel->logActivity($currentUserId, 'user', 'activated', $userId);
                        } else {
                            $results['failed']++;
                        }
                        break;

                    case 'deactivate':
                        if ($this->userModel->deactivateUser($userId)) {
                            $results['success']++;
                            $this->auditModel->logActivity($currentUserId, 'user', 'deactivated', $userId);
                        } else {
                            $results['failed']++;
                        }
                        break;

                    case 'delete':
                        $user = $this->userModel->find($userId);
                        if ($user && $this->userModel->delete($userId)) {
                            $results['success']++;
                            $this->auditModel->logActivity(
                                $currentUserId,
                                'user',
                                'deleted',
                                $userId,
                                ['old_data' => array_diff_key($user, ['password_hash' => ''])]
                            );
                        } else {
                            $results['failed']++;
                        }
                        break;

                    default:
                        $results['failed']++;
                        $results['messages'][] = "Unknown operation: {$operation}";
                        break;
                }
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => "Bulk operation completed: {$results['success']} successful, {$results['failed']} failed",
                'results' => $results
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Bulk user operation error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error during bulk operation'
            ]);
        }
    }

    /**
     * Update user role
     */
    public function updateRole($id)
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            $newRole = $this->request->getPost('role');
            
            if (!in_array($newRole, ['admin', 'manager', 'enumerator'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid role specified'
                ]);
            }

            $user = $this->userModel->find($id);
            if (!$user) {
                return $this->response->setJSON(['success' => false, 'message' => 'User not found']);
            }

            $oldRole = $user['role'];
            $success = $this->userModel->updateRole($id, $newRole);

            if ($success) {
                // Log role change
                $this->auditModel->logActivity(
                    $this->getCurrentUserId(),
                    'user',
                    'role_updated',
                    $id,
                    [
                        'old_data' => ['role' => $oldRole],
                        'new_data' => ['role' => $newRole]
                    ]
                );

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'User role updated successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update user role'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Role update error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error occurred'
            ]);
        }
    }

    /**
     * Reset user password
     */
    public function resetPassword($id)
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            $user = $this->userModel->find($id);
            if (!$user) {
                return $this->response->setJSON(['success' => false, 'message' => 'User not found']);
            }

            // Generate temporary password
            $tempPassword = $this->generateRandomPassword();
            
            $success = $this->userModel->updatePassword($id, $tempPassword);

            if ($success) {
                // Log password reset
                $this->auditModel->logActivity(
                    $this->getCurrentUserId(),
                    'user',
                    'password_reset',
                    $id
                );

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Password reset successfully',
                    'temp_password' => $tempPassword
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to reset password'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Password reset error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error occurred'
            ]);
        }
    }

    /**
     * Export users data
     */
    public function export()
    {
        if (!$this->checkAdminAccess()) {
            return redirect()->back()->with('error', 'Admin access required');
        }

        try {
            $format = $this->request->getGet('format') ?? 'csv';
            $users = $this->userModel->findAll();

            if ($format === 'csv') {
                return $this->exportCSV($users);
            } else {
                return $this->response->setJSON(['error' => 'Unsupported export format']);
            }

        } catch (\Exception $e) {
            log_message('error', 'User export error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to export users');
        }
    }

    /**
     * Export users to CSV
     */
    private function exportCSV(array $users): ResponseInterface
    {
        $filename = 'kobo_users_' . date('Y-m-d_H-i-s') . '.csv';
        
        // Set headers for CSV download
        $this->response->setHeader('Content-Type', 'text/csv');
        $this->response->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"');
        
        // Create CSV content
        $csv = "ID,Username,Email,Role,Status,Created At,Updated At\n";
        
        foreach ($users as $user) {
            $csv .= sprintf(
                "%d,%s,%s,%s,%s,%s,%s\n",
                $user['id'],
                $user['username'],
                $user['email'],
                $user['role'],
                $user['status'],
                $user['created_at'],
                $user['updated_at']
            );
        }

        // Log export activity
        $this->auditModel->logActivity(
            $this->getCurrentUserId(),
            'user',
            'exported',
            null,
            ['new_data' => ['format' => 'csv', 'count' => count($users)]]
        );

        return $this->response->setBody($csv);
    }

    /**
     * Get user activity timeline
     */
    public function getActivityTimeline($id)
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            $activities = $this->auditModel->getUserActivities($id, 100);
            
            // Format activities for timeline
            $timeline = [];
            foreach ($activities as $activity) {
                $timeline[] = [
                    'id' => $activity['id'],
                    'action' => $activity['action'],
                    'entity_type' => $activity['entity_type'],
                    'entity_id' => $activity['entity_id'],
                    'created_at' => $activity['created_at'],
                    'ip_address' => $activity['ip_address'],
                    'user_agent' => $activity['user_agent']
                ];
            }

            return $this->response->setJSON([
                'success' => true,
                'activities' => $timeline
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Activity timeline error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to load activity timeline'
            ]);
        }
    }

    /**
     * Search users with AJAX
     */
    public function search()
    {
        if (!$this->checkAdminAccess()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Admin access required']);
        }

        try {
            $query = $this->request->getGet('q') ?? '';
            $page = (int)($this->request->getGet('page') ?? 1);
            $perPage = 10;

            $usersData = $this->userModel->getUsersWithPagination($perPage, $query);
            
            return $this->response->setJSON([
                'success' => true,
                'users' => $usersData['users'],
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'has_more' => count($usersData['users']) === $perPage
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'User search error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Search failed'
            ]);
        }
    }

    // Private helper methods

    /**
     * Check if current user has admin access
     */
    private function checkAdminAccess(): bool
    {
        // Implement your authentication logic here
        // For now, return true for development
        return true;
        
        // Example implementation:
        // $userId = $this->getCurrentUserId();
        // return $userId && $this->userModel->isAdmin($userId);
    }

    /**
     * Get current user ID from session/JWT
     */
    private function getCurrentUserId(): ?int
    {
        // Implement your session/JWT logic here
        // For now, return 1 for development
        return 1;
        
        // Example implementation:
        // return $this->session->get('user_id');
    }

    /**
     * Get last login date for user
     */
    private function getLastLoginDate(int $userId): ?string
    {
        $lastLogin = $this->auditModel->where('user_id', $userId)
                                     ->where('action', 'login_success')
                                     ->orderBy('created_at', 'DESC')
                                     ->first();
        
        return $lastLogin ? $lastLogin['created_at'] : null;
    }

    /**
     * Generate random password
     */
    private function generateRandomPassword(int $length = 12): string
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';
        $max = strlen($characters) - 1;
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[random_int(0, $max)];
        }
        
        return $password;
    }
}