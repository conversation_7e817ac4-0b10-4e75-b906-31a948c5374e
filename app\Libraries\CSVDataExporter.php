<?php

namespace App\Libraries;

use App\Models\Kobo\KoboSubmissionModel;
use App\Models\Kobo\KoboFormModel;
use App\Libraries\XMLProcessingEngine;

/**
 * CSV Data Exporter
 * 
 * Advanced CSV export functionality with custom field selection and filtering
 */
class CSVDataExporter
{
    protected $submissionModel;
    protected $formModel;
    protected $xmlProcessor;
    protected $exportPath;
    protected $chunkSize = 1000; // Process records in chunks
    
    public function __construct()
    {
        $this->submissionModel = new KoboSubmissionModel();
        $this->formModel = new KoboFormModel();
        $this->xmlProcessor = new XMLProcessingEngine();
        $this->exportPath = WRITEPATH . 'exports/csv/';
        $this->ensureExportDirectory();
    }

    /**
     * Export submissions to CSV with custom options
     */
    public function exportSubmissions(array $options = []): array
    {
        try {
            // Validate and prepare export options
            $exportOptions = $this->prepareExportOptions($options);
            
            // Generate export filename
            $filename = $this->generateExportFilename($exportOptions);
            $filePath = $this->exportPath . $filename;
            
            // Get submission data
            $submissionData = $this->getSubmissionData($exportOptions);
            
            if (empty($submissionData)) {
                return [
                    'success' => false,
                    'message' => 'No data found for export',
                    'total_records' => 0
                ];
            }

            // Process and export data
            $exportResult = $this->processAndExportData($submissionData, $filePath, $exportOptions);
            
            // Generate export metadata
            $metadata = $this->generateExportMetadata($exportResult, $exportOptions);
            
            return [
                'success' => true,
                'file_path' => $filePath,
                'filename' => $filename,
                'download_url' => base_url('admin/kobo/exports/download/' . basename($filename)),
                'total_records' => $exportResult['total_records'],
                'file_size' => filesize($filePath),
                'metadata' => $metadata
            ];

        } catch (\Exception $e) {
            log_message('error', 'CSV export failed: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Prepare and validate export options
     */
    protected function prepareExportOptions(array $options): array
    {
        $defaultOptions = [
            'form_ids' => [], // Empty = all forms
            'date_from' => null,
            'date_to' => null,
            'status' => [], // Empty = all statuses
            'fields' => [], // Empty = all fields
            'exclude_fields' => [],
            'include_metadata' => true,
            'flatten_data' => true,
            'delimiter' => ',',
            'enclosure' => '"',
            'escape_char' => '\\',
            'include_headers' => true,
            'date_format' => 'Y-m-d H:i:s',
            'null_value' => '',
            'boolean_format' => 'true/false', // or '1/0'
            'max_records' => null,
            'sort_by' => 'created_at',
            'sort_order' => 'ASC'
        ];

        $exportOptions = array_merge($defaultOptions, $options);
        
        // Validate dates
        if ($exportOptions['date_from']) {
            $exportOptions['date_from'] = $this->validateDate($exportOptions['date_from']);
        }
        if ($exportOptions['date_to']) {
            $exportOptions['date_to'] = $this->validateDate($exportOptions['date_to']);
        }

        // Validate form IDs
        if (!empty($exportOptions['form_ids'])) {
            $exportOptions['form_ids'] = $this->validateFormIds($exportOptions['form_ids']);
        }

        return $exportOptions;
    }

    /**
     * Get submission data based on export options
     */
    protected function getSubmissionData(array $options): array
    {
        $builder = $this->submissionModel->builder();
        
        // Apply form filters
        if (!empty($options['form_ids'])) {
            $builder->whereIn('form_id', $options['form_ids']);
        }

        // Apply date filters
        if ($options['date_from']) {
            $builder->where('created_at >=', $options['date_from']);
        }
        if ($options['date_to']) {
            $builder->where('created_at <=', $options['date_to']);
        }

        // Apply status filters
        if (!empty($options['status'])) {
            $builder->whereIn('status', $options['status']);
        }

        // Apply sorting
        $builder->orderBy($options['sort_by'], $options['sort_order']);

        // Apply record limit
        if ($options['max_records']) {
            $builder->limit($options['max_records']);
        }

        // Get data with form information
        $builder->select('kobo_submissions.*, kobo_forms.form_name, kobo_forms.version as form_version');
        $builder->join('kobo_forms', 'kobo_forms.form_id = kobo_submissions.form_id', 'left');

        return $builder->get()->getResultArray();
    }

    /**
     * Process and export data to CSV file
     */
    protected function processAndExportData(array $submissionData, string $filePath, array $options): array
    {
        $handle = fopen($filePath, 'w');
        if (!$handle) {
            throw new \Exception('Unable to create export file');
        }

        $totalRecords = 0;
        $fieldMap = [];
        $allFields = [];

        try {
            // Process data in chunks
            $chunks = array_chunk($submissionData, $this->chunkSize);
            
            foreach ($chunks as $chunkIndex => $chunk) {
                $processedChunk = $this->processDataChunk($chunk, $options);
                
                // Build field map from first chunk
                if ($chunkIndex === 0 && !empty($processedChunk)) {
                    $allFields = $this->buildFieldMap($processedChunk, $options);
                    
                    // Write headers
                    if ($options['include_headers']) {
                        $this->writeCSVRow($handle, $allFields, $options);
                    }
                }

                // Write data rows
                foreach ($processedChunk as $row) {
                    $csvRow = $this->buildCSVRow($row, $allFields, $options);
                    $this->writeCSVRow($handle, $csvRow, $options);
                    $totalRecords++;
                }
            }

            return [
                'total_records' => $totalRecords,
                'total_fields' => count($allFields),
                'field_map' => $allFields
            ];

        } finally {
            fclose($handle);
        }
    }

    /**
     * Process a chunk of submission data
     */
    protected function processDataChunk(array $chunk, array $options): array
    {
        $processedData = [];

        foreach ($chunk as $submission) {
            // Parse JSON data
            $jsonData = [];
            if (!empty($submission['json_data'])) {
                $jsonData = json_decode($submission['json_data'], true) ?: [];
            }

            // Flatten nested data if requested
            if ($options['flatten_data']) {
                $jsonData = $this->flattenArray($jsonData);
            }

            // Merge with submission metadata
            $rowData = array_merge([
                'submission_id' => $submission['submission_id'],
                'form_id' => $submission['form_id'],
                'form_name' => $submission['form_name'] ?? '',
                'form_version' => $submission['form_version'] ?? '',
                'device_id' => $submission['device_id'] ?? '',
                'status' => $submission['status'],
                'created_at' => $submission['created_at'],
                'updated_at' => $submission['updated_at']
            ], $jsonData);

            // Include additional metadata if requested
            if ($options['include_metadata']) {
                $rowData = array_merge($rowData, [
                    'record_id' => $submission['id'],
                    'xml_length' => strlen($submission['xml_content'] ?? ''),
                    'processed_at' => $submission['processed_at'] ?? ''
                ]);
            }

            $processedData[] = $rowData;
        }

        return $processedData;
    }

    /**
     * Build field map from processed data
     */
    protected function buildFieldMap(array $processedData, array $options): array
    {
        $allFields = [];

        // Collect all possible fields
        foreach ($processedData as $row) {
            $allFields = array_merge($allFields, array_keys($row));
        }

        $allFields = array_unique($allFields);

        // Apply field selection/exclusion
        if (!empty($options['fields'])) {
            $allFields = array_intersect($allFields, $options['fields']);
        }

        if (!empty($options['exclude_fields'])) {
            $allFields = array_diff($allFields, $options['exclude_fields']);
        }

        // Ensure consistent field order
        $standardFields = [
            'submission_id', 'form_id', 'form_name', 'form_version', 
            'device_id', 'status', 'created_at', 'updated_at'
        ];

        $orderedFields = [];
        
        // Add standard fields first
        foreach ($standardFields as $field) {
            if (in_array($field, $allFields)) {
                $orderedFields[] = $field;
            }
        }

        // Add remaining fields
        foreach ($allFields as $field) {
            if (!in_array($field, $orderedFields)) {
                $orderedFields[] = $field;
            }
        }

        return $orderedFields;
    }

    /**
     * Build CSV row from data
     */
    protected function buildCSVRow(array $rowData, array $fieldMap, array $options): array
    {
        $csvRow = [];

        foreach ($fieldMap as $field) {
            $value = $rowData[$field] ?? $options['null_value'];
            $csvRow[] = $this->formatCSVValue($value, $options);
        }

        return $csvRow;
    }

    /**
     * Format value for CSV output
     */
    protected function formatCSVValue($value, array $options)
    {
        if ($value === null || $value === '') {
            return $options['null_value'];
        }

        // Format dates
        if ($this->isDateField($value)) {
            try {
                $date = new \DateTime($value);
                return $date->format($options['date_format']);
            } catch (\Exception $e) {
                return $value;
            }
        }

        // Format booleans
        if (is_bool($value)) {
            if ($options['boolean_format'] === '1/0') {
                return $value ? '1' : '0';
            } else {
                return $value ? 'true' : 'false';
            }
        }

        // Format arrays/objects
        if (is_array($value) || is_object($value)) {
            return json_encode($value);
        }

        // Clean string values
        if (is_string($value)) {
            // Remove line breaks and tabs
            $value = str_replace(["\r", "\n", "\t"], [' ', ' ', ' '], $value);
            // Trim whitespace
            $value = trim($value);
        }

        return $value;
    }

    /**
     * Write CSV row to file
     */
    protected function writeCSVRow($handle, array $row, array $options): void
    {
        fputcsv(
            $handle, 
            $row, 
            $options['delimiter'], 
            $options['enclosure'], 
            $options['escape_char']
        );
    }

    /**
     * Generate export filename
     */
    protected function generateExportFilename(array $options): string
    {
        $timestamp = date('YmdHis');
        $formPart = '';
        
        if (!empty($options['form_ids']) && count($options['form_ids']) === 1) {
            $formInfo = $this->formModel->where('form_id', $options['form_ids'][0])->first();
            if ($formInfo) {
                $formPart = '_' . preg_replace('/[^a-zA-Z0-9]/', '_', $formInfo['form_name']);
                $formPart = substr($formPart, 0, 20); // Limit length
            }
        }

        return "kobo_export{$formPart}_{$timestamp}.csv";
    }

    /**
     * Generate export metadata
     */
    protected function generateExportMetadata(array $exportResult, array $options): array
    {
        return [
            'export_date' => date('Y-m-d H:i:s'),
            'total_records' => $exportResult['total_records'],
            'total_fields' => $exportResult['total_fields'],
            'filters_applied' => [
                'form_ids' => $options['form_ids'],
                'date_from' => $options['date_from'],
                'date_to' => $options['date_to'],
                'status' => $options['status'],
                'max_records' => $options['max_records']
            ],
            'field_selection' => [
                'included_fields' => $options['fields'],
                'excluded_fields' => $options['exclude_fields']
            ],
            'format_options' => [
                'delimiter' => $options['delimiter'],
                'date_format' => $options['date_format'],
                'boolean_format' => $options['boolean_format'],
                'flatten_data' => $options['flatten_data']
            ]
        ];
    }

    /**
     * Export forms list to CSV
     */
    public function exportForms(array $options = []): array
    {
        try {
            $forms = $this->formModel->findAll();
            
            if (empty($forms)) {
                return [
                    'success' => false,
                    'message' => 'No forms found for export'
                ];
            }

            $filename = 'kobo_forms_' . date('YmdHis') . '.csv';
            $filePath = $this->exportPath . $filename;
            
            $handle = fopen($filePath, 'w');
            if (!$handle) {
                throw new \Exception('Unable to create export file');
            }

            // Write headers
            $headers = [
                'Form ID', 'Form Name', 'Version', 'Status', 'Description',
                'Created At', 'Updated At', 'Created By', 'Submission Count'
            ];
            fputcsv($handle, $headers);

            // Write form data
            foreach ($forms as $form) {
                $submissionCount = $this->submissionModel->where('form_id', $form['form_id'])->countAllResults();
                
                $row = [
                    $form['form_id'],
                    $form['form_name'],
                    $form['version'],
                    $form['status'],
                    $form['description'] ?? '',
                    $form['created_at'],
                    $form['updated_at'],
                    $form['created_by'] ?? '',
                    $submissionCount
                ];
                
                fputcsv($handle, $row);
            }

            fclose($handle);

            return [
                'success' => true,
                'file_path' => $filePath,
                'filename' => $filename,
                'download_url' => base_url('admin/kobo/exports/download/' . basename($filename)),
                'total_records' => count($forms),
                'file_size' => filesize($filePath)
            ];

        } catch (\Exception $e) {
            log_message('error', 'Forms export failed: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Export users list to CSV
     */
    public function exportUsers(array $options = []): array
    {
        try {
            $userModel = new \App\Models\Kobo\KoboUserModel();
            $users = $userModel->findAll();
            
            if (empty($users)) {
                return [
                    'success' => false,
                    'message' => 'No users found for export'
                ];
            }

            $filename = 'kobo_users_' . date('YmdHis') . '.csv';
            $filePath = $this->exportPath . $filename;
            
            $handle = fopen($filePath, 'w');
            if (!$handle) {
                throw new \Exception('Unable to create export file');
            }

            // Write headers
            $headers = [
                'User ID', 'Username', 'Email', 'Role', 'Status',
                'Created At', 'Updated At', 'Last Login'
            ];
            fputcsv($handle, $headers);

            // Write user data (excluding sensitive information)
            foreach ($users as $user) {
                $row = [
                    $user['id'],
                    $user['username'],
                    $user['email'],
                    $user['role'],
                    $user['status'],
                    $user['created_at'],
                    $user['updated_at'],
                    $user['last_login'] ?? ''
                ];
                
                fputcsv($handle, $row);
            }

            fclose($handle);

            return [
                'success' => true,
                'file_path' => $filePath,
                'filename' => $filename,
                'download_url' => base_url('admin/kobo/exports/download/' . basename($filename)),
                'total_records' => count($users),
                'file_size' => filesize($filePath)
            ];

        } catch (\Exception $e) {
            log_message('error', 'Users export failed: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get available export templates
     */
    public function getExportTemplates(): array
    {
        return [
            'basic' => [
                'name' => 'Basic Export',
                'description' => 'Essential fields only',
                'fields' => ['submission_id', 'form_name', 'device_id', 'status', 'created_at'],
                'flatten_data' => true
            ],
            'complete' => [
                'name' => 'Complete Export',
                'description' => 'All available fields',
                'fields' => [],
                'include_metadata' => true,
                'flatten_data' => true
            ],
            'minimal' => [
                'name' => 'Minimal Export',
                'description' => 'Form data only, no metadata',
                'exclude_fields' => ['xml_content', 'record_id', 'xml_length'],
                'include_metadata' => false,
                'flatten_data' => true
            ],
            'analysis' => [
                'name' => 'Analysis Ready',
                'description' => 'Optimized for data analysis',
                'flatten_data' => true,
                'boolean_format' => '1/0',
                'date_format' => 'Y-m-d',
                'null_value' => 'NULL'
            ]
        ];
    }

    /**
     * Helper methods
     */
    protected function flattenArray(array $array, string $prefix = ''): array
    {
        $result = [];
        
        foreach ($array as $key => $value) {
            $newKey = $prefix === '' ? $key : $prefix . '.' . $key;
            
            if (is_array($value)) {
                $result = array_merge($result, $this->flattenArray($value, $newKey));
            } else {
                $result[$newKey] = $value;
            }
        }
        
        return $result;
    }

    protected function validateDate(string $date): ?string
    {
        try {
            $dateObj = new \DateTime($date);
            return $dateObj->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            return null;
        }
    }

    protected function validateFormIds(array $formIds): array
    {
        return array_filter($formIds, function($id) {
            return !empty($id) && is_string($id);
        });
    }

    protected function isDateField($value): bool
    {
        if (!is_string($value)) {
            return false;
        }
        
        $datePatterns = [
            '/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/',  // ISO format
            '/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/',   // MySQL datetime
            '/^\d{4}-\d{2}-\d{2}$/'                     // Date only
        ];
        
        foreach ($datePatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }
        
        return false;
    }

    protected function ensureExportDirectory(): void
    {
        if (!is_dir($this->exportPath)) {
            mkdir($this->exportPath, 0755, true);
        }
    }

    /**
     * Clean up old export files
     */
    public function cleanupOldExports(int $maxAge = 86400): int
    {
        $cleaned = 0;
        $cutoff = time() - $maxAge;
        
        if (is_dir($this->exportPath)) {
            $files = glob($this->exportPath . '*.csv');
            foreach ($files as $file) {
                if (filemtime($file) < $cutoff) {
                    unlink($file);
                    $cleaned++;
                }
            }
        }
        
        return $cleaned;
    }
}