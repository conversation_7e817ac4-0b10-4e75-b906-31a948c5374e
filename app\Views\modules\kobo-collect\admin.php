<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Kobo Collect Admin Panel - CodiTest' ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
        
        .admin-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }
        
        .admin-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .btn-admin {
            padding: 0.75rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }
        
        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .stats-card {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
        }
        
        .quick-access {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-2">
                            <li class="breadcrumb-item">
                                <a href="<?= base_url() ?>" class="text-light text-decoration-none">
                                    <i class="fas fa-home me-1"></i>CodiTest
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="<?= base_url('modules') ?>" class="text-light text-decoration-none">Modules</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="<?= base_url('modules/kobo-collect') ?>" class="text-light text-decoration-none">Kobo Collect</a>
                            </li>
                            <li class="breadcrumb-item active text-light" aria-current="page">
                                Admin Panel
                            </li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold">
                        <i class="fas fa-shield-alt me-3"></i>Kobo Collect Admin Panel
                    </h1>
                    <p class="lead mb-0">Comprehensive administration and management interface</p>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge bg-success fs-6 me-2">
                        <i class="fas fa-check-circle me-1"></i>Active
                    </span>
                    <span class="badge bg-light text-dark fs-6">
                        v1.0.0
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container py-5">
        <!-- Quick Stats -->
        <div class="row mb-5">
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <h3 class="fw-bold mb-1">0</h3>
                    <p class="mb-0">Active Forms</p>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card" style="background: linear-gradient(45deg, #a8edea 0%, #fed6e3 100%);">
                    <h3 class="fw-bold mb-1 text-dark">0</h3>
                    <p class="mb-0 text-dark">Total Submissions</p>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card" style="background: linear-gradient(45deg, #ffecd2 0%, #fcb69f 100%);">
                    <h3 class="fw-bold mb-1 text-dark">0</h3>
                    <p class="mb-0 text-dark">Registered Users</p>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card" style="background: linear-gradient(45deg, #a8caba 0%, #5d4e75 100%);">
                    <h3 class="fw-bold mb-1">0</h3>
                    <p class="mb-0">Media Files</p>
                </div>
            </div>
        </div>

        <!-- Admin Modules -->
        <div class="row">
            <!-- Form Management -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="admin-card card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-file-alt feature-icon text-primary"></i>
                        <h4 class="card-title">Form Management</h4>
                        <p class="card-text">Upload, manage, and configure XLSForm questionnaires for mobile data collection.</p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>Upload XLSForm files</li>
                            <li><i class="fas fa-check text-success me-2"></i>Form versioning</li>
                            <li><i class="fas fa-check text-success me-2"></i>Status management</li>
                            <li><i class="fas fa-check text-success me-2"></i>Form preview</li>
                        </ul>
                        <a href="<?= base_url('admin/kobo/forms') ?>" class="btn btn-primary btn-admin">
                            <i class="fas fa-cog me-2"></i>Manage Forms
                        </a>
                    </div>
                </div>
            </div>

            <!-- User Management -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="admin-card card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-users feature-icon text-success"></i>
                        <h4 class="card-title">User Management</h4>
                        <p class="card-text">Manage enumerators, data collectors, and administrative users with role-based access.</p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>User registration</li>
                            <li><i class="fas fa-check text-success me-2"></i>Role assignment</li>
                            <li><i class="fas fa-check text-success me-2"></i>Access control</li>
                            <li><i class="fas fa-check text-success me-2"></i>Activity monitoring</li>
                        </ul>
                        <a href="<?= base_url('admin/kobo/users') ?>" class="btn btn-success btn-admin">
                            <i class="fas fa-user-cog me-2"></i>Manage Users
                        </a>
                    </div>
                </div>
            </div>

            <!-- Data Management -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="admin-card card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-database feature-icon text-info"></i>
                        <h4 class="card-title">Data Management</h4>
                        <p class="card-text">View, analyze, and export collected data submissions with advanced filtering options.</p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>Submission viewing</li>
                            <li><i class="fas fa-check text-success me-2"></i>Data validation</li>
                            <li><i class="fas fa-check text-success me-2"></i>Export formats</li>
                            <li><i class="fas fa-check text-success me-2"></i>Media handling</li>
                        </ul>
                        <a href="<?= base_url('admin/kobo/submissions') ?>" class="btn btn-info btn-admin">
                            <i class="fas fa-chart-bar me-2"></i>View Data
                        </a>
                    </div>
                </div>
            </div>

            <!-- API Documentation -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="admin-card card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-code feature-icon text-warning"></i>
                        <h4 class="card-title">API Management</h4>
                        <p class="card-text">Access comprehensive API documentation and test endpoints for integration.</p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>OpenRosa compliance</li>
                            <li><i class="fas fa-check text-success me-2"></i>RESTful endpoints</li>
                            <li><i class="fas fa-check text-success me-2"></i>Authentication</li>
                            <li><i class="fas fa-check text-success me-2"></i>Rate limiting</li>
                        </ul>
                        <a href="<?= base_url('api/v1/export/docs') ?>" class="btn btn-warning btn-admin" target="_blank">
                            <i class="fas fa-book me-2"></i>API Docs
                        </a>
                    </div>
                </div>
            </div>

            <!-- Export & Analytics -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="admin-card card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-download feature-icon text-danger"></i>
                        <h4 class="card-title">Export & Analytics</h4>
                        <p class="card-text">Advanced data export and real-time analytics with multiple format support.</p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>CSV/Excel export</li>
                            <li><i class="fas fa-check text-success me-2"></i>JSON API export</li>
                            <li><i class="fas fa-check text-success me-2"></i>Real-time sync</li>
                            <li><i class="fas fa-check text-success me-2"></i>Custom filtering</li>
                        </ul>
                        <a href="<?= base_url('admin/export-api') ?>" class="btn btn-danger btn-admin">
                            <i class="fas fa-chart-line me-2"></i>Export Tools
                        </a>
                    </div>
                </div>
            </div>

            <!-- System Settings -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="admin-card card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-cogs feature-icon text-secondary"></i>
                        <h4 class="card-title">System Settings</h4>
                        <p class="card-text">Configure system settings, security options, and monitor system health.</p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>Security settings</li>
                            <li><i class="fas fa-check text-success me-2"></i>System monitoring</li>
                            <li><i class="fas fa-check text-success me-2"></i>Audit logs</li>
                            <li><i class="fas fa-check text-success me-2"></i>Health checks</li>
                        </ul>
                        <a href="<?= base_url('admin/kobo/settings') ?>" class="btn btn-secondary btn-admin">
                            <i class="fas fa-wrench me-2"></i>Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Access Panel -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="quick-access">
                    <h3 class="mb-4 text-center">Quick Access</h3>
                    <div class="row text-center">
                        <div class="col-md-3 col-6 mb-3">
                            <a href="<?= base_url('api/v1/formList') ?>" class="btn btn-outline-primary btn-lg w-100" target="_blank">
                                <i class="fas fa-list mb-2 d-block"></i>
                                Form List API
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="<?= base_url('api/v1/export/health') ?>" class="btn btn-outline-success btn-lg w-100" target="_blank">
                                <i class="fas fa-heartbeat mb-2 d-block"></i>
                                System Health
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="<?= base_url('modules/kobo-collect') ?>" class="btn btn-outline-info btn-lg w-100">
                                <i class="fas fa-arrow-left mb-2 d-block"></i>
                                Back to Module
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="<?= base_url() ?>" class="btn btn-outline-warning btn-lg w-100">
                                <i class="fas fa-home mb-2 d-block"></i>
                                Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>Kobo Collect Integration - Admin Panel</h6>
                    <p class="mb-0 small">Complete mobile data collection management system</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Secure • Scalable • OpenRosa Compliant
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Add interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate stats on load
            const stats = document.querySelectorAll('.stats-card h3');
            stats.forEach(stat => {
                const finalValue = parseInt(stat.textContent) || 0;
                if (finalValue === 0) {
                    // For demo purposes, you can add actual data loading here
                    stat.textContent = '0';
                }
            });

            // Add hover effects to admin cards
            const cards = document.querySelectorAll('.admin-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>