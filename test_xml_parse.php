<?php

// Simple test script to debug XML parsing
$xmlFile = 'dev_guide/basic_xform.xml';

if (!file_exists($xmlFile)) {
    echo "File not found: $xmlFile\n";
    exit;
}

$xmlContent = file_get_contents($xmlFile);

echo "File size: " . strlen($xmlContent) . " bytes\n";
echo "First 200 characters:\n";
echo substr($xmlContent, 0, 200) . "\n\n";

// Check for BOM
$bom = substr($xmlContent, 0, 3);
if ($bom === "\xEF\xBB\xBF") {
    echo "BOM detected! Removing...\n";
    $xmlContent = substr($xmlContent, 3);
}

// Test with simplexml_load_string
libxml_use_internal_errors(true);
$xml = simplexml_load_string($xmlContent);

if ($xml === false) {
    echo "simplexml_load_string failed!\n";
    $errors = libxml_get_errors();
    foreach ($errors as $error) {
        echo "Error: " . trim($error->message) . "\n";
    }
} else {
    echo "XML parsed successfully!\n";
    echo "Root element: " . $xml->getName() . "\n";
    
    // Check namespaces
    $namespaces = $xml->getNamespaces(true);
    echo "Namespaces:\n";
    foreach ($namespaces as $prefix => $uri) {
        echo "  $prefix => $uri\n";
    }
}

libxml_clear_errors();