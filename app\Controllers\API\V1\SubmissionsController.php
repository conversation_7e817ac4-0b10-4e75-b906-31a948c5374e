<?php

namespace App\Controllers\API\V1;

use App\Controllers\BaseController;
use App\Models\Kobo\KoboSubmissionModel;
use App\Models\Kobo\KoboFormModel;
use App\Models\Kobo\KoboUserModel;
use App\Models\Kobo\KoboFormAccessModel;
use App\Models\Kobo\KoboAuditLogModel;
use App\Models\Kobo\KoboMediaFileModel;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Submissions API Controller
 * 
 * OpenRosa compliant API controller for data submission handling
 * Handles form data submission, validation, and processing
 */
class SubmissionsController extends BaseController
{
    protected $submissionModel;
    protected $formModel;
    protected $userModel;
    protected $accessModel;
    protected $auditModel;
    protected $mediaModel;

    public function __construct()
    {
        $this->submissionModel = new KoboSubmissionModel();
        $this->formModel = new KoboFormModel();
        $this->userModel = new KoboUserModel();
        $this->accessModel = new KoboFormAccessModel();
        $this->auditModel = new KoboAuditLogModel();
        $this->mediaModel = new KoboMediaFileModel();
    }

    /**
     * Submit form data (OpenRosa compliant)
     * POST /api/v1/submission
     */
    public function submit(): ResponseInterface
    {
        try {
            // Authenticate user
            $user = $this->authenticateUser();
            if (!$user) {
                return $this->respond([
                    'error' => 'Authentication required'
                ], 401, 'Unauthorized');
            }

            // Get submission data
            $xmlData = $this->getSubmissionXML();
            if (!$xmlData) {
                return $this->respond([
                    'error' => 'No XML data provided'
                ], 400);
            }

            // Parse XML to extract form ID and other metadata
            $submissionInfo = $this->parseSubmissionXML($xmlData);
            if (!$submissionInfo) {
                return $this->respond([
                    'error' => 'Invalid XML format'
                ], 400);
            }

            // Check form access
            if (!$this->accessModel->hasAccess($user['id'], $submissionInfo['form_id'], $submissionInfo['device_id'], 'write')) {
                $this->auditModel->logSecurityEvent(
                    'unauthorized_submission_attempt',
                    $user['id'],
                    ['form_id' => $submissionInfo['form_id'], 'device_id' => $submissionInfo['device_id']]
                );
                
                return $this->respond([
                    'error' => 'Access denied for this form'
                ], 403);
            }

            // Validate form exists and is active
            $form = $this->formModel->findByFormId($submissionInfo['form_id']);
            if (!$form || $form['status'] !== 'active') {
                return $this->respond([
                    'error' => 'Form not found or inactive'
                ], 404);
            }

            // Prepare submission data
            $submissionData = [
                'form_id' => $submissionInfo['form_id'],
                'user_id' => $user['id'],
                'device_id' => $submissionInfo['device_id'],
                'xml_data' => $xmlData,
                'submission_id' => $submissionInfo['submission_id'],
                'submitted_at' => date('Y-m-d H:i:s'),
                'status' => 'pending'
            ];

            // Create submission
            $submissionId = $this->submissionModel->createSubmission($submissionData);
            if (!$submissionId) {
                return $this->respond([
                    'error' => 'Failed to save submission'
                ], 500);
            }

            // Handle media files if present
            $this->processMediaFiles($submissionInfo['form_id'], $submissionInfo['submission_id'], $user['id']);

            // Log activity
            $this->auditModel->logSubmissionCreated($user['id'], $submissionId, $submissionData);

            // Build OpenRosa response
            $responseXML = $this->buildSubmissionResponse($submissionInfo['submission_id']);

            return $this->respond($responseXML, 201)
                        ->setContentType('text/xml; charset=utf-8')
                        ->setHeader('X-OpenRosa-Version', '1.0')
                        ->setHeader('Location', base_url("api/v1/submissions/{$submissionId}"));

        } catch (\Exception $e) {
            log_message('error', 'Submission error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Submit data for specific form
     * POST /api/v1/submissions/{formId}
     */
    public function submitToForm(string $formId): ResponseInterface
    {
        try {
            // Authenticate user
            $user = $this->authenticateUser();
            if (!$user) {
                return $this->respond([
                    'error' => 'Authentication required'
                ], 401);
            }

            // Check form access
            $deviceId = $this->request->getHeaderLine('X-Device-ID');
            if (!$this->accessModel->hasAccess($user['id'], $formId, $deviceId, 'write')) {
                return $this->respond([
                    'error' => 'Access denied for this form'
                ], 403);
            }

            // Get form
            $form = $this->formModel->findByFormId($formId);
            if (!$form || $form['status'] !== 'active') {
                return $this->respond([
                    'error' => 'Form not found or inactive'
                ], 404);
            }

            // Get JSON data
            $jsonData = $this->request->getJSON(true);
            if (!$jsonData) {
                return $this->respond([
                    'error' => 'No JSON data provided'
                ], 400);
            }

            // Convert JSON to XML
            $xmlData = $this->submissionModel->jsonToXml($jsonData, $formId);
            if (!$xmlData) {
                return $this->respond([
                    'error' => 'Failed to convert JSON to XML'
                ], 400);
            }

            // Create submission
            $submissionData = [
                'form_id' => $formId,
                'user_id' => $user['id'],
                'device_id' => $deviceId,
                'xml_data' => $xmlData,
                'json_data' => json_encode($jsonData),
                'submission_id' => $this->submissionModel->generateSubmissionId(),
                'submitted_at' => date('Y-m-d H:i:s'),
                'status' => 'pending'
            ];

            $submissionId = $this->submissionModel->createSubmission($submissionData);
            if (!$submissionId) {
                return $this->respond([
                    'error' => 'Failed to save submission'
                ], 500);
            }

            // Log activity
            $this->auditModel->logSubmissionCreated($user['id'], $submissionId, $submissionData);

            return $this->respond([
                'success' => true,
                'submission_id' => $submissionData['submission_id'],
                'id' => $submissionId,
                'message' => 'Submission saved successfully'
            ], 201);

        } catch (\Exception $e) {
            log_message('error', 'Form submission error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get submissions (with filters)
     * GET /api/v1/submissions
     */
    public function getSubmissions(): ResponseInterface
    {
        try {
            // Authenticate user
            $user = $this->authenticateUser();
            if (!$user) {
                return $this->respond([
                    'error' => 'Authentication required'
                ], 401);
            }

            // Get query parameters
            $filters = [
                'form_id' => $this->request->getGet('form_id'),
                'status' => $this->request->getGet('status'),
                'device_id' => $this->request->getGet('device_id'),
                'date_from' => $this->request->getGet('date_from'),
                'date_to' => $this->request->getGet('date_to'),
                'search' => $this->request->getGet('search')
            ];

            $perPage = (int) ($this->request->getGet('per_page') ?? 20);
            $page = (int) ($this->request->getGet('page') ?? 1);

            // Check access permissions for form filters
            if (!empty($filters['form_id'])) {
                if (!$this->accessModel->hasAccess($user['id'], $filters['form_id'])) {
                    return $this->respond([
                        'error' => 'Access denied for this form'
                    ], 403);
                }
            }

            // Get submissions with pagination
            $result = $this->submissionModel->getSubmissionsWithPagination($perPage, $filters);

            // Filter results based on user access
            $accessibleSubmissions = $this->filterSubmissionsByAccess($result['submissions'], $user['id']);

            return $this->respond([
                'success' => true,
                'data' => $accessibleSubmissions,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $result['pager']->getTotal()
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Get submissions error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get specific submission
     * GET /api/v1/submissions/{submissionId}
     */
    public function getSubmission(int $submissionId): ResponseInterface
    {
        try {
            // Authenticate user
            $user = $this->authenticateUser();
            if (!$user) {
                return $this->respond([
                    'error' => 'Authentication required'
                ], 401);
            }

            // Get submission
            $submission = $this->submissionModel->find($submissionId);
            if (!$submission) {
                return $this->respond([
                    'error' => 'Submission not found'
                ], 404);
            }

            // Check access
            if (!$this->accessModel->hasAccess($user['id'], $submission['form_id'])) {
                return $this->respond([
                    'error' => 'Access denied'
                ], 403);
            }

            // Get related media files
            $mediaFiles = $this->mediaModel->getFilesBySubmission($submission['submission_id']);

            $response = [
                'success' => true,
                'data' => $submission,
                'media_files' => $mediaFiles
            ];

            return $this->respond($response);

        } catch (\Exception $e) {
            log_message('error', 'Get submission error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Update submission status
     * PUT /api/v1/submissions/{submissionId}/status
     */
    public function updateSubmissionStatus(int $submissionId): ResponseInterface
    {
        try {
            // Authenticate user
            $user = $this->authenticateUser();
            if (!$user) {
                return $this->respond([
                    'error' => 'Authentication required'
                ], 401);
            }

            // Check manager/admin permissions
            if (!$this->userModel->isAdmin($user['id']) && !$this->userModel->isManager($user['id'])) {
                return $this->respond([
                    'error' => 'Manager or admin access required'
                ], 403);
            }

            $status = $this->request->getJSON(true)['status'] ?? '';
            if (!in_array($status, ['pending', 'processed', 'failed'])) {
                return $this->respond([
                    'error' => 'Invalid status. Must be pending, processed, or failed'
                ], 400);
            }

            // Get submission
            $submission = $this->submissionModel->find($submissionId);
            if (!$submission) {
                return $this->respond([
                    'error' => 'Submission not found'
                ], 404);
            }

            // Update status
            $success = false;
            switch ($status) {
                case 'processed':
                    $success = $this->submissionModel->markAsProcessed($submissionId);
                    break;
                case 'failed':
                    $success = $this->submissionModel->markAsFailed($submissionId);
                    break;
                default:
                    $success = $this->submissionModel->update($submissionId, ['status' => $status]);
            }

            if (!$success) {
                return $this->respond([
                    'error' => 'Failed to update submission status'
                ], 500);
            }

            // Log activity
            $this->auditModel->logActivity(
                $user['id'],
                'submission',
                'status_updated',
                $submissionId,
                ['old_status' => $submission['status'], 'new_status' => $status]
            );

            return $this->respond([
                'success' => true,
                'message' => "Submission status updated to {$status}"
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Submission status update error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Batch submission processing
     * POST /api/v1/submissions/batch
     */
    public function batchProcess(): ResponseInterface
    {
        try {
            // Authenticate user
            $user = $this->authenticateUser();
            if (!$user) {
                return $this->respond([
                    'error' => 'Authentication required'
                ], 401);
            }

            // Check admin permissions
            if (!$this->userModel->isAdmin($user['id'])) {
                return $this->respond([
                    'error' => 'Admin access required'
                ], 403);
            }

            // Process pending submissions
            $result = $this->submissionModel->processPendingSubmissions();

            // Log activity
            $this->auditModel->logSystemEvent('batch_processing_completed', $result);

            return $this->respond([
                'success' => true,
                'result' => $result,
                'message' => 'Batch processing completed'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Batch processing error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Export submissions
     * GET /api/v1/submissions/export
     */
    public function export(): ResponseInterface
    {
        try {
            // Authenticate user
            $user = $this->authenticateUser();
            if (!$user) {
                return $this->respond([
                    'error' => 'Authentication required'
                ], 401);
            }

            $format = $this->request->getGet('format') ?? 'csv';
            $formId = $this->request->getGet('form_id');

            // Check form access if specified
            if ($formId && !$this->accessModel->hasAccess($user['id'], $formId)) {
                return $this->respond([
                    'error' => 'Access denied for this form'
                ], 403);
            }

            switch ($format) {
                case 'csv':
                    $data = $this->submissionModel->exportToCSV($formId);
                    $filename = 'submissions_' . date('Y-m-d_H-i-s') . '.csv';
                    
                    return $this->response->download($filename, $data)
                                        ->setContentType('text/csv');
                
                default:
                    return $this->respond([
                        'error' => 'Unsupported export format'
                    ], 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Export error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get submission XML from request
     */
    private function getSubmissionXML(): string|false
    {
        // Try multipart form data first (common for Kobo Collect)
        $xmlContent = $this->request->getPost('xml_submission_file');
        if ($xmlContent) {
            return $xmlContent;
        }

        // Try raw XML body
        $rawInput = $this->request->getBody();
        if (!empty($rawInput) && str_contains($this->request->getHeaderLine('Content-Type'), 'xml')) {
            return $rawInput;
        }

        // Try file upload
        $file = $this->request->getFile('xml_submission_file');
        if ($file && $file->isValid()) {
            return file_get_contents($file->getTempName());
        }

        return false;
    }

    /**
     * Parse submission XML to extract metadata
     */
    private function parseSubmissionXML(string $xmlData): array|false
    {
        try {
            $xml = simplexml_load_string($xmlData);
            if ($xml === false) {
                return false;
            }

            // Extract metadata
            $metadata = [
                'form_id' => null,
                'submission_id' => null,
                'device_id' => null
            ];

            // Get form ID from root element or meta
            if (isset($xml->meta->instanceID)) {
                $instanceId = (string)$xml->meta->instanceID;
                $metadata['submission_id'] = $instanceId;
            }

            // Try to get form ID from various places
            if (isset($xml['id'])) {
                $metadata['form_id'] = (string)$xml['id'];
            } elseif (isset($xml->meta->formID)) {
                $metadata['form_id'] = (string)$xml->meta->formID;
            } else {
                // Use root element name as form ID
                $metadata['form_id'] = $xml->getName();
            }

            // Get device ID
            if (isset($xml->meta->deviceID)) {
                $metadata['device_id'] = (string)$xml->meta->deviceID;
            } else {
                $metadata['device_id'] = $this->request->getHeaderLine('X-Device-ID');
            }

            return $metadata;

        } catch (\Exception $e) {
            log_message('error', 'XML parsing error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Process uploaded media files
     */
    private function processMediaFiles(string $formId, string $submissionId, int $userId): void
    {
        $files = $this->request->getFiles();
        
        foreach ($files as $fieldName => $file) {
            if ($fieldName !== 'xml_submission_file' && $file->isValid()) {
                $this->mediaModel->uploadFile($file, $formId, $submissionId, $userId);
            }
        }
    }

    /**
     * Build OpenRosa submission response
     */
    private function buildSubmissionResponse(string $submissionId): string
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><OpenRosaResponse xmlns="http://openrosa.org/http/response"></OpenRosaResponse>');
        
        $xml->addChild('message', 'Submission received successfully');
        $xml->addChild('submissionMetadata');
        $xml->submissionMetadata->addChild('instanceID', $submissionId);
        
        return $xml->asXML();
    }

    /**
     * Filter submissions by user access
     */
    private function filterSubmissionsByAccess(array $submissions, int $userId): array
    {
        $filtered = [];
        
        foreach ($submissions as $submission) {
            if ($this->accessModel->hasAccess($userId, $submission['form_id'])) {
                $filtered[] = $submission;
            }
        }
        
        return $filtered;
    }

    /**
     * Authenticate user (shared with FormsController)
     */
    private function authenticateUser(): array|false
    {
        // Try JWT authentication first
        $jwt = $this->getJWTFromHeader();
        if ($jwt) {
            return $this->validateJWT($jwt);
        }

        // Fall back to Basic Authentication
        $authHeader = $this->request->getHeader('Authorization');
        if (!$authHeader || !str_starts_with($authHeader->getValue(), 'Basic ')) {
            return false;
        }

        $credentials = base64_decode(substr($authHeader->getValue(), 6));
        $parts = explode(':', $credentials, 2);
        
        if (count($parts) !== 2) {
            return false;
        }

        [$username, $password] = $parts;
        return $this->userModel->authenticateUser($username, $password);
    }

    /**
     * Get JWT token from Authorization header
     */
    private function getJWTFromHeader(): string|false
    {
        $authHeader = $this->request->getHeader('Authorization');
        if (!$authHeader || !str_starts_with($authHeader->getValue(), 'Bearer ')) {
            return false;
        }

        return substr($authHeader->getValue(), 7);
    }

    /**
     * Validate JWT token (placeholder)
     */
    private function validateJWT(string $token): array|false
    {
        // TODO: Implement JWT validation
        return false;
    }
}