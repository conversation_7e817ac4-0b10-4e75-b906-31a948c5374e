<?= $this->extend('admin/layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2 fw-bold">
        <i class="bi bi-people text-primary me-2"></i>
        User Management
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button class="btn btn-sm btn-outline-secondary" id="refreshUsers">
                <i class="bi bi-arrow-clockwise me-1"></i>
                Refresh
            </button>
            <button class="btn btn-sm btn-outline-primary" onclick="exportUsers()">
                <i class="bi bi-download me-1"></i>
                Export
            </button>
        </div>
        <a href="<?= base_url('admin/kobo/users/create') ?>" class="btn btn-sm btn-primary">
            <i class="bi bi-person-plus me-1"></i>
            Add New User
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card primary h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Total Users</div>
                        <div class="h3 fw-bold"><?= $stats['total'] ?? 0 ?></div>
                    </div>
                    <i class="bi bi-people align-self-center" style="font-size: 2rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card success h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Active Users</div>
                        <div class="h3 fw-bold"><?= $stats['active'] ?? 0 ?></div>
                    </div>
                    <i class="bi bi-person-check align-self-center" style="font-size: 2rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card warning h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Administrators</div>
                        <div class="h3 fw-bold"><?= $stats['by_role']['admin']['active'] ?? 0 ?></div>
                    </div>
                    <i class="bi bi-shield-check align-self-center" style="font-size: 2rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card info h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Enumerators</div>
                        <div class="h3 fw-bold"><?= $stats['by_role']['enumerator']['active'] ?? 0 ?></div>
                    </div>
                    <i class="bi bi-person-workspace align-self-center" style="font-size: 2rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3" id="filterForm">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Users</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= $search ?? '' ?>" placeholder="Search by username or email...">
            </div>
            <div class="col-md-2">
                <label for="role" class="form-label">Role</label>
                <select class="form-select" id="role" name="role">
                    <option value="">All Roles</option>
                    <option value="admin" <?= ($filters['role'] ?? '') === 'admin' ? 'selected' : '' ?>>Admin</option>
                    <option value="manager" <?= ($filters['role'] ?? '') === 'manager' ? 'selected' : '' ?>>Manager</option>
                    <option value="enumerator" <?= ($filters['role'] ?? '') === 'enumerator' ? 'selected' : '' ?>>Enumerator</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="active" <?= ($filters['status'] ?? '') === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= ($filters['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="sort" class="form-label">Sort By</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="created_at" <?= ($filters['sort'] ?? '') === 'created_at' ? 'selected' : '' ?>>Recent</option>
                    <option value="username" <?= ($filters['sort'] ?? '') === 'username' ? 'selected' : '' ?>>Username</option>
                    <option value="role" <?= ($filters['sort'] ?? '') === 'role' ? 'selected' : '' ?>>Role</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>
                        Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            Users List
        </h5>
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-secondary" onclick="selectAll()">
                <i class="bi bi-check-all me-1"></i>
                Select All
            </button>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success dropdown-toggle" 
                        data-bs-toggle="dropdown" id="bulkActionsBtn" style="display: none;">
                    <i class="bi bi-gear me-1"></i>
                    Bulk Actions
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="bulkAction('activate')">
                        <i class="bi bi-check-circle me-2"></i>Activate Selected
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="bulkAction('deactivate')">
                        <i class="bi bi-x-circle me-2"></i>Deactivate Selected
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">
                        <i class="bi bi-trash me-2"></i>Delete Selected
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($users)): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                            </th>
                            <th>User Info</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Activity</th>
                            <th>Joined</th>
                            <th width="200">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input row-select" value="<?= $user['id'] ?>">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-primary text-white me-3">
                                            <?= strtoupper(substr($user['username'], 0, 2)) ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?= esc($user['username']) ?></div>
                                            <small class="text-muted"><?= esc($user['email']) ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $roleColors = [
                                        'admin' => 'danger',
                                        'manager' => 'warning',
                                        'enumerator' => 'info'
                                    ];
                                    $roleIcons = [
                                        'admin' => 'shield-check',
                                        'manager' => 'person-gear',
                                        'enumerator' => 'person-workspace'
                                    ];
                                    $roleColor = $roleColors[$user['role']] ?? 'secondary';
                                    $roleIcon = $roleIcons[$user['role']] ?? 'person';
                                    ?>
                                    <span class="badge bg-<?= $roleColor ?>">
                                        <i class="bi bi-<?= $roleIcon ?> me-1"></i>
                                        <?= ucfirst($user['role']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $statusColor = $user['status'] === 'active' ? 'success' : 'secondary';
                                    $statusIcon = $user['status'] === 'active' ? 'check-circle' : 'x-circle';
                                    ?>
                                    <span class="badge bg-<?= $statusColor ?>">
                                        <i class="bi bi-<?= $statusIcon ?> me-1"></i>
                                        <?= ucfirst($user['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php
                                        $lastActivity = $user['last_activity'] ?? null;
                                        if ($lastActivity) {
                                            echo 'Last: ' . date('M j, H:i', strtotime($lastActivity));
                                        } else {
                                            echo 'No activity';
                                        }
                                        ?>
                                    </small>
                                </td>
                                <td>
                                    <small class="text-muted"><?= date('M j, Y', strtotime($user['created_at'])) ?></small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm table-actions">
                                        <button class="btn btn-outline-primary btn-sm" 
                                                onclick="viewUser(<?= $user['id'] ?>)" 
                                                title="View Details">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" 
                                                onclick="editUser(<?= $user['id'] ?>)" 
                                                title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-outline-info btn-sm dropdown-toggle" 
                                                    data-bs-toggle="dropdown" title="More Actions">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="resetPassword(<?= $user['id'] ?>)">
                                                    <i class="bi bi-key me-2"></i>Reset Password
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="changeRole(<?= $user['id'] ?>, '<?= $user['role'] ?>')">
                                                    <i class="bi bi-person-gear me-2"></i>Change Role
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="viewActivity(<?= $user['id'] ?>)">
                                                    <i class="bi bi-clock-history me-2"></i>View Activity
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <?php if ($user['status'] === 'active'): ?>
                                                    <li><a class="dropdown-item text-warning" href="#" onclick="deactivateUser(<?= $user['id'] ?>)">
                                                        <i class="bi bi-x-circle me-2"></i>Deactivate
                                                    </a></li>
                                                <?php else: ?>
                                                    <li><a class="dropdown-item text-success" href="#" onclick="activateUser(<?= $user['id'] ?>)">
                                                        <i class="bi bi-check-circle me-2"></i>Activate
                                                    </a></li>
                                                <?php endif; ?>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteUser(<?= $user['id'] ?>)">
                                                    <i class="bi bi-trash me-2"></i>Delete
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="bi bi-people" style="font-size: 4rem; color: #dee2e6;"></i>
                <h5 class="mt-3 text-muted">No Users Found</h5>
                <p class="text-muted">Add your first user to get started with user management.</p>
                <a href="<?= base_url('admin/kobo/users/create') ?>" class="btn btn-primary">
                    <i class="bi bi-person-plus me-2"></i>
                    Add User
                </a>
            </div>
        <?php endif; ?>
    </div>
    
    <?php if (isset($pager)): ?>
        <div class="card-footer">
            <?= $pager->links() ?>
        </div>
    <?php endif; ?>
</div>

<!-- Role Change Modal -->
<div class="modal fade" id="roleChangeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-gear me-2"></i>
                    Change User Role
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="roleChangeForm">
                    <input type="hidden" id="roleUserId">
                    <div class="mb-3">
                        <label for="newRole" class="form-label">Select New Role</label>
                        <select class="form-select" id="newRole" required>
                            <option value="">Select Role</option>
                            <option value="admin">Administrator</option>
                            <option value="manager">Manager</option>
                            <option value="enumerator">Enumerator</option>
                        </select>
                        <div class="form-text">
                            <strong>Admin:</strong> Full system access<br>
                            <strong>Manager:</strong> Form and user management<br>
                            <strong>Enumerator:</strong> Data collection only
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="confirmRoleChange()">
                    <i class="bi bi-check me-1"></i>
                    Change Role
                </button>
            </div>
        </div>
    </div>
</div>

<!-- User Activity Modal -->
<div class="modal fade" id="userActivityModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-clock-history me-2"></i>
                    User Activity Timeline
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="activityTimeline">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
    }
</style>

<script>
    // Selection management
    let selectedUsers = [];

    document.addEventListener('DOMContentLoaded', function() {
        initializeTableSelection();
        initializeUserActions();
    });

    function initializeTableSelection() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const rowCheckboxes = document.querySelectorAll('.row-select');

        selectAllCheckbox?.addEventListener('change', function() {
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedUsers();
        });

        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedUsers);
        });
    }

    function updateSelectedUsers() {
        const checkboxes = document.querySelectorAll('.row-select:checked');
        selectedUsers = Array.from(checkboxes).map(cb => parseInt(cb.value));
        
        const bulkActionsBtn = document.getElementById('bulkActionsBtn');
        if (selectedUsers.length > 0) {
            bulkActionsBtn.style.display = 'block';
            bulkActionsBtn.innerHTML = `<i class="bi bi-gear me-1"></i>Bulk Actions (${selectedUsers.length})`;
        } else {
            bulkActionsBtn.style.display = 'none';
        }
    }

    function selectAll() {
        document.getElementById('selectAllCheckbox').checked = true;
        document.getElementById('selectAllCheckbox').dispatchEvent(new Event('change'));
    }

    function initializeUserActions() {
        // Refresh users
        document.getElementById('refreshUsers').addEventListener('click', function() {
            window.location.reload();
        });

        // Auto-submit filter form on change
        ['role', 'status', 'sort'].forEach(id => {
            document.getElementById(id)?.addEventListener('change', function() {
                document.getElementById('filterForm').submit();
            });
        });
    }

    // User actions
    function viewUser(userId) {
        window.location.href = `<?= base_url('admin/kobo/users') ?>/${userId}`;
    }

    function editUser(userId) {
        window.location.href = `<?= base_url('admin/kobo/users') ?>/${userId}/edit`;
    }

    function deleteUser(userId) {
        if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            ajaxRequest(`<?= base_url('admin/kobo/users') ?>/${userId}`, 'DELETE', null,
                function(data) {
                    if (data.success) {
                        showToast('User deleted successfully', 'success');
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showToast('Failed to delete user: ' + (data.message || 'Unknown error'), 'danger');
                    }
                },
                function(error) {
                    showToast('Delete request failed', 'danger');
                }
            );
        }
    }

    function activateUser(userId) {
        ajaxRequest(`<?= base_url('admin/kobo/users') ?>/${userId}/activate`, 'POST', null,
            function(data) {
                if (data.success) {
                    showToast('User activated successfully', 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast('Failed to activate user: ' + (data.message || 'Unknown error'), 'danger');
                }
            }
        );
    }

    function deactivateUser(userId) {
        if (confirm('Are you sure you want to deactivate this user?')) {
            ajaxRequest(`<?= base_url('admin/kobo/users') ?>/${userId}/deactivate`, 'POST', null,
                function(data) {
                    if (data.success) {
                        showToast('User deactivated successfully', 'success');
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showToast('Failed to deactivate user: ' + (data.message || 'Unknown error'), 'danger');
                    }
                }
            );
        }
    }

    function resetPassword(userId) {
        if (confirm('Are you sure you want to reset this user\'s password? A new temporary password will be generated.')) {
            ajaxRequest(`<?= base_url('admin/kobo/users') ?>/${userId}/reset-password`, 'POST', null,
                function(data) {
                    if (data.success) {
                        alert(`Password reset successfully!\nTemporary password: ${data.temp_password}\n\nPlease provide this password to the user securely.`);
                        showToast('Password reset successfully', 'success');
                    } else {
                        showToast('Failed to reset password: ' + (data.message || 'Unknown error'), 'danger');
                    }
                }
            );
        }
    }

    function changeRole(userId, currentRole) {
        document.getElementById('roleUserId').value = userId;
        document.getElementById('newRole').value = '';
        
        // Remove current role from options temporarily
        const options = document.querySelectorAll('#newRole option');
        options.forEach(option => {
            if (option.value === currentRole) {
                option.disabled = true;
                option.textContent += ' (Current)';
            } else {
                option.disabled = false;
                option.textContent = option.textContent.replace(' (Current)', '');
            }
        });
        
        const modal = new bootstrap.Modal(document.getElementById('roleChangeModal'));
        modal.show();
    }

    function confirmRoleChange() {
        const userId = document.getElementById('roleUserId').value;
        const newRole = document.getElementById('newRole').value;
        
        if (!newRole) {
            showToast('Please select a role', 'warning');
            return;
        }
        
        ajaxRequest(`<?= base_url('admin/kobo/users') ?>/${userId}/role`, 'PUT', {
            role: newRole
        },
        function(data) {
            if (data.success) {
                showToast('User role updated successfully', 'success');
                setTimeout(() => window.location.reload(), 1000);
                bootstrap.Modal.getInstance(document.getElementById('roleChangeModal')).hide();
            } else {
                showToast('Failed to update role: ' + (data.message || 'Unknown error'), 'danger');
            }
        });
    }

    function viewActivity(userId) {
        const modal = new bootstrap.Modal(document.getElementById('userActivityModal'));
        
        // Load user activity
        ajaxRequest(`<?= base_url('admin/kobo/users') ?>/${userId}/activity`, 'GET', null,
            function(data) {
                if (data.success) {
                    renderActivityTimeline(data.activities);
                } else {
                    document.getElementById('activityTimeline').innerHTML = 
                        '<div class="alert alert-danger">Failed to load activity</div>';
                }
            },
            function(error) {
                document.getElementById('activityTimeline').innerHTML = 
                    '<div class="alert alert-danger">Error loading activity</div>';
            }
        );
        
        modal.show();
    }

    function renderActivityTimeline(activities) {
        const timeline = document.getElementById('activityTimeline');
        
        if (activities.length === 0) {
            timeline.innerHTML = '<div class="text-center text-muted">No activity found</div>';
            return;
        }
        
        let html = '<div class="timeline">';
        activities.forEach(activity => {
            html += `
                <div class="timeline-item border-start border-2 border-primary ps-3 pb-3">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="mb-1">${activity.action}</h6>
                            <p class="mb-1 text-muted small">${activity.entity_type}</p>
                            <small class="text-muted">${activity.created_at}</small>
                        </div>
                        <small class="text-muted">${activity.ip_address || 'N/A'}</small>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        
        timeline.innerHTML = html;
    }

    function bulkAction(action) {
        if (selectedUsers.length === 0) {
            showToast('Please select users first', 'warning');
            return;
        }

        let confirmMessage = '';
        switch(action) {
            case 'activate':
                confirmMessage = `Are you sure you want to activate ${selectedUsers.length} selected users?`;
                break;
            case 'deactivate':
                confirmMessage = `Are you sure you want to deactivate ${selectedUsers.length} selected users?`;
                break;
            case 'delete':
                confirmMessage = `Are you sure you want to delete ${selectedUsers.length} selected users? This action cannot be undone.`;
                break;
            default:
                showToast('Unknown bulk action', 'danger');
                return;
        }

        if (confirm(confirmMessage)) {
            ajaxRequest('<?= base_url('admin/kobo/users/bulk') ?>', 'POST', {
                operation: action,
                user_ids: selectedUsers
            },
            function(data) {
                if (data.success) {
                    showToast(data.message, 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast('Bulk operation failed: ' + (data.message || 'Unknown error'), 'danger');
                }
            });
        }
    }

    function exportUsers() {
        const filters = new URLSearchParams(window.location.search);
        window.open(`<?= base_url('admin/kobo/users/export') ?>?${filters.toString()}`, '_blank');
    }
</script>
<?= $this->endSection() ?>