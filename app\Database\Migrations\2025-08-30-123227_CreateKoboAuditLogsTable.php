<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateKoboAuditLogsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'action' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'target_type' => [
                'type' => 'ENUM',
                'constraint' => ['form', 'submission', 'user', 'system'],
            ],
            'target_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 45,
                'null' => true,
            ],
            'user_agent' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
        ]);
        
        $this->forge->addPrimaryKey('id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('action');
        $this->forge->addKey('target_type');
        $this->forge->addKey('created_at');
        $this->forge->addForeignKey('user_id', 'kobo_users', 'id', 'SET NULL', 'CASCADE');
        $this->forge->createTable('kobo_audit_logs');
    }

    public function down()
    {
        $this->forge->dropTable('kobo_audit_logs');
    }
}
