<?= $this->extend('admin/layout/main') ?>

<?= $this->section('title') ?>Data Export API<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-download me-2"></i>JSON API Data Export
                </h5>
            </div>
            <div class="card-body">
                <!-- API Endpoints Overview -->
                <div class="row">
                    <div class="col-lg-6">
                        <h6>Available Export Endpoints</h6>
                        <div class="list-group mb-4">
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Submissions Export</h6>
                                    <small class="text-success">GET</small>
                                </div>
                                <p class="mb-1">/api/v1/export/submissions</p>
                                <small>Export submission data with pagination and filtering</small>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Forms Export</h6>
                                    <small class="text-success">GET</small>
                                </div>
                                <p class="mb-1">/api/v1/export/forms</p>
                                <small>Export form definitions with metadata</small>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Statistics Export</h6>
                                    <small class="text-success">GET</small>
                                </div>
                                <p class="mb-1">/api/v1/export/statistics</p>
                                <small>Export aggregated statistics and analytics</small>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Real-time Sync</h6>
                                    <small class="text-info">GET</small>
                                </div>
                                <p class="mb-1">/api/v1/export/sync</p>
                                <small>Real-time data synchronization endpoint</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <h6>API Features</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Pagination support (configurable page sizes)</li>
                            <li><i class="fas fa-check text-success me-2"></i>Advanced filtering by form, user, status, dates</li>
                            <li><i class="fas fa-check text-success me-2"></i>Custom field selection</li>
                            <li><i class="fas fa-check text-success me-2"></i>Multiple sorting options</li>
                            <li><i class="fas fa-check text-success me-2"></i>JSON data flattening</li>
                            <li><i class="fas fa-check text-success me-2"></i>Metadata inclusion</li>
                            <li><i class="fas fa-check text-success me-2"></i>Real-time data synchronization</li>
                            <li><i class="fas fa-check text-success me-2"></i>Intelligent caching with bypass option</li>
                            <li><i class="fas fa-check text-success me-2"></i>JWT authentication required</li>
                            <li><i class="fas fa-check text-success me-2"></i>Rate limiting protection</li>
                        </ul>
                    </div>
                </div>

                <!-- API Testing Interface -->
                <div class="row">
                    <div class="col-12">
                        <h6>API Testing Interface</h6>
                        <div class="card">
                            <div class="card-body">
                                <form id="apiTestForm">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="apiEndpoint" class="form-label">Endpoint</label>
                                            <select class="form-select" id="apiEndpoint" name="endpoint">
                                                <option value="submissions">Submissions Export</option>
                                                <option value="forms">Forms Export</option>
                                                <option value="statistics">Statistics Export</option>
                                                <option value="sync">Real-time Sync</option>
                                                <option value="health">Health Check</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="pageSize" class="form-label">Page Size</label>
                                            <select class="form-select" id="pageSize" name="page_size">
                                                <option value="10">10</option>
                                                <option value="25" selected>25</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="realTime" class="form-label">Real-time</label>
                                            <select class="form-select" id="realTime" name="real_time">
                                                <option value="false">Use Cache</option>
                                                <option value="true">Real-time (no cache)</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="row mt-3">
                                        <div class="col-md-4">
                                            <label for="includeMetadata" class="form-label">Include Metadata</label>
                                            <select class="form-select" id="includeMetadata" name="include_metadata">
                                                <option value="true" selected>Yes</option>
                                                <option value="false">No</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="sortBy" class="form-label">Sort By</label>
                                            <select class="form-select" id="sortBy" name="sort_by">
                                                <option value="created_at" selected>Created Date</option>
                                                <option value="updated_at">Updated Date</option>
                                                <option value="id">ID</option>
                                                <option value="title">Title</option>
                                                <option value="status">Status</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="sortOrder" class="form-label">Sort Order</label>
                                            <select class="form-select" id="sortOrder" name="sort_order">
                                                <option value="DESC" selected>Descending</option>
                                                <option value="ASC">Ascending</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-primary" onclick="testAPI()">
                                            <i class="fas fa-play me-1"></i>Test API
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="viewDocs()">
                                            <i class="fas fa-book me-1"></i>View Documentation
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="checkHealth()">
                                            <i class="fas fa-heartbeat me-1"></i>Health Check
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API Response Display -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h6>API Response</h6>
                        <div class="card">
                            <div class="card-body">
                                <div id="apiResponse" class="bg-light p-3" style="min-height: 200px; font-family: monospace;">
                                    <em class="text-muted">API response will appear here...</em>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Example API Calls -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h6>Example API Calls</h6>
                        <div class="accordion" id="exampleAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="exampleSubmissions">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSubmissions">
                                        Submissions Export Examples
                                    </button>
                                </h2>
                                <div id="collapseSubmissions" class="accordion-collapse collapse" data-bs-parent="#exampleAccordion">
                                    <div class="accordion-body">
                                        <pre class="bg-dark text-light p-3"><code># Basic submissions export
GET /api/v1/export/submissions?page=1&page_size=25

# Filtered submissions export
GET /api/v1/export/submissions?form_id=1&status=processed&date_from=2024-01-01

# Custom fields and flattened JSON
GET /api/v1/export/submissions?fields=id,status,data&flatten_json=true

# Real-time data (no cache)
GET /api/v1/export/submissions?real_time=true&include_metadata=true</code></pre>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="exampleSync">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSync">
                                        Real-time Sync Examples
                                    </button>
                                </h2>
                                <div id="collapseSync" class="accordion-collapse collapse" data-bs-parent="#exampleAccordion">
                                    <div class="accordion-body">
                                        <pre class="bg-dark text-light p-3"><code># Get changes since timestamp
GET /api/v1/export/sync?since_timestamp=1693123200

# Sync specific entity types
GET /api/v1/export/sync?entity_types=submissions,forms&limit=50

# Continuous sync workflow
1. GET /api/v1/export/sync?since_timestamp=0
2. Process changes
3. GET /api/v1/export/sync?since_timestamp=LAST_SYNC_TIMESTAMP
4. Repeat step 2-3</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testAPI() {
    const form = document.getElementById('apiTestForm');
    const formData = new FormData(form);
    const params = new URLSearchParams();
    
    for (const [key, value] of formData.entries()) {
        if (value && value !== '') {
            params.append(key, value);
        }
    }
    
    const endpoint = document.getElementById('apiEndpoint').value;
    const url = `/api/v1/export/${endpoint}?${params.toString()}`;
    
    document.getElementById('apiResponse').innerHTML = '<div class="text-info"><i class="fas fa-spinner fa-spin me-2"></i>Loading...</div>';
    
    fetch(url, {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + (localStorage.getItem('jwt_token') || 'YOUR_JWT_TOKEN'),
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('apiResponse').innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="badge bg-${data.success ? 'success' : 'danger'}">
                    ${data.success ? 'Success' : 'Error'}
                </span>
                <small class="text-muted">URL: ${url}</small>
            </div>
            <pre class="mb-0">${JSON.stringify(data, null, 2)}</pre>
        `;
    })
    .catch(error => {
        document.getElementById('apiResponse').innerHTML = `
            <div class="alert alert-danger">
                <strong>Error:</strong> ${error.message}
            </div>
        `;
    });
}

function viewDocs() {
    window.open('/api/v1/export/docs', '_blank');
}

function checkHealth() {
    document.getElementById('apiResponse').innerHTML = '<div class="text-info"><i class="fas fa-spinner fa-spin me-2"></i>Checking health...</div>';
    
    fetch('/api/v1/export/health', {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        const statusBadge = data.status === 'healthy' ? 'success' : 'warning';
        document.getElementById('apiResponse').innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="badge bg-${statusBadge}">
                    System Status: ${data.status}
                </span>
                <small class="text-muted">Last Check: ${data.timestamp}</small>
            </div>
            <pre class="mb-0">${JSON.stringify(data, null, 2)}</pre>
        `;
    })
    .catch(error => {
        document.getElementById('apiResponse').innerHTML = `
            <div class="alert alert-danger">
                <strong>Health Check Failed:</strong> ${error.message}
            </div>
        `;
    });
}

// Auto-update example URL when form changes
document.getElementById('apiTestForm').addEventListener('change', function() {
    const form = document.getElementById('apiTestForm');
    const formData = new FormData(form);
    const params = new URLSearchParams();
    
    for (const [key, value] of formData.entries()) {
        if (value && value !== '' && key !== 'endpoint') {
            params.append(key, value);
        }
    }
    
    const endpoint = document.getElementById('apiEndpoint').value;
    const exampleUrl = `/api/v1/export/${endpoint}?${params.toString()}`;
    
    // Update example display if needed
    console.log('Example URL:', exampleUrl);
});
</script>
<?= $this->endSection() ?>