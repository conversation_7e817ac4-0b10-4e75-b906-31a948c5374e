<?= $this->extend('admin/layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2 fw-bold">
        <i class="bi bi-plus-circle text-primary me-2"></i>
        <?= isset($form) ? 'Edit Form' : 'Upload New Form' ?>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="<?= base_url('admin/kobo/forms') ?>" class="btn btn-sm btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i>
            Back to Forms
        </a>
    </div>
</div>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?= session()->getFlashdata('success') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-file-earmark-text me-2"></i>
                    <?= isset($form) ? 'Form Details' : 'Form Upload' ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="post" action="<?= isset($form) ? base_url('admin/kobo/forms/'.$form['id']) : base_url('admin/kobo/forms') ?>" enctype="multipart/form-data">
                    <?= csrf_field() ?>
                    <?php if (isset($form)): ?>
                        <input type="hidden" name="form_id" value="<?= $form['id'] ?>">
                    <?php endif; ?>
                    
                    <div class="row g-3">
                        <div class="col-md-8">
                            <label for="form_name" class="form-label">Form Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="form_name" name="form_name" 
                                   value="<?= $form['form_name'] ?? '' ?>" required>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="form_status" class="form-label">Status</label>
                            <select class="form-select" id="form_status" name="status">
                                <option value="draft" <?= ($form['status'] ?? 'draft') === 'draft' ? 'selected' : '' ?>>Draft</option>
                                <option value="active" <?= ($form['status'] ?? '') === 'active' ? 'selected' : '' ?>>Active</option>
                                <option value="inactive" <?= ($form['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>
                        
                        <?php if (!isset($form)): ?>
                        <div class="col-12">
                            <label for="xml_file" class="form-label">XForm XML File <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="xml_file" name="xml_file" 
                                   accept=".xml" required>
                            <div class="form-text">
                                Upload an XForm XML file (.xml)
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (isset($form)): ?>
                        <div class="col-12">
                            <label for="xml_content" class="form-label">XML Content</label>
                            <textarea class="form-control" id="xml_content" name="xml_content" rows="10" 
                                      style="font-family: monospace; font-size: 0.9em;"><?= htmlspecialchars($form['xml_content'] ?? '') ?></textarea>
                            <div class="form-text">
                                You can edit the XML content directly here
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-save me-1"></i>
                                <?= isset($form) ? 'Update Form' : 'Upload Form' ?>
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                                <i class="bi bi-x-circle me-1"></i>
                                Cancel
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle me-1"></i>
                    Upload Guidelines
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        Upload valid XForm XML files only
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        File size should be less than 10MB
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        Ensure XML is well-formed
                    </li>
                    <li class="mb-0">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        Use descriptive form names
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Simple file validation
        const fileInput = document.getElementById('xml_file');
        if (fileInput) {
            fileInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    // Validate file type
                    if (!file.name.toLowerCase().endsWith('.xml')) {
                        alert('Please select an XML file');
                        this.value = '';
                        return;
                    }
                    
                    // Validate file size (10MB)
                    if (file.size > 10 * 1024 * 1024) {
                        alert('File size must be less than 10MB');
                        this.value = '';
                        return;
                    }
                }
            });
        }
    });
</script>
<?= $this->endSection() ?>
