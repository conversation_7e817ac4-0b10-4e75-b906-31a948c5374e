<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateKoboFormAccessTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'form_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'device_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'access_type' => [
                'type' => 'ENUM',
                'constraint' => ['download', 'submit'],
            ],
            'granted_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
        ]);
        
        $this->forge->addPrimaryKey('id');
        $this->forge->addKey('form_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('device_id');
        $this->forge->addForeignKey('form_id', 'kobo_forms', 'form_id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('user_id', 'kobo_users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('kobo_form_access');
    }

    public function down()
    {
        $this->forge->dropTable('kobo_form_access');
    }
}
