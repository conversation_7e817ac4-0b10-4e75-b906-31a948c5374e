<?php

namespace App\Models\Kobo;

use CodeIgniter\Model;

/**
 * Kobo Audit Log Model
 * 
 * Handles audit logging functionality, activity tracking, and log filtering/search
 */
class KoboAuditLogModel extends Model
{
    protected $table = 'kobo_audit_logs';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id',
        'entity_type',
        'entity_id',
        'action',
        'old_data',
        'new_data',
        'ip_address',
        'user_agent',
        'created_at'
    ];

    // Dates
    protected $useTimestamps = false; // We'll handle created_at manually
    protected $dateFormat = 'datetime';

    // Validation
    protected $validationRules = [
        'entity_type' => 'required|string|max_length[100]',
        'action' => 'required|string|max_length[100]',
        'ip_address' => 'permit_empty|valid_ip',
        'user_agent' => 'permit_empty|string|max_length[500]'
    ];

    protected $validationMessages = [
        'entity_type' => [
            'required' => 'Entity type is required',
            'string' => 'Entity type must be a valid string',
            'max_length' => 'Entity type cannot exceed 100 characters'
        ],
        'action' => [
            'required' => 'Action is required',
            'string' => 'Action must be a valid string',
            'max_length' => 'Action cannot exceed 100 characters'
        ],
        'ip_address' => [
            'valid_ip' => 'IP address must be valid'
        ],
        'user_agent' => [
            'string' => 'User agent must be a valid string',
            'max_length' => 'User agent cannot exceed 500 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['setTimestamp', 'setRequestInfo'];

    /**
     * Set timestamp before inserting
     */
    protected function setTimestamp(array $data)
    {
        if (!isset($data['data']['created_at'])) {
            $data['data']['created_at'] = date('Y-m-d H:i:s');
        }
        return $data;
    }

    /**
     * Set request information before inserting
     */
    protected function setRequestInfo(array $data)
    {
        $request = \Config\Services::request();
        
        if (!isset($data['data']['ip_address'])) {
            $data['data']['ip_address'] = $request->getIPAddress();
        }
        
        if (!isset($data['data']['user_agent'])) {
            $data['data']['user_agent'] = $request->getUserAgent()->getAgentString();
        }

        return $data;
    }

    /**
     * Log user activity
     */
    public function logActivity(int $userId = null, string $entityType, string $action, int $entityId = null, array $data = []): int|false
    {
        $logData = [
            'user_id' => $userId,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'action' => $action,
            'old_data' => isset($data['old_data']) ? json_encode($data['old_data']) : null,
            'new_data' => isset($data['new_data']) ? json_encode($data['new_data']) : null,
        ];

        return $this->insert($logData);
    }

    /**
     * Log user login
     */
    public function logLogin(int $userId, bool $successful = true): int|false
    {
        $action = $successful ? 'login_success' : 'login_failed';
        return $this->logActivity($userId, 'user_auth', $action);
    }

    /**
     * Log user logout
     */
    public function logLogout(int $userId): int|false
    {
        return $this->logActivity($userId, 'user_auth', 'logout');
    }

    /**
     * Log form creation
     */
    public function logFormCreated(int $userId, int $formId, array $formData): int|false
    {
        return $this->logActivity($userId, 'form', 'created', $formId, ['new_data' => $formData]);
    }

    /**
     * Log form update
     */
    public function logFormUpdated(int $userId, int $formId, array $oldData, array $newData): int|false
    {
        return $this->logActivity($userId, 'form', 'updated', $formId, [
            'old_data' => $oldData,
            'new_data' => $newData
        ]);
    }

    /**
     * Log form deletion
     */
    public function logFormDeleted(int $userId, int $formId, array $formData): int|false
    {
        return $this->logActivity($userId, 'form', 'deleted', $formId, ['old_data' => $formData]);
    }

    /**
     * Log submission creation
     */
    public function logSubmissionCreated(int $userId = null, int $submissionId, array $submissionData): int|false
    {
        return $this->logActivity($userId, 'submission', 'created', $submissionId, ['new_data' => $submissionData]);
    }

    /**
     * Log submission processing
     */
    public function logSubmissionProcessed(int $submissionId): int|false
    {
        return $this->logActivity(null, 'submission', 'processed', $submissionId);
    }

    /**
     * Log access granted
     */
    public function logAccessGranted(int $grantedBy, int $accessId, array $accessData): int|false
    {
        return $this->logActivity($grantedBy, 'form_access', 'granted', $accessId, ['new_data' => $accessData]);
    }

    /**
     * Log access revoked
     */
    public function logAccessRevoked(int $revokedBy, int $accessId, array $accessData): int|false
    {
        return $this->logActivity($revokedBy, 'form_access', 'revoked', $accessId, ['old_data' => $accessData]);
    }

    /**
     * Log file upload
     */
    public function logFileUploaded(int $userId, int $fileId, array $fileData): int|false
    {
        return $this->logActivity($userId, 'media_file', 'uploaded', $fileId, ['new_data' => $fileData]);
    }

    /**
     * Log file deletion
     */
    public function logFileDeleted(int $userId, int $fileId, array $fileData): int|false
    {
        return $this->logActivity($userId, 'media_file', 'deleted', $fileId, ['old_data' => $fileData]);
    }

    /**
     * Log security event
     */
    public function logSecurityEvent(string $eventType, int $userId = null, array $details = []): int|false
    {
        return $this->logActivity($userId, 'security', $eventType, null, ['new_data' => $details]);
    }

    /**
     * Log system event
     */
    public function logSystemEvent(string $eventType, array $details = []): int|false
    {
        return $this->logActivity(null, 'system', $eventType, null, ['new_data' => $details]);
    }

    /**
     * Get user activities
     */
    public function getUserActivities(int $userId, int $limit = 50): array
    {
        return $this->where('user_id', $userId)
                    ->orderBy('created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Get activities by entity type
     */
    public function getActivitiesByEntity(string $entityType, int $entityId = null, int $limit = 50): array
    {
        $builder = $this->where('entity_type', $entityType);
        
        if ($entityId !== null) {
            $builder->where('entity_id', $entityId);
        }

        return $builder->orderBy('created_at', 'DESC')
                       ->limit($limit)
                       ->findAll();
    }

    /**
     * Get activities by action
     */
    public function getActivitiesByAction(string $action, int $limit = 50): array
    {
        return $this->where('action', $action)
                    ->orderBy('created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Get recent activities
     */
    public function getRecentActivities(int $limit = 20): array
    {
        return $this->select('kobo_audit_logs.*, kobo_users.username')
                    ->join('kobo_users', 'kobo_users.id = kobo_audit_logs.user_id', 'left')
                    ->orderBy('kobo_audit_logs.created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Get activities with pagination and filters
     */
    public function getActivitiesWithPagination(int $perPage = 20, array $filters = []): array
    {
        $builder = $this->select('kobo_audit_logs.*, kobo_users.username')
                        ->join('kobo_users', 'kobo_users.id = kobo_audit_logs.user_id', 'left');

        // Apply filters
        if (!empty($filters['user_id'])) {
            $builder->where('kobo_audit_logs.user_id', $filters['user_id']);
        }

        if (!empty($filters['entity_type'])) {
            $builder->where('kobo_audit_logs.entity_type', $filters['entity_type']);
        }

        if (!empty($filters['action'])) {
            $builder->where('kobo_audit_logs.action', $filters['action']);
        }

        if (!empty($filters['date_from'])) {
            $builder->where('kobo_audit_logs.created_at >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $builder->where('kobo_audit_logs.created_at <=', $filters['date_to']);
        }

        if (!empty($filters['ip_address'])) {
            $builder->where('kobo_audit_logs.ip_address', $filters['ip_address']);
        }

        if (!empty($filters['search'])) {
            $builder->groupStart()
                    ->like('kobo_users.username', $filters['search'])
                    ->orLike('kobo_audit_logs.action', $filters['search'])
                    ->orLike('kobo_audit_logs.entity_type', $filters['search'])
                    ->groupEnd();
        }

        $builder->orderBy('kobo_audit_logs.created_at', 'DESC');

        return [
            'activities' => $builder->paginate($perPage),
            'pager' => $this->pager
        ];
    }

    /**
     * Get activity statistics
     */
    public function getActivityStats(string $period = '30 days'): array
    {
        $db = \Config\Database::connect();
        
        $startDate = date('Y-m-d H:i:s', strtotime("-{$period}"));
        
        // Get activity counts by type
        $typeQuery = $db->query("
            SELECT 
                entity_type,
                action,
                COUNT(*) as count
            FROM kobo_audit_logs
            WHERE created_at >= ?
            GROUP BY entity_type, action
            ORDER BY count DESC
        ", [$startDate]);

        // Get daily activity counts
        $dailyQuery = $db->query("
            SELECT 
                DATE(created_at) as activity_date,
                COUNT(*) as total_activities,
                COUNT(DISTINCT user_id) as unique_users
            FROM kobo_audit_logs
            WHERE created_at >= ?
            GROUP BY DATE(created_at)
            ORDER BY activity_date DESC
        ", [$startDate]);

        // Get top users by activity
        $userQuery = $db->query("
            SELECT 
                u.username,
                COUNT(al.id) as activity_count
            FROM kobo_audit_logs al
            LEFT JOIN kobo_users u ON u.id = al.user_id
            WHERE al.created_at >= ? AND al.user_id IS NOT NULL
            GROUP BY al.user_id, u.username
            ORDER BY activity_count DESC
            LIMIT 10
        ", [$startDate]);

        return [
            'activity_by_type' => $typeQuery->getResultArray(),
            'daily_activities' => $dailyQuery->getResultArray(),
            'top_users' => $userQuery->getResultArray(),
            'period' => $period,
            'start_date' => $startDate
        ];
    }

    /**
     * Get security events
     */
    public function getSecurityEvents(int $limit = 50): array
    {
        return $this->select('kobo_audit_logs.*, kobo_users.username')
                    ->join('kobo_users', 'kobo_users.id = kobo_audit_logs.user_id', 'left')
                    ->where('kobo_audit_logs.entity_type', 'security')
                    ->orderBy('kobo_audit_logs.created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Get failed login attempts
     */
    public function getFailedLoginAttempts(string $period = '24 hours'): array
    {
        $startDate = date('Y-m-d H:i:s', strtotime("-{$period}"));
        
        return $this->select('kobo_audit_logs.*, kobo_users.username')
                    ->join('kobo_users', 'kobo_users.id = kobo_audit_logs.user_id', 'left')
                    ->where('kobo_audit_logs.entity_type', 'user_auth')
                    ->where('kobo_audit_logs.action', 'login_failed')
                    ->where('kobo_audit_logs.created_at >=', $startDate)
                    ->orderBy('kobo_audit_logs.created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get IP activity summary
     */
    public function getIPActivitySummary(string $period = '7 days'): array
    {
        $db = \Config\Database::connect();
        $startDate = date('Y-m-d H:i:s', strtotime("-{$period}"));
        
        $query = $db->query("
            SELECT 
                ip_address,
                COUNT(*) as total_activities,
                COUNT(DISTINCT user_id) as unique_users,
                MIN(created_at) as first_activity,
                MAX(created_at) as last_activity
            FROM kobo_audit_logs
            WHERE created_at >= ? AND ip_address IS NOT NULL
            GROUP BY ip_address
            ORDER BY total_activities DESC
        ", [$startDate]);

        return $query->getResultArray();
    }

    /**
     * Clean up old audit logs
     */
    public function cleanupOldLogs(int $daysOld = 365): int
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysOld} days"));
        
        return $this->where('created_at <', $cutoffDate)->delete();
    }

    /**
     * Export audit logs to CSV
     */
    public function exportToCSV(array $filters = []): string
    {
        $builder = $this->select('kobo_audit_logs.*, kobo_users.username')
                        ->join('kobo_users', 'kobo_users.id = kobo_audit_logs.user_id', 'left');

        // Apply same filters as pagination method
        if (!empty($filters['user_id'])) {
            $builder->where('kobo_audit_logs.user_id', $filters['user_id']);
        }

        if (!empty($filters['entity_type'])) {
            $builder->where('kobo_audit_logs.entity_type', $filters['entity_type']);
        }

        if (!empty($filters['action'])) {
            $builder->where('kobo_audit_logs.action', $filters['action']);
        }

        if (!empty($filters['date_from'])) {
            $builder->where('kobo_audit_logs.created_at >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $builder->where('kobo_audit_logs.created_at <=', $filters['date_to']);
        }

        $logs = $builder->orderBy('kobo_audit_logs.created_at', 'DESC')->findAll();
        
        // Create CSV content
        $csv = "ID,Username,Entity Type,Entity ID,Action,IP Address,User Agent,Created At\n";
        
        foreach ($logs as $log) {
            $csv .= sprintf(
                "%d,%s,%s,%s,%s,%s,%s,%s\n",
                $log['id'],
                $log['username'] ?? 'System',
                $log['entity_type'],
                $log['entity_id'] ?? '',
                $log['action'],
                $log['ip_address'] ?? '',
                str_replace(["\n", "\r", ','], [' ', ' ', ' '], $log['user_agent'] ?? ''),
                $log['created_at']
            );
        }

        return $csv;
    }

    /**
     * Get activity timeline for dashboard
     */
    public function getActivityTimeline(int $limit = 10): array
    {
        $activities = $this->select('kobo_audit_logs.*, kobo_users.username')
                           ->join('kobo_users', 'kobo_users.id = kobo_audit_logs.user_id', 'left')
                           ->orderBy('kobo_audit_logs.created_at', 'DESC')
                           ->limit($limit)
                           ->findAll();

        // Format activities for timeline display
        $timeline = [];
        foreach ($activities as $activity) {
            $timeline[] = [
                'id' => $activity['id'],
                'user' => $activity['username'] ?? 'System',
                'action' => $this->formatActionForDisplay($activity['action'], $activity['entity_type']),
                'entity_type' => $activity['entity_type'],
                'entity_id' => $activity['entity_id'],
                'time' => $activity['created_at'],
                'time_ago' => $this->timeAgo($activity['created_at']),
                'ip_address' => $activity['ip_address']
            ];
        }

        return $timeline;
    }

    /**
     * Format action for display
     */
    private function formatActionForDisplay(string $action, string $entityType): string
    {
        $actionMap = [
            'created' => 'created a new',
            'updated' => 'updated a',
            'deleted' => 'deleted a',
            'login_success' => 'logged in successfully',
            'login_failed' => 'failed to log in',
            'logout' => 'logged out',
            'granted' => 'granted access to',
            'revoked' => 'revoked access to',
            'uploaded' => 'uploaded a',
            'processed' => 'processed a'
        ];

        $baseAction = $actionMap[$action] ?? $action;
        
        if (in_array($action, ['login_success', 'login_failed', 'logout'])) {
            return $baseAction;
        }

        return $baseAction . ' ' . str_replace('_', ' ', $entityType);
    }

    /**
     * Calculate time ago
     */
    private function timeAgo(string $datetime): string
    {
        $time = time() - strtotime($datetime);
        
        if ($time < 60) return 'just now';
        if ($time < 3600) return floor($time/60) . ' minutes ago';
        if ($time < 86400) return floor($time/3600) . ' hours ago';
        if ($time < 2592000) return floor($time/86400) . ' days ago';
        if ($time < 31536000) return floor($time/2592000) . ' months ago';
        
        return floor($time/31536000) . ' years ago';
    }

    /**
     * Monitor suspicious activities
     */
    public function getSuspiciousActivities(string $period = '24 hours'): array
    {
        $db = \Config\Database::connect();
        $startDate = date('Y-m-d H:i:s', strtotime("-{$period}"));
        
        // Multiple failed login attempts from same IP
        $suspiciousIPs = $db->query("
            SELECT 
                ip_address,
                COUNT(*) as failed_attempts,
                COUNT(DISTINCT user_id) as different_users
            FROM kobo_audit_logs
            WHERE entity_type = 'user_auth' 
                AND action = 'login_failed'
                AND created_at >= ?
            GROUP BY ip_address
            HAVING failed_attempts >= 5
            ORDER BY failed_attempts DESC
        ", [$startDate]);

        // Rapid successive actions from same user
        $rapidActions = $db->query("
            SELECT 
                user_id,
                COUNT(*) as action_count,
                MIN(created_at) as first_action,
                MAX(created_at) as last_action
            FROM kobo_audit_logs
            WHERE created_at >= ?
            GROUP BY user_id
            HAVING action_count >= 50
            ORDER BY action_count DESC
        ", [$startDate]);

        return [
            'suspicious_ips' => $suspiciousIPs->getResultArray(),
            'rapid_actions' => $rapidActions->getResultArray(),
            'period' => $period
        ];
    }
}