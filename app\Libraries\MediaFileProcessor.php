<?php

namespace App\Libraries;

use App\Libraries\FileSecurityScanner;

/**
 * Media File Processor
 * 
 * Advanced media file processing, optimization, and management
 */
class MediaFileProcessor
{
    protected $securityScanner;
    protected $uploadPath;
    protected $allowedTypes = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'],
        'audio' => ['mp3', 'wav', 'ogg', 'aac', 'm4a'],
        'video' => ['mp4', 'avi', 'mov', 'wmv', 'flv', '3gp'],
        'document' => ['pdf', 'doc', 'docx', 'txt', 'rtf']
    ];
    protected $maxSizes = [
        'image' => 10485760,     // 10MB
        'audio' => 52428800,     // 50MB
        'video' => 104857600,    // 100MB
        'document' => 10485760   // 10MB
    ];

    public function __construct()
    {
        $this->securityScanner = new FileSecurityScanner();
        $this->uploadPath = WRITEPATH . 'uploads/media/';
        $this->ensureUploadDirectories();
    }

    /**
     * Process uploaded media file
     */
    public function processMediaFile(\CodeIgniter\HTTP\Files\UploadedFile $file, array $options = []): array
    {
        try {
            // Security scan
            $securityResult = $this->securityScanner->scanFile($file);
            if (!$securityResult['safe']) {
                return [
                    'success' => false,
                    'error' => 'Security scan failed: ' . implode(', ', $securityResult['threats']),
                    'threats' => $securityResult['threats']
                ];
            }

            // File validation
            $validationResult = $this->validateMediaFile($file);
            if (!$validationResult['valid']) {
                return [
                    'success' => false,
                    'error' => 'File validation failed: ' . implode(', ', $validationResult['errors']),
                    'errors' => $validationResult['errors']
                ];
            }

            // Determine file type and category
            $fileInfo = $this->analyzeFile($file);
            
            // Process based on file type
            $processedFile = $this->processFileByType($file, $fileInfo, $options);
            
            // Store file metadata
            $metadata = $this->generateFileMetadata($file, $fileInfo, $processedFile);
            
            return [
                'success' => true,
                'file_info' => $fileInfo,
                'processed_file' => $processedFile,
                'metadata' => $metadata,
                'storage_path' => $processedFile['final_path']
            ];

        } catch (\Exception $e) {
            log_message('error', 'Media file processing failed: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Processing failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate media file
     */
    protected function validateMediaFile(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        $errors = [];
        
        if (!$file->isValid()) {
            $errors[] = 'File upload error: ' . $file->getErrorString();
            return ['valid' => false, 'errors' => $errors];
        }

        $extension = strtolower($file->getClientExtension());
        $fileCategory = $this->getFileCategory($extension);
        
        if (!$fileCategory) {
            $errors[] = "File type not supported: {$extension}";
        }

        // Check file size
        $maxSize = $this->maxSizes[$fileCategory] ?? $this->maxSizes['document'];
        if ($file->getSize() > $maxSize) {
            $errors[] = "File size exceeds limit for {$fileCategory} files";
        }

        // MIME type validation
        $expectedMimes = $this->getExpectedMimeTypes($extension);
        if (!in_array($file->getMimeType(), $expectedMimes)) {
            $errors[] = "MIME type mismatch for {$extension} files";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Analyze file properties
     */
    protected function analyzeFile(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        $extension = strtolower($file->getClientExtension());
        $category = $this->getFileCategory($extension);
        
        $info = [
            'original_name' => $file->getClientName(),
            'extension' => $extension,
            'category' => $category,
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'hash' => hash_file('sha256', $file->getTempName())
        ];

        // Category-specific analysis
        switch ($category) {
            case 'image':
                $info = array_merge($info, $this->analyzeImage($file));
                break;
            case 'audio':
                $info = array_merge($info, $this->analyzeAudio($file));
                break;
            case 'video':
                $info = array_merge($info, $this->analyzeVideo($file));
                break;
            case 'document':
                $info = array_merge($info, $this->analyzeDocument($file));
                break;
        }

        return $info;
    }

    /**
     * Process file based on type
     */
    protected function processFileByType(\CodeIgniter\HTTP\Files\UploadedFile $file, array $fileInfo, array $options): array
    {
        $category = $fileInfo['category'];
        $processed = [
            'original_path' => null,
            'processed_paths' => [],
            'final_path' => null,
            'thumbnails' => [],
            'optimized' => false
        ];

        // Generate safe filename
        $safeFilename = $this->generateSafeFilename($file);
        $categoryPath = $this->uploadPath . $category . '/';
        $originalPath = $categoryPath . 'original/' . $safeFilename;

        // Move original file
        if (!$file->move($categoryPath . 'original/', $safeFilename)) {
            throw new \Exception('Failed to move uploaded file');
        }
        $processed['original_path'] = $originalPath;

        // Process based on category
        switch ($category) {
            case 'image':
                $processed = array_merge($processed, $this->processImage($originalPath, $categoryPath, $options));
                break;
            case 'audio':
                $processed = array_merge($processed, $this->processAudio($originalPath, $categoryPath, $options));
                break;
            case 'video':
                $processed = array_merge($processed, $this->processVideo($originalPath, $categoryPath, $options));
                break;
            case 'document':
                $processed = array_merge($processed, $this->processDocument($originalPath, $categoryPath, $options));
                break;
        }

        // Set final path (optimized version if available, otherwise original)
        $processed['final_path'] = $processed['processed_paths'][0] ?? $originalPath;

        return $processed;
    }

    /**
     * Process image files
     */
    protected function processImage(string $originalPath, string $basePath, array $options): array
    {
        $result = [
            'processed_paths' => [],
            'thumbnails' => [],
            'optimized' => false
        ];

        try {
            // Create image resource
            $imageInfo = getimagesize($originalPath);
            if (!$imageInfo) {
                throw new \Exception('Invalid image file');
            }

            $image = $this->createImageResource($originalPath, $imageInfo[2]);
            if (!$image) {
                throw new \Exception('Failed to create image resource');
            }

            // Generate thumbnails
            $thumbnailSizes = $options['thumbnail_sizes'] ?? [150, 300, 600];
            foreach ($thumbnailSizes as $size) {
                $thumbnailPath = $this->generateThumbnail($image, $originalPath, $basePath, $size);
                if ($thumbnailPath) {
                    $result['thumbnails'][$size] = $thumbnailPath;
                }
            }

            // Optimize image
            if ($options['optimize'] ?? true) {
                $optimizedPath = $this->optimizeImage($image, $originalPath, $basePath);
                if ($optimizedPath) {
                    $result['processed_paths'][] = $optimizedPath;
                    $result['optimized'] = true;
                }
            }

            // Convert to different formats if requested
            if (isset($options['convert_to'])) {
                $convertedPath = $this->convertImage($image, $originalPath, $basePath, $options['convert_to']);
                if ($convertedPath) {
                    $result['processed_paths'][] = $convertedPath;
                }
            }

            imagedestroy($image);

        } catch (\Exception $e) {
            log_message('error', 'Image processing failed: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * Process audio files
     */
    protected function processAudio(string $originalPath, string $basePath, array $options): array
    {
        $result = [
            'processed_paths' => [],
            'optimized' => false
        ];

        // Audio processing would require FFmpeg or similar
        // For now, we'll implement basic metadata extraction
        try {
            if ($options['extract_metadata'] ?? true) {
                $metadata = $this->extractAudioMetadata($originalPath);
                $result['metadata'] = $metadata;
            }

            // Placeholder for audio optimization/conversion
            if ($options['optimize'] ?? false) {
                // Would implement FFmpeg-based optimization here
                log_message('info', 'Audio optimization requires FFmpeg integration');
            }

        } catch (\Exception $e) {
            log_message('error', 'Audio processing failed: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * Process video files
     */
    protected function processVideo(string $originalPath, string $basePath, array $options): array
    {
        $result = [
            'processed_paths' => [],
            'thumbnails' => [],
            'optimized' => false
        ];

        try {
            // Video processing would require FFmpeg
            if ($options['generate_thumbnail'] ?? true) {
                // Would implement video thumbnail generation here
                log_message('info', 'Video thumbnail generation requires FFmpeg integration');
            }

            if ($options['extract_metadata'] ?? true) {
                $metadata = $this->extractVideoMetadata($originalPath);
                $result['metadata'] = $metadata;
            }

        } catch (\Exception $e) {
            log_message('error', 'Video processing failed: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * Process document files
     */
    protected function processDocument(string $originalPath, string $basePath, array $options): array
    {
        $result = [
            'processed_paths' => [],
            'optimized' => false
        ];

        try {
            if ($options['extract_text'] ?? false) {
                $text = $this->extractDocumentText($originalPath);
                $result['extracted_text'] = $text;
            }

            // Could implement PDF optimization, image extraction, etc.

        } catch (\Exception $e) {
            log_message('error', 'Document processing failed: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * Analyze image properties
     */
    protected function analyzeImage(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        $info = [];
        
        try {
            $imageInfo = getimagesize($file->getTempName());
            if ($imageInfo) {
                $info['width'] = $imageInfo[0];
                $info['height'] = $imageInfo[1];
                $info['type'] = $imageInfo[2];
                $info['channels'] = $imageInfo['channels'] ?? null;
                $info['bits'] = $imageInfo['bits'] ?? null;
            }

            // Extract EXIF data
            if (function_exists('exif_read_data') && in_array($imageInfo[2], [IMAGETYPE_JPEG, IMAGETYPE_TIFF_II, IMAGETYPE_TIFF_MM])) {
                $exif = @exif_read_data($file->getTempName());
                if ($exif) {
                    $info['exif'] = $this->sanitizeExifData($exif);
                }
            }

        } catch (\Exception $e) {
            log_message('warning', 'Image analysis failed: ' . $e->getMessage());
        }

        return $info;
    }

    /**
     * Analyze audio properties
     */
    protected function analyzeAudio(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        // Basic audio analysis - would be enhanced with proper audio libraries
        return [
            'duration' => null,
            'bitrate' => null,
            'sample_rate' => null,
            'channels' => null
        ];
    }

    /**
     * Analyze video properties
     */
    protected function analyzeVideo(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        // Basic video analysis - would be enhanced with FFmpeg
        return [
            'duration' => null,
            'width' => null,
            'height' => null,
            'framerate' => null,
            'bitrate' => null
        ];
    }

    /**
     * Analyze document properties
     */
    protected function analyzeDocument(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        $info = [];
        
        try {
            if (strtolower($file->getClientExtension()) === 'pdf') {
                // Basic PDF analysis
                $info['pages'] = $this->countPDFPages($file->getTempName());
            }
        } catch (\Exception $e) {
            log_message('warning', 'Document analysis failed: ' . $e->getMessage());
        }

        return $info;
    }

    /**
     * Create image resource from file
     */
    protected function createImageResource(string $imagePath, int $imageType)
    {
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                return imagecreatefromjpeg($imagePath);
            case IMAGETYPE_PNG:
                return imagecreatefrompng($imagePath);
            case IMAGETYPE_GIF:
                return imagecreatefromgif($imagePath);
            case IMAGETYPE_WEBP:
                return imagecreatefromwebp($imagePath);
            case IMAGETYPE_BMP:
                return imagecreatefrombmp($imagePath);
            default:
                return false;
        }
    }

    /**
     * Generate thumbnail
     */
    protected function generateThumbnail($image, string $originalPath, string $basePath, int $size): ?string
    {
        try {
            $originalWidth = imagesx($image);
            $originalHeight = imagesy($image);
            
            // Calculate thumbnail dimensions
            if ($originalWidth > $originalHeight) {
                $thumbWidth = $size;
                $thumbHeight = ($originalHeight * $size) / $originalWidth;
            } else {
                $thumbHeight = $size;
                $thumbWidth = ($originalWidth * $size) / $originalHeight;
            }

            // Create thumbnail
            $thumbnail = imagecreatetruecolor($thumbWidth, $thumbHeight);
            
            // Preserve transparency for PNG and GIF
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefilledrectangle($thumbnail, 0, 0, $thumbWidth, $thumbHeight, $transparent);

            imagecopyresampled($thumbnail, $image, 0, 0, 0, 0, $thumbWidth, $thumbHeight, $originalWidth, $originalHeight);

            // Save thumbnail
            $filename = pathinfo($originalPath, PATHINFO_FILENAME);
            $extension = pathinfo($originalPath, PATHINFO_EXTENSION);
            $thumbnailPath = $basePath . "thumbnails/{$filename}_{$size}x{$size}.{$extension}";
            
            $this->saveImage($thumbnail, $thumbnailPath, $extension);
            imagedestroy($thumbnail);

            return $thumbnailPath;

        } catch (\Exception $e) {
            log_message('error', 'Thumbnail generation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Optimize image
     */
    protected function optimizeImage($image, string $originalPath, string $basePath): ?string
    {
        try {
            $filename = pathinfo($originalPath, PATHINFO_FILENAME);
            $extension = pathinfo($originalPath, PATHINFO_EXTENSION);
            $optimizedPath = $basePath . "optimized/{$filename}_optimized.{$extension}";
            
            $this->saveImage($image, $optimizedPath, $extension, 85); // 85% quality
            
            return $optimizedPath;

        } catch (\Exception $e) {
            log_message('error', 'Image optimization failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Save image with specified quality
     */
    protected function saveImage($image, string $path, string $extension, int $quality = 90): bool
    {
        $this->ensureDirectoryExists(dirname($path));
        
        switch (strtolower($extension)) {
            case 'jpg':
            case 'jpeg':
                return imagejpeg($image, $path, $quality);
            case 'png':
                return imagepng($image, $path, (int)((100 - $quality) / 11.111));
            case 'gif':
                return imagegif($image, $path);
            case 'webp':
                return imagewebp($image, $path, $quality);
            case 'bmp':
                return imagebmp($image, $path);
            default:
                return false;
        }
    }

    /**
     * Get file category by extension
     */
    protected function getFileCategory(string $extension): ?string
    {
        foreach ($this->allowedTypes as $category => $extensions) {
            if (in_array($extension, $extensions)) {
                return $category;
            }
        }
        return null;
    }

    /**
     * Get expected MIME types for extension
     */
    protected function getExpectedMimeTypes(string $extension): array
    {
        $mimeMap = [
            'jpg' => ['image/jpeg'],
            'jpeg' => ['image/jpeg'],
            'png' => ['image/png'],
            'gif' => ['image/gif'],
            'webp' => ['image/webp'],
            'bmp' => ['image/bmp'],
            'mp3' => ['audio/mpeg'],
            'wav' => ['audio/wav'],
            'ogg' => ['audio/ogg'],
            'mp4' => ['video/mp4'],
            'avi' => ['video/x-msvideo'],
            'pdf' => ['application/pdf']
        ];

        return $mimeMap[$extension] ?? [];
    }

    /**
     * Generate safe filename
     */
    protected function generateSafeFilename(\CodeIgniter\HTTP\Files\UploadedFile $file): string
    {
        $originalName = pathinfo($file->getClientName(), PATHINFO_FILENAME);
        $extension = $file->getClientExtension();
        
        // Sanitize filename
        $safeName = preg_replace('/[^a-zA-Z0-9._-]/', '_', $originalName);
        $safeName = substr($safeName, 0, 50); // Limit length
        
        return $safeName . '_' . date('YmdHis') . '_' . uniqid() . '.' . $extension;
    }

    /**
     * Generate file metadata
     */
    protected function generateFileMetadata(\CodeIgniter\HTTP\Files\UploadedFile $file, array $fileInfo, array $processedFile): array
    {
        return [
            'upload_time' => date('Y-m-d H:i:s'),
            'original_name' => $file->getClientName(),
            'file_size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'file_hash' => $fileInfo['hash'],
            'category' => $fileInfo['category'],
            'processed' => !empty($processedFile['processed_paths']),
            'thumbnails_created' => count($processedFile['thumbnails'] ?? []),
            'storage_size' => $this->calculateStorageSize($processedFile)
        ];
    }

    /**
     * Calculate total storage size
     */
    protected function calculateStorageSize(array $processedFile): int
    {
        $totalSize = 0;
        
        if ($processedFile['original_path'] && file_exists($processedFile['original_path'])) {
            $totalSize += filesize($processedFile['original_path']);
        }

        foreach ($processedFile['processed_paths'] as $path) {
            if (file_exists($path)) {
                $totalSize += filesize($path);
            }
        }

        foreach ($processedFile['thumbnails'] as $thumbnail) {
            if (file_exists($thumbnail)) {
                $totalSize += filesize($thumbnail);
            }
        }

        return $totalSize;
    }

    /**
     * Ensure upload directories exist
     */
    protected function ensureUploadDirectories(): void
    {
        $directories = [
            $this->uploadPath,
            $this->uploadPath . 'image/',
            $this->uploadPath . 'image/original/',
            $this->uploadPath . 'image/thumbnails/',
            $this->uploadPath . 'image/optimized/',
            $this->uploadPath . 'audio/',
            $this->uploadPath . 'audio/original/',
            $this->uploadPath . 'video/',
            $this->uploadPath . 'video/original/',
            $this->uploadPath . 'document/',
            $this->uploadPath . 'document/original/'
        ];

        foreach ($directories as $dir) {
            $this->ensureDirectoryExists($dir);
        }
    }

    /**
     * Ensure directory exists
     */
    protected function ensureDirectoryExists(string $path): void
    {
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
    }

    /**
     * Helper methods for analysis
     */
    protected function sanitizeExifData(array $exif): array
    {
        $safeData = [];
        $allowedKeys = ['DateTime', 'Make', 'Model', 'Software', 'Orientation', 'XResolution', 'YResolution'];
        
        foreach ($allowedKeys as $key) {
            if (isset($exif[$key])) {
                $safeData[$key] = $exif[$key];
            }
        }

        return $safeData;
    }

    protected function countPDFPages(string $filePath): int
    {
        try {
            $content = file_get_contents($filePath);
            $pageCount = preg_match_all('/\/Page\W/', $content);
            return $pageCount ?: 1;
        } catch (\Exception $e) {
            return 1;
        }
    }

    protected function extractAudioMetadata(string $filePath): array
    {
        // Would implement with getID3 or similar library
        return [];
    }

    protected function extractVideoMetadata(string $filePath): array
    {
        // Would implement with FFmpeg
        return [];
    }

    protected function extractDocumentText(string $filePath): string
    {
        // Would implement text extraction based on file type
        return '';
    }
}