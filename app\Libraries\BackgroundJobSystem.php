<?php

namespace App\Libraries;

use App\Models\Kobo\KoboAuditLogModel;

/**
 * Background Job System
 * 
 * Queue-based background job processing system for long-running tasks
 */
class BackgroundJobSystem
{
    protected $cache;
    protected $auditModel;
    protected $jobHandlers = [];
    protected $maxRetries = 3;
    protected $defaultDelay = 0;
    protected $jobTimeout = 300; // 5 minutes

    public function __construct()
    {
        $this->cache = cache();
        $this->auditModel = new KoboAuditLogModel();
        $this->registerDefaultHandlers();
    }

    /**
     * Queue a new job
     */
    public function queueJob(string $jobType, array $payload, array $options = []): string
    {
        $jobId = $this->generateJobId();
        
        $job = [
            'id' => $jobId,
            'type' => $jobType,
            'payload' => $payload,
            'status' => 'queued',
            'priority' => $options['priority'] ?? 5,
            'delay' => $options['delay'] ?? $this->defaultDelay,
            'max_retries' => $options['max_retries'] ?? $this->maxRetries,
            'retries' => 0,
            'created_at' => time(),
            'scheduled_at' => time() + ($options['delay'] ?? $this->defaultDelay),
            'started_at' => null,
            'completed_at' => null,
            'failed_at' => null,
            'error_message' => null,
            'progress' => 0,
            'metadata' => $options['metadata'] ?? []
        ];

        // Store job in cache
        $this->storeJob($job);
        
        // Add to queue
        $this->addToQueue($jobId, $job['priority']);
        
        // Log job creation
        $this->logJobEvent($jobId, 'queued', $job);
        
        return $jobId;
    }

    /**
     * Process jobs in queue
     */
    public function processJobs(int $maxJobs = 10): array
    {
        $processedJobs = [];
        $jobsProcessed = 0;
        
        while ($jobsProcessed < $maxJobs) {
            $jobId = $this->getNextJob();
            
            if (!$jobId) {
                break; // No more jobs in queue
            }

            $result = $this->processJob($jobId);
            $processedJobs[] = $result;
            $jobsProcessed++;
        }

        return $processedJobs;
    }

    /**
     * Process individual job
     */
    protected function processJob(string $jobId): array
    {
        try {
            $job = $this->getJob($jobId);
            
            if (!$job) {
                return ['job_id' => $jobId, 'status' => 'not_found'];
            }

            // Check if job is ready to run
            if ($job['scheduled_at'] > time()) {
                // Put back in queue for later
                $this->addToQueue($jobId, $job['priority']);
                return ['job_id' => $jobId, 'status' => 'delayed'];
            }

            // Mark job as running
            $job['status'] = 'running';
            $job['started_at'] = time();
            $this->storeJob($job);
            $this->logJobEvent($jobId, 'started', $job);

            // Execute job
            $handler = $this->getJobHandler($job['type']);
            if (!$handler) {
                throw new \Exception("No handler found for job type: {$job['type']}");
            }

            $result = $this->executeJobWithTimeout($handler, $job);
            
            // Mark job as completed
            $job['status'] = 'completed';
            $job['completed_at'] = time();
            $job['progress'] = 100;
            $job['result'] = $result;
            $this->storeJob($job);
            $this->logJobEvent($jobId, 'completed', $job);

            return ['job_id' => $jobId, 'status' => 'completed', 'result' => $result];

        } catch (\Exception $e) {
            return $this->handleJobFailure($jobId, $e);
        }
    }

    /**
     * Execute job with timeout protection
     */
    protected function executeJobWithTimeout(callable $handler, array $job)
    {
        $startTime = time();
        
        // Set time limit
        set_time_limit($this->jobTimeout);
        
        try {
            return $handler($job['payload'], function($progress) use ($job) {
                $this->updateJobProgress($job['id'], $progress);
            });
        } finally {
            // Reset time limit
            set_time_limit(0);
        }
    }

    /**
     * Handle job failure
     */
    protected function handleJobFailure(string $jobId, \Exception $e): array
    {
        try {
            $job = $this->getJob($jobId);
            
            if (!$job) {
                return ['job_id' => $jobId, 'status' => 'not_found'];
            }

            $job['retries']++;
            $job['error_message'] = $e->getMessage();
            $job['failed_at'] = time();

            // Check if we should retry
            if ($job['retries'] < $job['max_retries']) {
                $job['status'] = 'retrying';
                $job['scheduled_at'] = time() + ($job['retries'] * 60); // Exponential backoff
                $this->storeJob($job);
                $this->addToQueue($jobId, $job['priority']);
                $this->logJobEvent($jobId, 'retry_scheduled', $job);
                
                return ['job_id' => $jobId, 'status' => 'retry_scheduled'];
            } else {
                $job['status'] = 'failed';
                $this->storeJob($job);
                $this->logJobEvent($jobId, 'failed', $job);
                
                return ['job_id' => $jobId, 'status' => 'failed', 'error' => $e->getMessage()];
            }

        } catch (\Exception $failureError) {
            log_message('error', 'Failed to handle job failure: ' . $failureError->getMessage());
            return ['job_id' => $jobId, 'status' => 'error', 'error' => $failureError->getMessage()];
        }
    }

    /**
     * Get job status and details
     */
    public function getJobStatus(string $jobId): ?array
    {
        return $this->getJob($jobId);
    }

    /**
     * Cancel a job
     */
    public function cancelJob(string $jobId): bool
    {
        try {
            $job = $this->getJob($jobId);
            
            if (!$job) {
                return false;
            }

            if (in_array($job['status'], ['completed', 'failed', 'cancelled'])) {
                return false; // Cannot cancel finished jobs
            }

            $job['status'] = 'cancelled';
            $job['completed_at'] = time();
            $this->storeJob($job);
            $this->logJobEvent($jobId, 'cancelled', $job);
            
            return true;

        } catch (\Exception $e) {
            log_message('error', 'Failed to cancel job: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Update job progress
     */
    public function updateJobProgress(string $jobId, int $progress): void
    {
        try {
            $job = $this->getJob($jobId);
            
            if ($job && $job['status'] === 'running') {
                $job['progress'] = max(0, min(100, $progress));
                $this->storeJob($job);
            }

        } catch (\Exception $e) {
            log_message('error', 'Failed to update job progress: ' . $e->getMessage());
        }
    }

    /**
     * Register job handler
     */
    public function registerHandler(string $jobType, callable $handler): self
    {
        $this->jobHandlers[$jobType] = $handler;
        return $this;
    }

    /**
     * Get queue statistics
     */
    public function getQueueStatistics(): array
    {
        $queueKey = 'bg_job_queue';
        $queueSize = $this->cache->get($queueKey . '_size') ?: 0;
        
        $stats = [
            'queue_size' => $queueSize,
            'jobs_by_status' => [],
            'jobs_by_type' => [],
            'average_processing_time' => 0,
            'total_jobs_processed' => 0
        ];

        // Get job statistics from cache
        $jobStats = $this->cache->get('bg_job_stats') ?: [];
        if (!empty($jobStats)) {
            $stats = array_merge($stats, $jobStats);
        }

        return $stats;
    }

    /**
     * Clean up old jobs
     */
    public function cleanupOldJobs(int $maxAge = 86400): int
    {
        $cleaned = 0;
        $cutoff = time() - $maxAge;
        
        try {
            // Get list of all job IDs
            $jobIds = $this->cache->get('bg_job_list') ?: [];
            
            foreach ($jobIds as $jobId) {
                $job = $this->getJob($jobId);
                
                if ($job && isset($job['completed_at']) && $job['completed_at'] < $cutoff) {
                    $this->deleteJob($jobId);
                    $cleaned++;
                }
            }

            // Update job list
            $remainingJobs = array_filter($jobIds, function($jobId) {
                return $this->getJob($jobId) !== null;
            });
            $this->cache->save('bg_job_list', $remainingJobs, 604800); // 1 week

        } catch (\Exception $e) {
            log_message('error', 'Job cleanup failed: ' . $e->getMessage());
        }

        return $cleaned;
    }

    /**
     * Storage and queue management methods
     */
    protected function storeJob(array $job): void
    {
        $jobKey = 'bg_job_' . $job['id'];
        $this->cache->save($jobKey, $job, 604800); // Store for 1 week
        
        // Update job list
        $jobList = $this->cache->get('bg_job_list') ?: [];
        if (!in_array($job['id'], $jobList)) {
            $jobList[] = $job['id'];
            $this->cache->save('bg_job_list', $jobList, 604800);
        }
    }

    protected function getJob(string $jobId): ?array
    {
        $jobKey = 'bg_job_' . $jobId;
        return $this->cache->get($jobKey);
    }

    protected function deleteJob(string $jobId): void
    {
        $jobKey = 'bg_job_' . $jobId;
        $this->cache->delete($jobKey);
    }

    protected function addToQueue(string $jobId, int $priority = 5): void
    {
        $queueKey = 'bg_job_queue';
        $queue = $this->cache->get($queueKey) ?: [];
        
        // Add job with priority (lower number = higher priority)
        $queue[] = ['id' => $jobId, 'priority' => $priority, 'queued_at' => time()];
        
        // Sort by priority
        usort($queue, function($a, $b) {
            if ($a['priority'] === $b['priority']) {
                return $a['queued_at'] - $b['queued_at']; // FIFO for same priority
            }
            return $a['priority'] - $b['priority'];
        });

        $this->cache->save($queueKey, $queue, 3600);
        $this->cache->save($queueKey . '_size', count($queue), 3600);
    }

    protected function getNextJob(): ?string
    {
        $queueKey = 'bg_job_queue';
        $queue = $this->cache->get($queueKey) ?: [];
        
        if (empty($queue)) {
            return null;
        }

        $nextJob = array_shift($queue);
        $this->cache->save($queueKey, $queue, 3600);
        $this->cache->save($queueKey . '_size', count($queue), 3600);
        
        return $nextJob['id'];
    }

    protected function getJobHandler(string $jobType): ?callable
    {
        return $this->jobHandlers[$jobType] ?? null;
    }

    protected function generateJobId(): string
    {
        return 'job_' . date('YmdHis') . '_' . uniqid();
    }

    protected function logJobEvent(string $jobId, string $event, array $job): void
    {
        try {
            $this->auditModel->logActivity(
                $job['metadata']['user_id'] ?? null,
                'background_job',
                $event,
                $jobId,
                [
                    'job_type' => $job['type'],
                    'status' => $job['status'],
                    'progress' => $job['progress'] ?? 0,
                    'retries' => $job['retries'] ?? 0,
                    'error' => $job['error_message'] ?? null
                ]
            );
        } catch (\Exception $e) {
            log_message('error', 'Failed to log job event: ' . $e->getMessage());
        }
    }

    /**
     * Register default job handlers
     */
    protected function registerDefaultHandlers(): void
    {
        // Submission processing job
        $this->registerHandler('process_submission', function($payload, $progressCallback) {
            return $this->processSubmissionJob($payload, $progressCallback);
        });

        // Media file processing job
        $this->registerHandler('process_media', function($payload, $progressCallback) {
            return $this->processMediaJob($payload, $progressCallback);
        });

        // Data export job
        $this->registerHandler('export_data', function($payload, $progressCallback) {
            return $this->processExportJob($payload, $progressCallback);
        });

        // Email notification job
        $this->registerHandler('send_notification', function($payload, $progressCallback) {
            return $this->processNotificationJob($payload, $progressCallback);
        });

        // System maintenance job
        $this->registerHandler('system_maintenance', function($payload, $progressCallback) {
            return $this->processMaintenanceJob($payload, $progressCallback);
        });
    }

    /**
     * Job handlers
     */
    protected function processSubmissionJob(array $payload, callable $progressCallback): array
    {
        $progressCallback(10);
        
        // Load required libraries
        $xmlProcessor = new XMLProcessingEngine();
        $submissionModel = new \App\Models\Kobo\KoboSubmissionModel();
        
        $progressCallback(30);
        
        // Process XML data
        $xmlResult = $xmlProcessor->processSubmissionXML($payload['xml_content']);
        
        $progressCallback(60);
        
        // Store processed data
        if ($xmlResult['success']) {
            $submissionData = [
                'form_id' => $payload['form_id'],
                'submission_id' => $payload['submission_id'],
                'device_id' => $payload['device_id'] ?? null,
                'xml_content' => $payload['xml_content'],
                'json_data' => json_encode($xmlResult['data']),
                'status' => 'processed',
                'processed_at' => date('Y-m-d H:i:s')
            ];
            
            $submissionModel->insert($submissionData);
        }
        
        $progressCallback(100);
        
        return [
            'processed' => $xmlResult['success'],
            'submission_id' => $payload['submission_id'],
            'data_fields' => count($xmlResult['data'] ?? [])
        ];
    }

    protected function processMediaJob(array $payload, callable $progressCallback): array
    {
        $progressCallback(20);
        
        $mediaProcessor = new MediaFileProcessor();
        
        $progressCallback(50);
        
        // This would process uploaded media files
        // Implementation depends on specific media processing requirements
        
        $progressCallback(100);
        
        return [
            'processed' => true,
            'file_id' => $payload['file_id'] ?? null
        ];
    }

    protected function processExportJob(array $payload, callable $progressCallback): array
    {
        $progressCallback(25);
        
        // Load export library based on format
        $exportFormat = $payload['format'] ?? 'csv';
        
        $progressCallback(50);
        
        // Generate export file
        // Implementation would depend on specific export requirements
        
        $progressCallback(100);
        
        return [
            'exported' => true,
            'format' => $exportFormat,
            'file_path' => $payload['output_path'] ?? null
        ];
    }

    protected function processNotificationJob(array $payload, callable $progressCallback): array
    {
        $progressCallback(30);
        
        $email = service('email');
        
        $progressCallback(60);
        
        try {
            $email->setTo($payload['to']);
            $email->setSubject($payload['subject']);
            $email->setMessage($payload['message']);
            $sent = $email->send();
            
            $progressCallback(100);
            
            return [
                'sent' => $sent,
                'recipient' => $payload['to']
            ];
            
        } catch (\Exception $e) {
            throw new \Exception('Failed to send notification: ' . $e->getMessage());
        }
    }

    protected function processMaintenanceJob(array $payload, callable $progressCallback): array
    {
        $progressCallback(20);
        
        $tasks = $payload['tasks'] ?? [];
        $completed = [];
        
        foreach ($tasks as $index => $task) {
            switch ($task) {
                case 'cleanup_logs':
                    $this->cleanupLogs();
                    break;
                case 'optimize_database':
                    $this->optimizeDatabase();
                    break;
                case 'cleanup_temp_files':
                    $this->cleanupTempFiles();
                    break;
            }
            
            $completed[] = $task;
            $progress = 20 + (($index + 1) / count($tasks)) * 80;
            $progressCallback((int)$progress);
        }
        
        return [
            'completed_tasks' => $completed,
            'total_tasks' => count($tasks)
        ];
    }

    /**
     * Maintenance helper methods
     */
    protected function cleanupLogs(): void
    {
        // Clean up old log files
        $logPath = WRITEPATH . 'logs/';
        $cutoff = time() - (30 * 24 * 3600); // 30 days
        
        if (is_dir($logPath)) {
            $files = glob($logPath . '*.log');
            foreach ($files as $file) {
                if (filemtime($file) < $cutoff) {
                    unlink($file);
                }
            }
        }
    }

    protected function optimizeDatabase(): void
    {
        // Database optimization tasks
        $db = \Config\Database::connect();
        
        // Get all Kobo tables
        $tables = ['kobo_users', 'kobo_forms', 'kobo_submissions', 'kobo_form_access', 'kobo_media_files', 'kobo_audit_logs'];
        
        foreach ($tables as $table) {
            try {
                $db->query("OPTIMIZE TABLE {$table}");
            } catch (\Exception $e) {
                log_message('warning', "Failed to optimize table {$table}: " . $e->getMessage());
            }
        }
    }

    protected function cleanupTempFiles(): void
    {
        // Clean up temporary files
        $tempPaths = [
            WRITEPATH . 'uploads/temp/',
            WRITEPATH . 'cache/',
            sys_get_temp_dir() . '/kobo_temp/'
        ];
        
        $cutoff = time() - (24 * 3600); // 1 day
        
        foreach ($tempPaths as $path) {
            if (is_dir($path)) {
                $files = glob($path . '*');
                foreach ($files as $file) {
                    if (is_file($file) && filemtime($file) < $cutoff) {
                        unlink($file);
                    }
                }
            }
        }
    }
}