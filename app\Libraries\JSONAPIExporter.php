<?php

namespace App\Libraries;

use App\Models\Kobo\KoboSubmissionModel;
use App\Models\Kobo\KoboFormModel;
use App\Models\Kobo\KoboUserModel;
use App\Models\Kobo\KoboAuditLogModel;

/**
 * JSON API Exporter
 * 
 * RESTful JSON API for data export with pagination, filtering, and real-time sync
 */
class JSONAPIExporter
{
    protected $submissionModel;
    protected $formModel;
    protected $userModel;
    protected $auditModel;
    protected $cache;
    protected $defaultPageSize = 50;
    protected $maxPageSize = 500;

    public function __construct()
    {
        $this->submissionModel = new KoboSubmissionModel();
        $this->formModel = new KoboFormModel();
        $this->userModel = new KoboUserModel();
        $this->auditModel = new KoboAuditLogModel();
        $this->cache = cache();
    }

    /**
     * Export submissions with pagination and filtering
     */
    public function exportSubmissions(array $params = []): array
    {
        try {
            // Validate and sanitize parameters
            $validatedParams = $this->validateExportParams($params);
            
            // Check cache first
            $cacheKey = $this->generateCacheKey('submissions', $validatedParams);
            $cachedResult = $this->cache->get($cacheKey);
            
            if ($cachedResult && !($validatedParams['real_time'] ?? false)) {
                return $cachedResult;
            }

            // Build query with filters
            $query = $this->buildSubmissionQuery($validatedParams);
            
            // Get total count for pagination
            $totalCount = $this->getTotalCount($query, $validatedParams);
            
            // Apply pagination
            $paginatedQuery = $this->applyPagination($query, $validatedParams);
            
            // Execute query and get data
            $submissions = $paginatedQuery->findAll();
            
            // Transform data based on requested format
            $transformedData = $this->transformSubmissionData($submissions, $validatedParams);
            
            // Build response with metadata
            $response = $this->buildPaginatedResponse([
                'data' => $transformedData,
                'total_count' => $totalCount,
                'params' => $validatedParams
            ]);

            // Cache result if not real-time
            if (!($validatedParams['real_time'] ?? false)) {
                $this->cache->save($cacheKey, $response, 300); // 5 minutes
            }

            // Log export activity
            $this->logExportActivity('submissions', $validatedParams, count($transformedData));

            return $response;

        } catch (\Exception $e) {
            log_message('error', 'JSON API export failed: ' . $e->getMessage());
            return $this->buildErrorResponse('Export failed: ' . $e->getMessage());
        }
    }

    /**
     * Export forms with metadata
     */
    public function exportForms(array $params = []): array
    {
        try {
            $validatedParams = $this->validateExportParams($params);
            
            $cacheKey = $this->generateCacheKey('forms', $validatedParams);
            $cachedResult = $this->cache->get($cacheKey);
            
            if ($cachedResult && !($validatedParams['real_time'] ?? false)) {
                return $cachedResult;
            }

            // Build forms query
            $query = $this->buildFormsQuery($validatedParams);
            $totalCount = $this->getTotalCount($query, $validatedParams);
            $paginatedQuery = $this->applyPagination($query, $validatedParams);
            
            $forms = $paginatedQuery->findAll();
            $transformedData = $this->transformFormsData($forms, $validatedParams);
            
            $response = $this->buildPaginatedResponse([
                'data' => $transformedData,
                'total_count' => $totalCount,
                'params' => $validatedParams
            ]);

            if (!($validatedParams['real_time'] ?? false)) {
                $this->cache->save($cacheKey, $response, 300);
            }

            $this->logExportActivity('forms', $validatedParams, count($transformedData));

            return $response;

        } catch (\Exception $e) {
            log_message('error', 'Forms JSON API export failed: ' . $e->getMessage());
            return $this->buildErrorResponse('Forms export failed: ' . $e->getMessage());
        }
    }

    /**
     * Export aggregated statistics
     */
    public function exportStatistics(array $params = []): array
    {
        try {
            $validatedParams = $this->validateExportParams($params);
            
            $cacheKey = $this->generateCacheKey('statistics', $validatedParams);
            $cachedResult = $this->cache->get($cacheKey);
            
            if ($cachedResult && !($validatedParams['real_time'] ?? false)) {
                return $cachedResult;
            }

            $statistics = $this->generateStatistics($validatedParams);
            
            $response = [
                'success' => true,
                'data' => $statistics,
                'generated_at' => date('Y-m-d H:i:s'),
                'cache_duration' => $validatedParams['real_time'] ? 0 : 300,
                'api_version' => '1.0'
            ];

            if (!($validatedParams['real_time'] ?? false)) {
                $this->cache->save($cacheKey, $response, 300);
            }

            $this->logExportActivity('statistics', $validatedParams, 1);

            return $response;

        } catch (\Exception $e) {
            log_message('error', 'Statistics JSON API export failed: ' . $e->getMessage());
            return $this->buildErrorResponse('Statistics export failed: ' . $e->getMessage());
        }
    }

    /**
     * Real-time sync endpoint for live data updates
     */
    public function syncData(array $params = []): array
    {
        try {
            $validatedParams = $this->validateSyncParams($params);
            
            // Get changes since last sync timestamp
            $changes = $this->getChangesSince($validatedParams['since_timestamp'], $validatedParams);
            
            $response = [
                'success' => true,
                'sync_timestamp' => time(),
                'changes' => $changes,
                'has_more' => $this->hasMoreChanges($validatedParams['since_timestamp'], $validatedParams),
                'next_sync_url' => $this->buildNextSyncUrl($validatedParams),
                'api_version' => '1.0'
            ];

            $this->logExportActivity('sync', $validatedParams, count($changes));

            return $response;

        } catch (\Exception $e) {
            log_message('error', 'Real-time sync failed: ' . $e->getMessage());
            return $this->buildErrorResponse('Sync failed: ' . $e->getMessage());
        }
    }

    /**
     * Validate export parameters
     */
    protected function validateExportParams(array $params): array
    {
        $validated = [];
        
        // Pagination
        $validated['page'] = max(1, (int)($params['page'] ?? 1));
        $validated['page_size'] = min($this->maxPageSize, max(1, (int)($params['page_size'] ?? $this->defaultPageSize)));
        
        // Filtering
        $validated['form_id'] = $this->validateFormId($params['form_id'] ?? null);
        $validated['user_id'] = $this->validateUserId($params['user_id'] ?? null);
        $validated['status'] = $this->validateStatus($params['status'] ?? null);
        $validated['date_from'] = $this->validateDate($params['date_from'] ?? null);
        $validated['date_to'] = $this->validateDate($params['date_to'] ?? null);
        
        // Output format
        $validated['include_metadata'] = filter_var($params['include_metadata'] ?? true, FILTER_VALIDATE_BOOLEAN);
        $validated['include_media'] = filter_var($params['include_media'] ?? false, FILTER_VALIDATE_BOOLEAN);
        $validated['flatten_json'] = filter_var($params['flatten_json'] ?? false, FILTER_VALIDATE_BOOLEAN);
        $validated['real_time'] = filter_var($params['real_time'] ?? false, FILTER_VALIDATE_BOOLEAN);
        
        // Field selection
        if (isset($params['fields'])) {
            $validated['fields'] = $this->validateFields($params['fields']);
        }
        
        // Sorting
        $validated['sort_by'] = $this->validateSortField($params['sort_by'] ?? 'created_at');
        $validated['sort_order'] = in_array(strtoupper($params['sort_order'] ?? 'DESC'), ['ASC', 'DESC']) 
                                 ? strtoupper($params['sort_order']) : 'DESC';
        
        return array_filter($validated, function($value) {
            return $value !== null;
        });
    }

    /**
     * Validate sync parameters
     */
    protected function validateSyncParams(array $params): array
    {
        $validated = [];
        
        $validated['since_timestamp'] = $this->validateTimestamp($params['since_timestamp'] ?? time() - 3600);
        $validated['entity_types'] = $this->validateEntityTypes($params['entity_types'] ?? ['submissions']);
        $validated['limit'] = min(1000, max(1, (int)($params['limit'] ?? 100)));
        
        return $validated;
    }

    /**
     * Build submission query with filters
     */
    protected function buildSubmissionQuery(array $params)
    {
        $query = $this->submissionModel->select([
            'kobo_submissions.*',
            'kobo_forms.title as form_title',
            'kobo_forms.form_id as form_identifier',
            'kobo_users.username as submitted_by'
        ])
        ->join('kobo_forms', 'kobo_forms.id = kobo_submissions.form_id', 'left')
        ->join('kobo_users', 'kobo_users.id = kobo_submissions.user_id', 'left');

        // Apply filters
        if (isset($params['form_id'])) {
            $query->where('kobo_submissions.form_id', $params['form_id']);
        }
        
        if (isset($params['user_id'])) {
            $query->where('kobo_submissions.user_id', $params['user_id']);
        }
        
        if (isset($params['status'])) {
            $query->where('kobo_submissions.status', $params['status']);
        }
        
        if (isset($params['date_from'])) {
            $query->where('kobo_submissions.created_at >=', $params['date_from']);
        }
        
        if (isset($params['date_to'])) {
            $query->where('kobo_submissions.created_at <=', $params['date_to']);
        }

        // Apply sorting
        $query->orderBy('kobo_submissions.' . $params['sort_by'], $params['sort_order']);

        return $query;
    }

    /**
     * Build forms query with filters
     */
    protected function buildFormsQuery(array $params)
    {
        $query = $this->formModel->select([
            'kobo_forms.*',
            'kobo_users.username as created_by',
            '(SELECT COUNT(*) FROM kobo_submissions WHERE kobo_submissions.form_id = kobo_forms.id) as submission_count'
        ])
        ->join('kobo_users', 'kobo_users.id = kobo_forms.created_by', 'left');

        if (isset($params['status'])) {
            $query->where('kobo_forms.status', $params['status']);
        }
        
        if (isset($params['date_from'])) {
            $query->where('kobo_forms.created_at >=', $params['date_from']);
        }
        
        if (isset($params['date_to'])) {
            $query->where('kobo_forms.created_at <=', $params['date_to']);
        }

        $query->orderBy('kobo_forms.' . ($params['sort_by'] ?? 'created_at'), $params['sort_order'] ?? 'DESC');

        return $query;
    }

    /**
     * Apply pagination to query
     */
    protected function applyPagination($query, array $params)
    {
        $offset = ($params['page'] - 1) * $params['page_size'];
        return $query->limit($params['page_size'], $offset);
    }

    /**
     * Get total count for pagination
     */
    protected function getTotalCount($query, array $params): int
    {
        $countQuery = clone $query;
        return $countQuery->countAllResults(false);
    }

    /**
     * Transform submission data
     */
    protected function transformSubmissionData(array $submissions, array $params): array
    {
        $transformed = [];
        
        foreach ($submissions as $submission) {
            $item = [
                'id' => $submission['id'],
                'submission_id' => $submission['submission_id'],
                'form_id' => $submission['form_id'],
                'form_title' => $submission['form_title'] ?? null,
                'form_identifier' => $submission['form_identifier'] ?? null,
                'device_id' => $submission['device_id'],
                'status' => $submission['status'],
                'submitted_at' => $submission['created_at'],
                'submitted_by' => $submission['submitted_by'] ?? null
            ];

            // Include JSON data
            if (!empty($submission['json_data'])) {
                $jsonData = json_decode($submission['json_data'], true);
                
                if ($params['flatten_json'] ?? false) {
                    $item['data'] = $this->flattenJsonData($jsonData);
                } else {
                    $item['data'] = $jsonData;
                }
            }

            // Include metadata if requested
            if ($params['include_metadata'] ?? true) {
                $item['metadata'] = [
                    'xml_size' => strlen($submission['xml_content'] ?? ''),
                    'json_size' => strlen($submission['json_data'] ?? ''),
                    'processed_at' => $submission['processed_at'],
                    'updated_at' => $submission['updated_at']
                ];
            }

            // Include media files if requested
            if ($params['include_media'] ?? false) {
                $item['media_files'] = $this->getSubmissionMediaFiles($submission['id']);
            }

            // Filter fields if specified
            if (isset($params['fields'])) {
                $item = $this->filterFields($item, $params['fields']);
            }

            $transformed[] = $item;
        }

        return $transformed;
    }

    /**
     * Transform forms data
     */
    protected function transformFormsData(array $forms, array $params): array
    {
        $transformed = [];
        
        foreach ($forms as $form) {
            $item = [
                'id' => $form['id'],
                'form_id' => $form['form_id'],
                'title' => $form['title'],
                'description' => $form['description'],
                'version' => $form['version'],
                'status' => $form['status'],
                'created_at' => $form['created_at'],
                'created_by' => $form['created_by'] ?? null,
                'submission_count' => (int)($form['submission_count'] ?? 0)
            ];

            if ($params['include_metadata'] ?? true) {
                $item['metadata'] = [
                    'xml_size' => strlen($form['xml_content'] ?? ''),
                    'last_modified' => $form['updated_at'],
                    'is_active' => $form['status'] === 'active'
                ];
            }

            if (isset($params['fields'])) {
                $item = $this->filterFields($item, $params['fields']);
            }

            $transformed[] = $item;
        }

        return $transformed;
    }

    /**
     * Generate statistics
     */
    protected function generateStatistics(array $params): array
    {
        $db = \Config\Database::connect();
        
        $statistics = [
            'overview' => [
                'total_forms' => $this->formModel->countAll(),
                'active_forms' => $this->formModel->where('status', 'active')->countAllResults(),
                'total_submissions' => $this->submissionModel->countAll(),
                'processed_submissions' => $this->submissionModel->where('status', 'processed')->countAllResults()
            ],
            'recent_activity' => [
                'submissions_today' => $this->getSubmissionsCount('today'),
                'submissions_this_week' => $this->getSubmissionsCount('week'),
                'submissions_this_month' => $this->getSubmissionsCount('month')
            ],
            'form_statistics' => $this->getFormStatistics(),
            'user_statistics' => $this->getUserStatistics(),
            'performance_metrics' => $this->getPerformanceMetrics()
        ];

        return $statistics;
    }

    /**
     * Get changes since timestamp for real-time sync
     */
    protected function getChangesSince(int $timestamp, array $params): array
    {
        $changes = [];
        $entityTypes = $params['entity_types'] ?? ['submissions'];
        $limit = $params['limit'] ?? 100;

        foreach ($entityTypes as $entityType) {
            switch ($entityType) {
                case 'submissions':
                    $changes['submissions'] = $this->getSubmissionChanges($timestamp, $limit);
                    break;
                case 'forms':
                    $changes['forms'] = $this->getFormChanges($timestamp, $limit);
                    break;
            }
        }

        return $changes;
    }

    /**
     * Build paginated response
     */
    protected function buildPaginatedResponse(array $data): array
    {
        $params = $data['params'];
        $totalPages = ceil($data['total_count'] / $params['page_size']);
        
        return [
            'success' => true,
            'data' => $data['data'],
            'pagination' => [
                'current_page' => $params['page'],
                'page_size' => $params['page_size'],
                'total_count' => $data['total_count'],
                'total_pages' => $totalPages,
                'has_next' => $params['page'] < $totalPages,
                'has_previous' => $params['page'] > 1,
                'next_page_url' => $params['page'] < $totalPages ? $this->buildPageUrl($params, $params['page'] + 1) : null,
                'previous_page_url' => $params['page'] > 1 ? $this->buildPageUrl($params, $params['page'] - 1) : null
            ],
            'filters' => $this->getActiveFilters($params),
            'generated_at' => date('Y-m-d H:i:s'),
            'api_version' => '1.0'
        ];
    }

    /**
     * Build error response
     */
    protected function buildErrorResponse(string $message, int $code = 400): array
    {
        return [
            'success' => false,
            'error' => [
                'message' => $message,
                'code' => $code,
                'timestamp' => date('Y-m-d H:i:s')
            ],
            'api_version' => '1.0'
        ];
    }

    /**
     * Helper methods for validation
     */
    protected function validateFormId($formId): ?int
    {
        if ($formId === null) return null;
        $id = (int)$formId;
        return $id > 0 && $this->formModel->find($id) ? $id : null;
    }

    protected function validateUserId($userId): ?int
    {
        if ($userId === null) return null;
        $id = (int)$userId;
        return $id > 0 && $this->userModel->find($id) ? $id : null;
    }

    protected function validateStatus($status): ?string
    {
        if ($status === null) return null;
        $validStatuses = ['draft', 'active', 'inactive', 'processed', 'failed'];
        return in_array($status, $validStatuses) ? $status : null;
    }

    protected function validateDate($date): ?string
    {
        if ($date === null) return null;
        $timestamp = strtotime($date);
        return $timestamp !== false ? date('Y-m-d H:i:s', $timestamp) : null;
    }

    protected function validateFields($fields): array
    {
        if (is_string($fields)) {
            $fields = explode(',', $fields);
        }
        return array_map('trim', (array)$fields);
    }

    protected function validateSortField($field): string
    {
        $validFields = ['id', 'created_at', 'updated_at', 'title', 'status'];
        return in_array($field, $validFields) ? $field : 'created_at';
    }

    protected function validateTimestamp($timestamp): int
    {
        return is_numeric($timestamp) ? (int)$timestamp : time() - 3600;
    }

    protected function validateEntityTypes($types): array
    {
        $validTypes = ['submissions', 'forms', 'users'];
        if (is_string($types)) {
            $types = explode(',', $types);
        }
        return array_intersect((array)$types, $validTypes) ?: ['submissions'];
    }

    /**
     * Cache and utility methods
     */
    protected function generateCacheKey(string $type, array $params): string
    {
        ksort($params);
        return 'json_api_' . $type . '_' . md5(serialize($params));
    }

    protected function flattenJsonData(array $data, string $prefix = ''): array
    {
        $flattened = [];
        
        foreach ($data as $key => $value) {
            $newKey = $prefix ? $prefix . '.' . $key : $key;
            
            if (is_array($value)) {
                $flattened = array_merge($flattened, $this->flattenJsonData($value, $newKey));
            } else {
                $flattened[$newKey] = $value;
            }
        }
        
        return $flattened;
    }

    protected function filterFields(array $data, array $fields): array
    {
        return array_intersect_key($data, array_flip($fields));
    }

    protected function buildPageUrl(array $params, int $page): string
    {
        $params['page'] = $page;
        return base_url('api/v1/export/submissions?' . http_build_query($params));
    }

    protected function buildNextSyncUrl(array $params): string
    {
        $params['since_timestamp'] = time();
        return base_url('api/v1/sync?' . http_build_query($params));
    }

    protected function getActiveFilters(array $params): array
    {
        $filters = [];
        $filterKeys = ['form_id', 'user_id', 'status', 'date_from', 'date_to'];
        
        foreach ($filterKeys as $key) {
            if (isset($params[$key])) {
                $filters[$key] = $params[$key];
            }
        }
        
        return $filters;
    }

    /**
     * Statistics helper methods
     */
    protected function getSubmissionsCount(string $period): int
    {
        $query = $this->submissionModel;
        
        switch ($period) {
            case 'today':
                $query->where('DATE(created_at)', date('Y-m-d'));
                break;
            case 'week':
                $query->where('created_at >=', date('Y-m-d', strtotime('-7 days')));
                break;
            case 'month':
                $query->where('created_at >=', date('Y-m-d', strtotime('-30 days')));
                break;
        }
        
        return $query->countAllResults();
    }

    protected function getFormStatistics(): array
    {
        $db = \Config\Database::connect();
        
        $query = $db->query("
            SELECT 
                f.id,
                f.title,
                f.status,
                COUNT(s.id) as submission_count,
                MAX(s.created_at) as last_submission
            FROM kobo_forms f
            LEFT JOIN kobo_submissions s ON f.id = s.form_id
            GROUP BY f.id, f.title, f.status
            ORDER BY submission_count DESC
            LIMIT 10
        ");
        
        return $query->getResultArray();
    }

    protected function getUserStatistics(): array
    {
        $db = \Config\Database::connect();
        
        $query = $db->query("
            SELECT 
                u.id,
                u.username,
                u.role,
                COUNT(s.id) as submission_count,
                MAX(s.created_at) as last_activity
            FROM kobo_users u
            LEFT JOIN kobo_submissions s ON u.id = s.user_id
            GROUP BY u.id, u.username, u.role
            ORDER BY submission_count DESC
            LIMIT 10
        ");
        
        return $query->getResultArray();
    }

    protected function getPerformanceMetrics(): array
    {
        return [
            'average_processing_time' => $this->getAverageProcessingTime(),
            'api_response_time' => $this->getAverageApiResponseTime(),
            'cache_hit_ratio' => $this->getCacheHitRatio(),
            'error_rate' => $this->getErrorRate()
        ];
    }

    protected function getSubmissionChanges(int $timestamp, int $limit): array
    {
        return $this->submissionModel
                    ->where('updated_at >', date('Y-m-d H:i:s', $timestamp))
                    ->orderBy('updated_at', 'ASC')
                    ->limit($limit)
                    ->findAll();
    }

    protected function getFormChanges(int $timestamp, int $limit): array
    {
        return $this->formModel
                    ->where('updated_at >', date('Y-m-d H:i:s', $timestamp))
                    ->orderBy('updated_at', 'ASC')
                    ->limit($limit)
                    ->findAll();
    }

    protected function hasMoreChanges(int $timestamp, array $params): bool
    {
        $entityTypes = $params['entity_types'] ?? ['submissions'];
        
        foreach ($entityTypes as $entityType) {
            switch ($entityType) {
                case 'submissions':
                    if ($this->submissionModel->where('updated_at >', date('Y-m-d H:i:s', $timestamp))->countAllResults() > 0) {
                        return true;
                    }
                    break;
                case 'forms':
                    if ($this->formModel->where('updated_at >', date('Y-m-d H:i:s', $timestamp))->countAllResults() > 0) {
                        return true;
                    }
                    break;
            }
        }
        
        return false;
    }

    protected function getSubmissionMediaFiles(int $submissionId): array
    {
        // Would implement media file retrieval
        return [];
    }

    protected function logExportActivity(string $type, array $params, int $recordCount): void
    {
        try {
            $this->auditModel->logActivity([
                'user_id' => $params['user_id'] ?? null,
                'action' => 'json_api_export',
                'resource_type' => $type,
                'details' => json_encode([
                    'export_type' => $type,
                    'record_count' => $recordCount,
                    'filters' => $this->getActiveFilters($params),
                    'real_time' => $params['real_time'] ?? false
                ])
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Failed to log export activity: ' . $e->getMessage());
        }
    }

    protected function getAverageProcessingTime(): float
    {
        // Would implement processing time calculation
        return 0.0;
    }

    protected function getAverageApiResponseTime(): float
    {
        // Would implement API response time calculation
        return 0.0;
    }

    protected function getCacheHitRatio(): float
    {
        // Would implement cache hit ratio calculation
        return 0.0;
    }

    protected function getErrorRate(): float
    {
        // Would implement error rate calculation
        return 0.0;
    }
}