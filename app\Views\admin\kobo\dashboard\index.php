<?= $this->extend('admin/layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2 fw-bold">
        <i class="bi bi-speedometer2 text-primary me-2"></i>
        Dashboard Overview
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button class="btn btn-sm btn-outline-secondary" id="refreshStats">
            <i class="bi bi-arrow-clockwise me-1"></i>
            Refresh
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card primary h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Total Forms</div>
                        <div class="h2 fw-bold" id="totalForms"><?= $stats['total_forms'] ?? 0 ?></div>
                        <div class="small">
                            <span class="text-white-50">Active: </span>
                            <span class="fw-bold"><?= $stats['status_counts']['active'] ?? 0 ?></span>
                        </div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-file-earmark-text" style="font-size: 2.5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card success h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Total Users</div>
                        <div class="h2 fw-bold" id="totalUsers"><?= $user_stats['total'] ?? 0 ?></div>
                        <div class="small">
                            <span class="text-white-50">Active: </span>
                            <span class="fw-bold"><?= $user_stats['active'] ?? 0 ?></span>
                        </div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people" style="font-size: 2.5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card warning h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Total Submissions</div>
                        <div class="h2 fw-bold" id="totalSubmissions"><?= $submission_stats['total'] ?? 0 ?></div>
                        <div class="small">
                            <span class="text-white-50">Processed: </span>
                            <span class="fw-bold"><?= $submission_stats['processed'] ?? 0 ?></span>
                        </div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-database" style="font-size: 2.5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card info h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">System Health</div>
                        <div class="h4 fw-bold" id="systemHealth">
                            <?= ($system_health['status'] ?? 'Unknown') === 'healthy' ? 'Excellent' : 'Needs Attention' ?>
                        </div>
                        <div class="small">
                            <span class="text-white-50">Uptime: </span>
                            <span class="fw-bold"><?= $system_health['uptime'] ?? 'N/A' ?></span>
                        </div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-heart-pulse" style="font-size: 2.5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart me-2"></i>
                    Submission Activity (Last 30 Days)
                </h5>
            </div>
            <div class="card-body">
                <canvas id="submissionChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart me-2"></i>
                    Form Status Distribution
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity and System Information -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    Recent Activity
                </h5>
                <a href="<?= base_url('admin/kobo/audit-logs') ?>" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($recent_activities)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($recent_activities, 0, 10) as $activity): ?>
                            <div class="list-group-item activity-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <div>
                                        <h6 class="mb-1">
                                            <i class="bi bi-<?= $this->getActivityIcon($activity['action']) ?> me-2 text-primary"></i>
                                            <?= $this->formatActivityTitle($activity) ?>
                                        </h6>
                                        <p class="mb-1 text-muted small">
                                            <?= $activity['user'] ?? 'System' ?> • <?= $activity['entity_type'] ?>
                                        </p>
                                    </div>
                                    <small class="text-muted"><?= $activity['time_ago'] ?></small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4 text-muted">
                        <i class="bi bi-clock-history" style="font-size: 3rem; opacity: 0.3;"></i>
                        <p class="mt-2">No recent activity</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Version:</span>
                            <span class="fw-bold">1.0.0</span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Database:</span>
                            <span class="fw-bold text-success">Connected</span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Storage:</span>
                            <span class="fw-bold"><?= $system_info['storage_used'] ?? 'N/A' ?></span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Memory:</span>
                            <span class="fw-bold"><?= $system_info['memory_used'] ?? 'N/A' ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-tools me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= base_url('admin/kobo/forms/create') ?>" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus-circle me-2"></i>
                        Create New Form
                    </a>
                    <a href="<?= base_url('admin/kobo/users/create') ?>" class="btn btn-success btn-sm">
                        <i class="bi bi-person-plus me-2"></i>
                        Add New User
                    </a>
                    <a href="<?= base_url('admin/kobo/submissions/export') ?>" class="btn btn-info btn-sm">
                        <i class="bi bi-download me-2"></i>
                        Export Data
                    </a>
                    <button class="btn btn-warning btn-sm" onclick="runSystemMaintenance()">
                        <i class="bi bi-gear me-2"></i>
                        System Maintenance
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Dashboard data
    const dashboardData = {
        submissions: <?= json_encode($submission_timeline ?? []) ?>,
        statusDistribution: <?= json_encode($status_distribution ?? []) ?>
    };

    // Initialize charts
    document.addEventListener('DOMContentLoaded', function() {
        initializeSubmissionChart();
        initializeStatusChart();
        initializeRealTimeUpdates();
    });

    // Submission activity chart
    function initializeSubmissionChart() {
        const ctx = document.getElementById('submissionChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: dashboardData.submissions.map(item => item.date),
                datasets: [{
                    label: 'Submissions',
                    data: dashboardData.submissions.map(item => item.count),
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.05)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.05)'
                        }
                    }
                }
            }
        });
    }

    // Status distribution chart
    function initializeStatusChart() {
        const ctx = document.getElementById('statusChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Active', 'Inactive', 'Draft'],
                datasets: [{
                    data: [
                        dashboardData.statusDistribution.active || 0,
                        dashboardData.statusDistribution.inactive || 0,
                        dashboardData.statusDistribution.draft || 0
                    ],
                    backgroundColor: [
                        '#38ef7d',
                        '#f5576c',
                        '#f093fb'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }

    // Real-time updates
    function initializeRealTimeUpdates() {
        // Update statistics every 30 seconds
        setInterval(refreshStatistics, 30000);
    }

    // Refresh statistics
    function refreshStatistics() {
        ajaxRequest('<?= base_url('admin/kobo/dashboard/stats') ?>', 'GET', null, 
            function(data) {
                if (data.success) {
                    updateStatCards(data.stats);
                }
            }
        );
    }

    // Update stat cards
    function updateStatCards(stats) {
        document.getElementById('totalForms').textContent = stats.total_forms || 0;
        document.getElementById('totalUsers').textContent = stats.total_users || 0;
        document.getElementById('totalSubmissions').textContent = stats.total_submissions || 0;
    }

    // Refresh button handler
    document.getElementById('refreshStats').addEventListener('click', function() {
        const originalText = showLoading(this);
        refreshStatistics();
        
        setTimeout(() => {
            hideLoading(this, originalText);
            showToast('Statistics refreshed successfully', 'success');
        }, 1000);
    });

    // System maintenance
    function runSystemMaintenance() {
        if (confirm('Are you sure you want to run system maintenance? This may take a few minutes.')) {
            ajaxRequest('<?= base_url('admin/kobo/dashboard/maintenance') ?>', 'POST', null,
                function(data) {
                    if (data.success) {
                        showToast('System maintenance completed successfully', 'success');
                    } else {
                        showToast('Maintenance failed: ' + (data.message || 'Unknown error'), 'danger');
                    }
                },
                function(error) {
                    showToast('Maintenance request failed', 'danger');
                }
            );
        }
    }
</script>
<?= $this->endSection() ?>

<?php
// Helper functions for view
if (!function_exists('getActivityIcon')) {
    function getActivityIcon($action) {
        $icons = [
            'created' => 'plus-circle',
            'updated' => 'pencil-square',
            'deleted' => 'trash',
            'login_success' => 'box-arrow-in-right',
            'logout' => 'box-arrow-right',
            'uploaded' => 'cloud-upload',
            'exported' => 'download'
        ];
        
        return $icons[$action] ?? 'activity';
    }
}

if (!function_exists('formatActivityTitle')) {
    function formatActivityTitle($activity) {
        $user = $activity['user'] ?? 'System';
        $action = $activity['action'];
        $entityType = str_replace('_', ' ', $activity['entity_type']);
        
        return "{$user} {$action} a {$entityType}";
    }
}
?>