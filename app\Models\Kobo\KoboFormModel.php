<?php

namespace App\Models\Kobo;

use CodeIgniter\Model;

/**
 * Kobo Form Model
 * 
 * Handles CRUD operations for kobo_forms table
 * Provides XML content validation and form version management
 */
class KoboFormModel extends Model
{
    protected $table = 'kobo_forms';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'form_id',
        'form_name',
        'xml_content',
        'version',
        'status',
        'created_by',
        'created_at',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation - Simplified for debugging
    protected $validationRules = [
        'form_id' => 'required|max_length[255]',
        'form_name' => 'required|max_length[255]',
        'xml_content' => 'required',
        'version' => 'permit_empty|max_length[50]',
        'status' => 'permit_empty|in_list[active,inactive,draft]',
        'created_by' => 'permit_empty|integer'
    ];

    protected $validationMessages = [
        'form_id' => [
            'required' => 'Form ID is required',
            'alpha_numeric_space' => 'Form ID can only contain alphanumeric characters and spaces',
            'max_length' => 'Form ID cannot exceed 255 characters'
        ],
        'form_name' => [
            'required' => 'Form name is required',
            'string' => 'Form name must be a valid string',
            'max_length' => 'Form name cannot exceed 255 characters'
        ],
        'xml_content' => [
            'required' => 'XML content is required'
        ],
        'version' => [
            'required' => 'Version is required',
            'string' => 'Version must be a valid string',
            'max_length' => 'Version cannot exceed 50 characters'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be active, inactive, or draft'
        ],
        'created_by' => [
            'required' => 'Created by user ID is required',
            'integer' => 'Created by must be a valid user ID'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks - Temporarily disabled for debugging
    protected $allowCallbacks = false;
    // protected $beforeInsert = ['validateXmlContent'];
    // protected $beforeUpdate = ['validateXmlContent'];

    /**
     * Validate XML content before saving
     */
    protected function validateXmlContent(array $data)
    {
        if (isset($data['data']['xml_content'])) {
            if (!$this->isValidXml($data['data']['xml_content'])) {
                throw new \CodeIgniter\Database\Exceptions\DataException('Invalid XML content provided');
            }
        }
        return $data;
    }

    /**
     * Create a new form
     */
    public function createForm(array $formData): int|false
    {
        // Set default values
        $formData['status'] = $formData['status'] ?? 'draft';
        $formData['version'] = $formData['version'] ?? '1.0';
        
        // Validate XML content
        if (!$this->isValidXml($formData['xml_content'])) {
            return false;
        }

        return $this->insert($formData);
    }

    /**
     * Find form by form_id
     */
    public function findByFormId(string $formId): array|null
    {
        return $this->where('form_id', $formId)
                    ->where('status !=', 'inactive')
                    ->first();
    }

    /**
     * Get active forms
     */
    public function getActiveForms(): array
    {
        return $this->where('status', 'active')
                    ->orderBy('form_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get forms by status
     */
    public function getFormsByStatus(string $status): array
    {
        return $this->where('status', $status)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get forms created by user
     */
    public function getFormsByUser(int $userId): array
    {
        return $this->where('created_by', $userId)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Activate form
     */
    public function activateForm(int $formId): bool
    {
        return $this->update($formId, ['status' => 'active']);
    }

    /**
     * Deactivate form
     */
    public function deactivateForm(int $formId): bool
    {
        return $this->update($formId, ['status' => 'inactive']);
    }

    /**
     * Set form as draft
     */
    public function setDraftStatus(int $formId): bool
    {
        return $this->update($formId, ['status' => 'draft']);
    }

    /**
     * Update form version
     */
    public function updateVersion(int $formId, string $version): bool
    {
        return $this->update($formId, ['version' => $version]);
    }

    /**
     * Update form XML content
     */
    public function updateXmlContent(int $formId, string $xmlContent): bool
    {
        if (!$this->isValidXml($xmlContent)) {
            return false;
        }

        return $this->update($formId, ['xml_content' => $xmlContent]);
    }

    /**
     * Check if XML content is valid
     */
    public function isValidXml(string $xmlContent): bool
    {
        // Disable libxml errors and allow user to fetch error information as needed
        libxml_use_internal_errors(true);
        
        // Try to parse the XML
        $doc = simplexml_load_string($xmlContent);
        
        // Check if parsing was successful
        if ($doc === false) {
            // Get the errors
            $errors = libxml_get_errors();
            libxml_clear_errors();
            return false;
        }

        return true;
    }

    /**
     * Validate XForm structure
     */
    public function isValidXForm(string $xmlContent): bool
    {
        if (!$this->isValidXml($xmlContent)) {
            return false;
        }

        try {
            $xml = simplexml_load_string($xmlContent);
            
            // Check for required XForm elements
            if (!isset($xml->head) || !isset($xml->body)) {
                return false;
            }

            // Check for model element in head
            if (!isset($xml->head->model)) {
                return false;
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Extract form metadata from XML
     */
    public function extractFormMetadata(string $xmlContent): array|false
    {
        if (!$this->isValidXml($xmlContent)) {
            return false;
        }

        try {
            $xml = simplexml_load_string($xmlContent);
            $metadata = [];

            // Extract form ID
            if (isset($xml->head->model->instance)) {
                $instance = $xml->head->model->instance->children();
                if ($instance->count() > 0) {
                    $rootElement = $instance[0];
                    $metadata['form_id'] = $rootElement->getName();
                }
            }

            // Extract title
            if (isset($xml->head->title)) {
                $metadata['title'] = (string)$xml->head->title;
            }

            // Extract version
            if (isset($xml['version'])) {
                $metadata['version'] = (string)$xml['version'];
            }

            return $metadata;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get form statistics
     */
    public function getFormStats(): array
    {
        $db = \Config\Database::connect();
        
        // Get submission counts per form
        $submissionQuery = $db->query("
            SELECT 
                f.id,
                f.form_name,
                f.status,
                COUNT(s.id) as submission_count
            FROM kobo_forms f
            LEFT JOIN kobo_submissions s ON f.form_id = s.form_id
            GROUP BY f.id, f.form_name, f.status
            ORDER BY submission_count DESC
        ");

        $results = $submissionQuery->getResultArray();
        
        // Get status counts
        $statusQuery = $db->query("
            SELECT status, COUNT(*) as count
            FROM kobo_forms
            GROUP BY status
        ");

        $statusCounts = [];
        foreach ($statusQuery->getResultArray() as $row) {
            $statusCounts[$row['status']] = $row['count'];
        }

        return [
            'total_forms' => $this->countAll(),
            'status_counts' => $statusCounts,
            'forms_with_submissions' => $results
        ];
    }

    /**
     * Get forms with pagination and search
     */
    public function getFormsWithPagination(int $perPage = 20, string $search = ''): array
    {
        $builder = $this->select('kobo_forms.*, kobo_users.username as created_by_name')
                        ->join('kobo_users', 'kobo_users.id = kobo_forms.created_by', 'left');
        
        if (!empty($search)) {
            $builder->groupStart()
                    ->like('form_name', $search)
                    ->orLike('form_id', $search)
                    ->groupEnd();
        }

        $builder->orderBy('created_at', 'DESC');

        return [
            'forms' => $builder->paginate($perPage),
            'pager' => $this->pager
        ];
    }

    /**
     * Get latest version of form
     */
    public function getLatestVersion(string $formId): string
    {
        $form = $this->where('form_id', $formId)
                     ->orderBy('version', 'DESC')
                     ->first();

        return $form ? $form['version'] : '1.0';
    }

    /**
     * Check if form has submissions
     */
    public function hasSubmissions(int $formId): bool
    {
        $submissionModel = new \App\Models\Kobo\KoboSubmissionModel();
        $form = $this->find($formId);
        
        if (!$form) {
            return false;
        }

        return $submissionModel->where('form_id', $form['form_id'])->countAllResults() > 0;
    }

    /**
     * Clone form with new version
     */
    public function cloneForm(int $originalFormId, string $newVersion): int|false
    {
        $originalForm = $this->find($originalFormId);
        
        if (!$originalForm) {
            return false;
        }

        $newForm = $originalForm;
        unset($newForm['id']); // Remove ID for new record
        $newForm['version'] = $newVersion;
        $newForm['status'] = 'draft';

        return $this->insert($newForm);
    }

    /**
     * Get form manifest (for media files)
     */
    public function getFormManifest(string $formId): array
    {
        $mediaModel = new \App\Models\Kobo\KoboMediaFileModel();
        return $mediaModel->where('form_id', $formId)
                          ->where('status', 'active')
                          ->findAll();
    }

    /**
     * Validate form permissions for user
     */
    public function hasUserAccess(string $formId, int $userId): bool
    {
        $accessModel = new \App\Models\Kobo\KoboFormAccessModel();
        return $accessModel->hasAccess($userId, $formId);
    }

    /**
     * Get OpenRosa compliant form list
     */
    public function getOpenRosaFormList(int $userId = null): array
    {
        $builder = $this->where('status', 'active');
        
        // If user ID provided, check form access
        if ($userId) {
            $accessModel = new \App\Models\Kobo\KoboFormAccessModel();
            $accessibleForms = $accessModel->getUserAccessibleForms($userId);
            if (!empty($accessibleForms)) {
                $builder->whereIn('form_id', $accessibleForms);
            } else {
                return []; // User has no access to any forms
            }
        }

        $forms = $builder->findAll();
        $formList = [];

        foreach ($forms as $form) {
            $formList[] = [
                'formID' => $form['form_id'],
                'name' => $form['form_name'],
                'version' => $form['version'],
                'hash' => 'md5:' . md5($form['xml_content']),
                'downloadUrl' => base_url("api/v1/forms/{$form['form_id']}"),
                'manifestUrl' => base_url("api/v1/forms/{$form['form_id']}/manifest")
            ];
        }

        return $formList;
    }
}