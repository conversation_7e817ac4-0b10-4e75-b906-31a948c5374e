<?= $this->extend('admin/layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2 fw-bold">
        <i class="bi bi-<?= isset($user) ? 'pencil-square' : 'person-plus' ?> text-primary me-2"></i>
        <?= isset($user) ? 'Edit User' : 'Add New User' ?>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="<?= base_url('admin/kobo/users') ?>" class="btn btn-sm btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i>
            Back to Users
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- User Form Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-badge me-2"></i>
                    User Information
                </h5>
            </div>
            <div class="card-body">
                <form id="userForm">
                    <?= csrf_field() ?>
                    <?php if (isset($user)): ?>
                        <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                    <?php endif; ?>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="<?= $user['username'] ?? '' ?>" required>
                            <div class="form-text">
                                Username must be unique and contain only letters, numbers, and basic punctuation
                            </div>
                            <div class="invalid-feedback" id="username_error"></div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?= $user['email'] ?? '' ?>" required>
                            <div class="form-text">
                                Email must be unique and valid
                            </div>
                            <div class="invalid-feedback" id="email_error"></div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="password" class="form-label">
                                Password <?= isset($user) ? '(Leave blank to keep current)' : '<span class="text-danger">*</span>' ?>
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" 
                                       <?= isset($user) ? '' : 'required' ?>>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="bi bi-eye" id="passwordIcon"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                Password must be at least 8 characters with uppercase, lowercase, number, and special character
                            </div>
                            <div class="invalid-feedback" id="password_error"></div>
                            
                            <!-- Password strength indicator -->
                            <div class="mt-2" id="passwordStrength" style="display: none;">
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar" role="progressbar" id="strengthBar"></div>
                                </div>
                                <small class="text-muted" id="strengthText"></small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="confirmPassword" class="form-label">
                                Confirm Password <?= isset($user) ? '' : '<span class="text-danger">*</span>' ?>
                            </label>
                            <input type="password" class="form-control" id="confirmPassword" name="confirm_password" 
                                   <?= isset($user) ? '' : 'required' ?>>
                            <div class="invalid-feedback" id="confirmPassword_error"></div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="role" class="form-label">User Role <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">Select Role</option>
                                <?php foreach ($roles as $roleValue): ?>
                                    <option value="<?= $roleValue ?>" 
                                            <?= ($user['role'] ?? '') === $roleValue ? 'selected' : '' ?>>
                                        <?= ucfirst($roleValue) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text" id="roleDescription">
                                Select a role to see its permissions
                            </div>
                            <div class="invalid-feedback" id="role_error"></div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?= ($user['status'] ?? 'active') === 'active' ? 'selected' : '' ?>>Active</option>
                                <option value="inactive" <?= ($user['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                            <div class="form-text">
                                Inactive users cannot log in to the system
                            </div>
                        </div>
                        
                        <?php if (isset($user)): ?>
                        <div class="col-12">
                            <hr>
                            <h6 class="text-muted">Account Information</h6>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <small class="text-muted">User ID:</small>
                                    <div class="fw-bold"><?= $user['id'] ?></div>
                                </div>
                                <div class="col-md-4">
                                    <small class="text-muted">Created:</small>
                                    <div class="fw-bold"><?= date('M j, Y H:i', strtotime($user['created_at'])) ?></div>
                                </div>
                                <div class="col-md-4">
                                    <small class="text-muted">Last Updated:</small>
                                    <div class="fw-bold"><?= date('M j, Y H:i', strtotime($user['updated_at'])) ?></div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary me-2" id="submitBtn">
                                <i class="bi bi-save me-1"></i>
                                <?= isset($user) ? 'Update User' : 'Create User' ?>
                            </button>
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="resetForm()">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                Reset
                            </button>
                            <?php if (isset($user)): ?>
                            <button type="button" class="btn btn-outline-warning me-2" onclick="generatePassword()">
                                <i class="bi bi-key me-1"></i>
                                Generate Password
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="sendWelcomeEmail()">
                                <i class="bi bi-envelope me-1"></i>
                                Send Welcome Email
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Role Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Role Permissions
                </h6>
            </div>
            <div class="card-body">
                <div id="rolePermissions">
                    <p class="text-muted">Select a role to view permissions</p>
                </div>
            </div>
        </div>
        
        <!-- Password Guidelines -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-shield-check me-2"></i>
                    Password Requirements
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check text-success me-2"></i>
                        At least 8 characters long
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check text-success me-2"></i>
                        Contains uppercase letter (A-Z)
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check text-success me-2"></i>
                        Contains lowercase letter (a-z)
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check text-success me-2"></i>
                        Contains number (0-9)
                    </li>
                    <li class="mb-0">
                        <i class="bi bi-check text-success me-2"></i>
                        Contains special character (!@#$%^&*)
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Recent Users -->
        <?php if (!empty($recent_users) && !isset($user)): ?>
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    Recent Users
                </h6>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <?php foreach (array_slice($recent_users, 0, 5) as $recent): ?>
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1"><?= esc($recent['username']) ?></h6>
                                <small><?= date('M j', strtotime($recent['created_at'])) ?></small>
                            </div>
                            <p class="mb-1 small text-muted"><?= esc($recent['email']) ?></p>
                            <small class="text-muted">
                                <span class="badge bg-<?= $recent['role'] === 'admin' ? 'danger' : ($recent['role'] === 'manager' ? 'warning' : 'info') ?> me-1">
                                    <?= ucfirst($recent['role']) ?>
                                </span>
                            </small>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Role permissions data
    const rolePermissions = {
        admin: {
            title: 'Administrator',
            icon: 'shield-check',
            color: 'danger',
            permissions: [
                'Full system access',
                'User management',
                'Form management',
                'Data management',
                'System configuration',
                'Audit log access'
            ]
        },
        manager: {
            title: 'Manager',
            icon: 'person-gear', 
            color: 'warning',
            permissions: [
                'Form management',
                'Data export/import',
                'User activity monitoring',
                'Basic user management',
                'Report generation'
            ]
        },
        enumerator: {
            title: 'Enumerator',
            icon: 'person-workspace',
            color: 'info',
            permissions: [
                'Data collection',
                'Form submission',
                'Profile management',
                'View assigned forms only'
            ]
        }
    };

    document.addEventListener('DOMContentLoaded', function() {
        initializeUserForm();
        initializePasswordValidation();
        initializeRoleSelection();
    });

    function initializeUserForm() {
        const form = document.getElementById('userForm');
        form.addEventListener('submit', handleFormSubmit);
        
        // Password toggle
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = document.getElementById('passwordIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });
    }

    function initializePasswordValidation() {
        const passwordInput = document.getElementById('password');
        const confirmInput = document.getElementById('confirmPassword');
        const strengthDiv = document.getElementById('passwordStrength');
        const strengthBar = document.getElementById('strengthBar');
        const strengthText = document.getElementById('strengthText');

        passwordInput.addEventListener('input', function() {
            const password = this.value;
            
            if (password.length > 0) {
                strengthDiv.style.display = 'block';
                const strength = calculatePasswordStrength(password);
                updateStrengthIndicator(strength, strengthBar, strengthText);
            } else {
                strengthDiv.style.display = 'none';
            }
            
            validatePasswordMatch();
        });

        confirmInput.addEventListener('input', validatePasswordMatch);
    }

    function calculatePasswordStrength(password) {
        let score = 0;
        const checks = {
            length: password.length >= 8,
            lowercase: /[a-z]/.test(password),
            uppercase: /[A-Z]/.test(password),
            numbers: /\d/.test(password),
            symbols: /[^A-Za-z0-9]/.test(password)
        };

        Object.values(checks).forEach(check => {
            if (check) score++;
        });

        return {
            score: score,
            checks: checks,
            percentage: (score / 5) * 100
        };
    }

    function updateStrengthIndicator(strength, bar, text) {
        const colors = ['bg-danger', 'bg-warning', 'bg-info', 'bg-success', 'bg-success'];
        const labels = ['Very Weak', 'Weak', 'Fair', 'Strong', 'Very Strong'];
        
        bar.className = `progress-bar ${colors[strength.score - 1] || 'bg-danger'}`;
        bar.style.width = strength.percentage + '%';
        text.textContent = labels[strength.score - 1] || 'Very Weak';
    }

    function validatePasswordMatch() {
        const password = document.getElementById('password').value;
        const confirm = document.getElementById('confirmPassword').value;
        const confirmInput = document.getElementById('confirmPassword');
        
        if (confirm.length > 0) {
            if (password !== confirm) {
                confirmInput.classList.add('is-invalid');
                document.getElementById('confirmPassword_error').textContent = 'Passwords do not match';
            } else {
                confirmInput.classList.remove('is-invalid');
                document.getElementById('confirmPassword_error').textContent = '';
            }
        }
    }

    function initializeRoleSelection() {
        const roleSelect = document.getElementById('role');
        roleSelect.addEventListener('change', function() {
            updateRolePermissions(this.value);
        });
        
        // Update permissions for current role
        if (roleSelect.value) {
            updateRolePermissions(roleSelect.value);
        }
    }

    function updateRolePermissions(role) {
        const container = document.getElementById('rolePermissions');
        
        if (!role || !rolePermissions[role]) {
            container.innerHTML = '<p class="text-muted">Select a role to view permissions</p>';
            return;
        }

        const roleData = rolePermissions[role];
        let html = `
            <div class="text-center mb-3">
                <i class="bi bi-${roleData.icon} text-${roleData.color}" style="font-size: 2rem;"></i>
                <h6 class="mt-2 text-${roleData.color}">${roleData.title}</h6>
            </div>
            <ul class="list-unstyled mb-0">
        `;

        roleData.permissions.forEach(permission => {
            html += `
                <li class="mb-2">
                    <i class="bi bi-check text-success me-2"></i>
                    ${permission}
                </li>
            `;
        });

        html += '</ul>';
        container.innerHTML = html;
    }

    function handleFormSubmit(e) {
        e.preventDefault();
        
        // Validate passwords match
        const password = document.getElementById('password').value;
        const confirm = document.getElementById('confirmPassword').value;
        
        if (password && password !== confirm) {
            showToast('Passwords do not match', 'danger');
            return;
        }
        
        const submitBtn = document.getElementById('submitBtn');
        const originalText = showLoading(submitBtn);
        
        const formData = new FormData(e.target);
        const url = <?= isset($user) ? "'".base_url('admin/kobo/users/'.$user['id'])."'" : "'".base_url('admin/kobo/users')."'" ?>;
        const method = <?= isset($user) ? "'PUT'" : "'POST'" ?>;
        
        ajaxRequest(url, method, Object.fromEntries(formData),
            function(data) {
                hideLoading(submitBtn, originalText);
                
                if (data.success) {
                    showToast(data.message || 'User saved successfully', 'success');
                    setTimeout(() => {
                        window.location.href = '<?= base_url('admin/kobo/users') ?>';
                    }, 1000);
                } else {
                    showFormErrors(data.errors || {});
                    showToast(data.message || 'Form submission failed', 'danger');
                }
            },
            function(error) {
                hideLoading(submitBtn, originalText);
                showToast('An error occurred while saving the user', 'danger');
            }
        );
    }

    function showFormErrors(errors) {
        // Clear previous errors
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
        
        // Show new errors
        Object.keys(errors).forEach(field => {
            const input = document.getElementById(field) || document.querySelector(`[name="${field}"]`);
            const errorDiv = document.getElementById(field + '_error');
            
            if (input) {
                input.classList.add('is-invalid');
            }
            
            if (errorDiv) {
                errorDiv.textContent = Array.isArray(errors[field]) ? errors[field][0] : errors[field];
            }
        });
    }

    function resetForm() {
        if (confirm('Are you sure you want to reset the form? All changes will be lost.')) {
            document.getElementById('userForm').reset();
            document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
            document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
            document.getElementById('passwordStrength').style.display = 'none';
            updateRolePermissions('');
        }
    }

    function generatePassword() {
        const length = 12;
        const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
        let password = "";
        
        for (let i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        
        document.getElementById('password').value = password;
        document.getElementById('confirmPassword').value = password;
        
        // Trigger validation
        document.getElementById('password').dispatchEvent(new Event('input'));
        
        showToast('Password generated successfully', 'success');
    }

    function sendWelcomeEmail() {
        <?php if (isset($user)): ?>
        ajaxRequest('<?= base_url('admin/kobo/users/'.$user['id'].'/welcome-email') ?>', 'POST', null,
            function(data) {
                if (data.success) {
                    showToast('Welcome email sent successfully', 'success');
                } else {
                    showToast('Failed to send welcome email: ' + (data.message || 'Unknown error'), 'danger');
                }
            }
        );
        <?php endif; ?>
    }
</script>
<?= $this->endSection() ?>