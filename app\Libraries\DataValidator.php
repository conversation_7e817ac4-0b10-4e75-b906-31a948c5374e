<?php

namespace App\Libraries;

/**
 * Data Validator Library
 * 
 * Implements form data validation, submission validation rules,
 * and custom validation methods for Kobo data collection
 */
class DataValidator
{
    protected $errors = [];
    protected $warnings = [];
    protected $validationRules = [];
    protected $customValidators = [];

    /**
     * Validate submission data against form definition
     */
    public function validateSubmission(array $submissionData, array $formDefinition): bool
    {
        $this->errors = [];
        $this->warnings = [];
        
        $isValid = true;

        // Validate required fields
        if (!$this->validateRequiredFields($submissionData, $formDefinition)) {
            $isValid = false;
        }

        // Validate data types
        if (!$this->validateDataTypes($submissionData, $formDefinition)) {
            $isValid = false;
        }

        // Validate constraints
        if (!$this->validateConstraints($submissionData, $formDefinition)) {
            $isValid = false;
        }

        // Validate custom business rules
        if (!$this->validateCustomRules($submissionData, $formDefinition)) {
            $isValid = false;
        }

        return $isValid;
    }

    /**
     * Validate required fields
     */
    private function validateRequiredFields(array $data, array $definition): bool
    {
        $isValid = true;

        foreach ($definition['fields'] ?? [] as $field) {
            if ($field['required'] ?? false) {
                $fieldName = $field['name'];
                
                if (!isset($data[$fieldName]) || $this->isEmpty($data[$fieldName])) {
                    $this->errors[] = "Required field '{$fieldName}' is missing or empty";
                    $isValid = false;
                }
            }
        }

        return $isValid;
    }

    /**
     * Validate data types
     */
    private function validateDataTypes(array $data, array $definition): bool
    {
        $isValid = true;

        foreach ($definition['fields'] ?? [] as $field) {
            $fieldName = $field['name'];
            $dataType = $field['data_type'] ?? 'string';
            
            if (!isset($data[$fieldName]) || $this->isEmpty($data[$fieldName])) {
                continue; // Skip empty values (handled by required validation)
            }

            $value = $data[$fieldName];
            
            if (!$this->validateDataType($value, $dataType)) {
                $this->errors[] = "Field '{$fieldName}' has invalid data type. Expected: {$dataType}";
                $isValid = false;
            }
        }

        return $isValid;
    }

    /**
     * Validate individual data type
     */
    private function validateDataType($value, string $type): bool
    {
        switch ($type) {
            case 'string':
                return is_string($value);
                
            case 'int':
            case 'integer':
                return filter_var($value, FILTER_VALIDATE_INT) !== false;
                
            case 'decimal':
            case 'float':
            case 'double':
                return filter_var($value, FILTER_VALIDATE_FLOAT) !== false;
                
            case 'boolean':
                return in_array(strtolower($value), ['true', 'false', '1', '0', 'yes', 'no']);
                
            case 'date':
                return $this->validateDate($value);
                
            case 'datetime':
                return $this->validateDateTime($value);
                
            case 'time':
                return $this->validateTime($value);
                
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
                
            case 'url':
                return filter_var($value, FILTER_VALIDATE_URL) !== false;
                
            case 'geopoint':
                return $this->validateGeopoint($value);
                
            case 'binary':
            case 'image':
            case 'audio':
            case 'video':
                return $this->validateBinaryData($value, $type);
                
            default:
                return true; // Unknown types pass validation
        }
    }

    /**
     * Validate constraints (min, max, length, etc.)
     */
    private function validateConstraints(array $data, array $definition): bool
    {
        $isValid = true;

        foreach ($definition['fields'] ?? [] as $field) {
            $fieldName = $field['name'];
            
            if (!isset($data[$fieldName]) || $this->isEmpty($data[$fieldName])) {
                continue;
            }

            $value = $data[$fieldName];
            $constraints = $field['constraints'] ?? [];

            // Validate length constraints
            if (isset($constraints['min_length']) || isset($constraints['max_length'])) {
                if (!$this->validateLength($value, $constraints)) {
                    $this->errors[] = "Field '{$fieldName}' length constraint violation";
                    $isValid = false;
                }
            }

            // Validate value constraints
            if (isset($constraints['min_value']) || isset($constraints['max_value'])) {
                if (!$this->validateValueRange($value, $constraints)) {
                    $this->errors[] = "Field '{$fieldName}' value constraint violation";
                    $isValid = false;
                }
            }

            // Validate pattern constraints
            if (isset($constraints['pattern'])) {
                if (!$this->validatePattern($value, $constraints['pattern'])) {
                    $this->errors[] = "Field '{$fieldName}' does not match required pattern";
                    $isValid = false;
                }
            }

            // Validate choice constraints
            if (isset($constraints['choices'])) {
                if (!$this->validateChoice($value, $constraints['choices'])) {
                    $this->errors[] = "Field '{$fieldName}' value not in allowed choices";
                    $isValid = false;
                }
            }
        }

        return $isValid;
    }

    /**
     * Validate custom business rules
     */
    private function validateCustomRules(array $data, array $definition): bool
    {
        $isValid = true;

        // Apply custom validators
        foreach ($this->customValidators as $validator) {
            if (!call_user_func($validator, $data, $definition)) {
                $isValid = false;
            }
        }

        // Apply form-specific rules
        $customRules = $definition['validation_rules'] ?? [];
        foreach ($customRules as $rule) {
            if (!$this->applyCustomRule($data, $rule)) {
                $isValid = false;
            }
        }

        return $isValid;
    }

    /**
     * Validate date format
     */
    private function validateDate(string $date): bool
    {
        $formats = ['Y-m-d', 'm/d/Y', 'd/m/Y', 'Y/m/d'];
        
        foreach ($formats as $format) {
            $d = \DateTime::createFromFormat($format, $date);
            if ($d && $d->format($format) === $date) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Validate datetime format
     */
    private function validateDateTime(string $datetime): bool
    {
        $formats = [
            'Y-m-d H:i:s',
            'Y-m-d\TH:i:s',
            'Y-m-d\TH:i:s\Z',
            'Y-m-d\TH:i:sP'
        ];
        
        foreach ($formats as $format) {
            $d = \DateTime::createFromFormat($format, $datetime);
            if ($d && $d->format($format) === $datetime) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Validate time format
     */
    private function validateTime(string $time): bool
    {
        $formats = ['H:i:s', 'H:i'];
        
        foreach ($formats as $format) {
            $t = \DateTime::createFromFormat($format, $time);
            if ($t && $t->format($format) === $time) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Validate geopoint format (latitude longitude altitude accuracy)
     */
    private function validateGeopoint(string $geopoint): bool
    {
        $parts = explode(' ', trim($geopoint));
        
        if (count($parts) < 2 || count($parts) > 4) {
            return false;
        }

        // Validate latitude (-90 to 90)
        $lat = (float)$parts[0];
        if ($lat < -90 || $lat > 90) {
            return false;
        }

        // Validate longitude (-180 to 180)
        $lng = (float)$parts[1];
        if ($lng < -180 || $lng > 180) {
            return false;
        }

        // Validate altitude (optional)
        if (isset($parts[2]) && !is_numeric($parts[2])) {
            return false;
        }

        // Validate accuracy (optional)
        if (isset($parts[3]) && !is_numeric($parts[3])) {
            return false;
        }

        return true;
    }

    /**
     * Validate binary/media data
     */
    private function validateBinaryData($value, string $type): bool
    {
        // For file references, validate filename format
        if (is_string($value)) {
            // Should be a filename
            return !empty($value) && preg_match('/^[a-zA-Z0-9_.-]+\.[a-zA-Z0-9]+$/', $value);
        }

        // For base64 data, validate format
        if (str_starts_with($value, 'data:')) {
            return $this->validateBase64Data($value, $type);
        }

        return false;
    }

    /**
     * Validate base64 encoded data
     */
    private function validateBase64Data(string $data, string $type): bool
    {
        // Parse data URI
        if (!preg_match('/^data:([^;]+);base64,(.+)$/', $data, $matches)) {
            return false;
        }

        $mimeType = $matches[1];
        $base64Data = $matches[2];

        // Validate base64 encoding
        if (!base64_decode($base64Data, true)) {
            return false;
        }

        // Validate MIME type for specific media types
        $allowedMimes = [
            'image' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'audio' => ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp4'],
            'video' => ['video/mp4', 'video/quicktime', 'video/webm'],
            'binary' => [] // Any MIME type allowed
        ];

        if (isset($allowedMimes[$type]) && !empty($allowedMimes[$type])) {
            return in_array($mimeType, $allowedMimes[$type]);
        }

        return true;
    }

    /**
     * Validate string length constraints
     */
    private function validateLength(string $value, array $constraints): bool
    {
        $length = strlen($value);
        
        if (isset($constraints['min_length']) && $length < $constraints['min_length']) {
            return false;
        }
        
        if (isset($constraints['max_length']) && $length > $constraints['max_length']) {
            return false;
        }
        
        return true;
    }

    /**
     * Validate numeric value range
     */
    private function validateValueRange($value, array $constraints): bool
    {
        $numValue = (float)$value;
        
        if (isset($constraints['min_value']) && $numValue < $constraints['min_value']) {
            return false;
        }
        
        if (isset($constraints['max_value']) && $numValue > $constraints['max_value']) {
            return false;
        }
        
        return true;
    }

    /**
     * Validate pattern match
     */
    private function validatePattern(string $value, string $pattern): bool
    {
        return preg_match($pattern, $value) === 1;
    }

    /**
     * Validate choice selection
     */
    private function validateChoice($value, array $choices): bool
    {
        return in_array($value, $choices);
    }

    /**
     * Apply custom validation rule
     */
    private function applyCustomRule(array $data, array $rule): bool
    {
        $type = $rule['type'] ?? '';
        
        switch ($type) {
            case 'conditional_required':
                return $this->validateConditionalRequired($data, $rule);
                
            case 'cross_field_validation':
                return $this->validateCrossField($data, $rule);
                
            case 'calculation_validation':
                return $this->validateCalculation($data, $rule);
                
            default:
                return true;
        }
    }

    /**
     * Validate conditional required fields
     */
    private function validateConditionalRequired(array $data, array $rule): bool
    {
        $condition = $rule['condition'] ?? [];
        $requiredField = $rule['required_field'] ?? '';
        
        if (empty($condition) || empty($requiredField)) {
            return true;
        }

        // Check if condition is met
        $conditionMet = $this->evaluateCondition($data, $condition);
        
        if ($conditionMet && (!isset($data[$requiredField]) || $this->isEmpty($data[$requiredField]))) {
            $this->errors[] = "Field '{$requiredField}' is required when condition is met";
            return false;
        }

        return true;
    }

    /**
     * Validate cross-field relationships
     */
    private function validateCrossField(array $data, array $rule): bool
    {
        $field1 = $rule['field1'] ?? '';
        $field2 = $rule['field2'] ?? '';
        $relationship = $rule['relationship'] ?? '';
        
        if (!isset($data[$field1]) || !isset($data[$field2])) {
            return true; // Skip if fields don't exist
        }

        $value1 = $data[$field1];
        $value2 = $data[$field2];

        switch ($relationship) {
            case 'greater_than':
                if ((float)$value1 <= (float)$value2) {
                    $this->errors[] = "Field '{$field1}' must be greater than '{$field2}'";
                    return false;
                }
                break;
                
            case 'less_than':
                if ((float)$value1 >= (float)$value2) {
                    $this->errors[] = "Field '{$field1}' must be less than '{$field2}'";
                    return false;
                }
                break;
                
            case 'equal':
                if ($value1 !== $value2) {
                    $this->errors[] = "Field '{$field1}' must equal '{$field2}'";
                    return false;
                }
                break;
        }

        return true;
    }

    /**
     * Validate calculated values
     */
    private function validateCalculation(array $data, array $rule): bool
    {
        $field = $rule['field'] ?? '';
        $expression = $rule['expression'] ?? '';
        
        if (empty($field) || empty($expression) || !isset($data[$field])) {
            return true;
        }

        // Simple calculation validation (extend as needed)
        $calculatedValue = $this->evaluateExpression($data, $expression);
        $actualValue = (float)$data[$field];
        
        $tolerance = $rule['tolerance'] ?? 0.01;
        
        if (abs($actualValue - $calculatedValue) > $tolerance) {
            $this->errors[] = "Calculated value for '{$field}' is incorrect";
            return false;
        }

        return true;
    }

    /**
     * Evaluate simple condition
     */
    private function evaluateCondition(array $data, array $condition): bool
    {
        $field = $condition['field'] ?? '';
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? '';
        
        if (!isset($data[$field])) {
            return false;
        }

        $fieldValue = $data[$field];
        
        switch ($operator) {
            case '=':
            case '==':
                return $fieldValue == $value;
            case '!=':
                return $fieldValue != $value;
            case '>':
                return (float)$fieldValue > (float)$value;
            case '<':
                return (float)$fieldValue < (float)$value;
            case '>=':
                return (float)$fieldValue >= (float)$value;
            case '<=':
                return (float)$fieldValue <= (float)$value;
            default:
                return false;
        }
    }

    /**
     * Evaluate simple mathematical expression
     */
    private function evaluateExpression(array $data, string $expression): float
    {
        // Simple expression evaluator (for basic arithmetic)
        // Replace field references with values
        $expr = $expression;
        foreach ($data as $field => $value) {
            $expr = str_replace('{' . $field . '}', (string)$value, $expr);
        }

        // Basic safety check
        if (preg_match('/[^0-9+\-*\/\(\)\s\.]/', $expr)) {
            return 0; // Invalid expression
        }

        try {
            return eval("return $expr;");
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Check if value is empty
     */
    private function isEmpty($value): bool
    {
        if (is_null($value)) {
            return true;
        }
        
        if (is_string($value)) {
            return trim($value) === '';
        }
        
        if (is_array($value)) {
            return empty($value);
        }
        
        return false;
    }

    /**
     * Add custom validator
     */
    public function addCustomValidator(callable $validator): void
    {
        $this->customValidators[] = $validator;
    }

    /**
     * Sanitize input data
     */
    public function sanitizeData(array $data): array
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // Remove null bytes and trim whitespace
                $value = str_replace("\0", '', $value);
                $value = trim($value);
                
                // Sanitize HTML if needed
                $sanitized[$key] = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }

    /**
     * Get validation errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get validation warnings
     */
    public function getWarnings(): array
    {
        return $this->warnings;
    }

    /**
     * Check if validation passed
     */
    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    /**
     * Clear errors and warnings
     */
    public function reset(): void
    {
        $this->errors = [];
        $this->warnings = [];
    }
}