<?php

namespace App\Libraries;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;

/**
 * JWT Token Helper Library
 * 
 * Handles JWT token generation, validation, and management
 */
class JWTHelper
{
    protected $secret;
    protected $algorithm = 'HS256';
    protected $issuer;
    protected $audience;
    protected $tokenLifetime = 3600; // 1 hour
    protected $refreshLifetime = 604800; // 7 days

    public function __construct()
    {
        $this->secret = env('JWT_SECRET', 'kobo-collect-secret-key-2024');
        $this->issuer = base_url();
        $this->audience = base_url();
    }

    /**
     * Generate access token for user
     */
    public function generateAccessToken(array $user): string
    {
        $issuedAt = time();
        $expiresAt = $issuedAt + $this->tokenLifetime;

        $payload = [
            'iss' => $this->issuer,           // Issuer
            'aud' => $this->audience,         // Audience
            'iat' => $issuedAt,              // Issued at
            'exp' => $expiresAt,             // Expires at
            'sub' => $user['id'],            // Subject (user ID)
            'username' => $user['username'],  // Username
            'role' => $user['role'],         // User role
            'device_id' => $user['device_id'] ?? null, // Device ID if applicable
            'permissions' => $this->getUserPermissions($user['role']), // User permissions
            'token_type' => 'access'         // Token type
        ];

        return JWT::encode($payload, $this->secret, $this->algorithm);
    }

    /**
     * Generate refresh token for user
     */
    public function generateRefreshToken(array $user): string
    {
        $issuedAt = time();
        $expiresAt = $issuedAt + $this->refreshLifetime;

        $payload = [
            'iss' => $this->issuer,
            'aud' => $this->audience,
            'iat' => $issuedAt,
            'exp' => $expiresAt,
            'sub' => $user['id'],
            'token_type' => 'refresh',
            'jti' => $this->generateJTI($user['id'], $issuedAt) // JWT ID for revocation
        ];

        return JWT::encode($payload, $this->secret, $this->algorithm);
    }

    /**
     * Validate and decode JWT token
     */
    public function validateToken(string $token): ?\stdClass
    {
        try {
            $decoded = JWT::decode($token, new Key($this->secret, $this->algorithm));
            
            // Validate basic structure
            if (!isset($decoded->sub, $decoded->iat, $decoded->exp)) {
                throw new \Exception('Invalid token structure');
            }

            // Check expiration
            if ($decoded->exp < time()) {
                throw new ExpiredException('Token has expired');
            }

            // Validate issuer
            if (isset($decoded->iss) && $decoded->iss !== $this->issuer) {
                throw new \Exception('Invalid token issuer');
            }

            return $decoded;

        } catch (ExpiredException $e) {
            throw $e;
        } catch (SignatureInvalidException $e) {
            throw $e;
        } catch (\Exception $e) {
            log_message('error', 'JWT validation error: ' . $e->getMessage());
            throw new \Exception('Invalid token');
        }
    }

    /**
     * Extract token from Authorization header
     */
    public function extractTokenFromHeader(string $authHeader): ?string
    {
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return $matches[1];
        }
        return null;
    }

    /**
     * Check if token is expired
     */
    public function isTokenExpired(string $token): bool
    {
        try {
            $decoded = JWT::decode($token, new Key($this->secret, $this->algorithm));
            return $decoded->exp < time();
        } catch (\Exception $e) {
            return true; // Consider invalid tokens as expired
        }
    }

    /**
     * Get token expiry time
     */
    public function getTokenExpiry(string $token): ?int
    {
        try {
            $decoded = JWT::decode($token, new Key($this->secret, $this->algorithm));
            return $decoded->exp ?? null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Refresh access token using refresh token
     */
    public function refreshAccessToken(string $refreshToken, array $user): array
    {
        try {
            $decoded = $this->validateToken($refreshToken);
            
            // Verify it's a refresh token
            if (!isset($decoded->token_type) || $decoded->token_type !== 'refresh') {
                throw new \Exception('Invalid refresh token type');
            }

            // Verify user matches
            if ($decoded->sub != $user['id']) {
                throw new \Exception('Token user mismatch');
            }

            // Generate new access token
            $accessToken = $this->generateAccessToken($user);
            
            // Optionally generate new refresh token
            $newRefreshToken = $this->generateRefreshToken($user);

            return [
                'access_token' => $accessToken,
                'refresh_token' => $newRefreshToken,
                'token_type' => 'Bearer',
                'expires_in' => $this->tokenLifetime
            ];

        } catch (\Exception $e) {
            log_message('error', 'Token refresh failed: ' . $e->getMessage());
            throw new \Exception('Failed to refresh token');
        }
    }

    /**
     * Generate JWT ID for tracking
     */
    protected function generateJTI(int $userId, int $issuedAt): string
    {
        return hash('sha256', $userId . $issuedAt . $this->secret);
    }

    /**
     * Get user permissions based on role
     */
    protected function getUserPermissions(string $role): array
    {
        $permissions = [
            'admin' => [
                'forms.create',
                'forms.read',
                'forms.update',
                'forms.delete',
                'submissions.create',
                'submissions.read',
                'submissions.update',
                'submissions.delete',
                'users.create',
                'users.read',
                'users.update',
                'users.delete',
                'system.manage'
            ],
            'supervisor' => [
                'forms.create',
                'forms.read',
                'forms.update',
                'submissions.create',
                'submissions.read',
                'submissions.update',
                'users.read'
            ],
            'enumerator' => [
                'forms.read',
                'submissions.create',
                'submissions.read'
            ],
            'viewer' => [
                'forms.read',
                'submissions.read'
            ]
        ];

        return $permissions[$role] ?? [];
    }

    /**
     * Check if user has specific permission
     */
    public function hasPermission(array $tokenPayload, string $permission): bool
    {
        if (!isset($tokenPayload['permissions'])) {
            return false;
        }

        return in_array($permission, $tokenPayload['permissions']);
    }

    /**
     * Create token response array
     */
    public function createTokenResponse(array $user): array
    {
        $accessToken = $this->generateAccessToken($user);
        $refreshToken = $this->generateRefreshToken($user);

        return [
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken,
            'token_type' => 'Bearer',
            'expires_in' => $this->tokenLifetime,
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'role' => $user['role'],
                'status' => $user['status']
            ]
        ];
    }

    /**
     * Blacklist token (store in cache/database for revocation)
     */
    public function blacklistToken(string $token): bool
    {
        try {
            $decoded = $this->validateToken($token);
            $jti = $decoded->jti ?? hash('sha256', $token);
            
            // Store in cache with expiry time
            cache()->save('blacklist_' . $jti, true, $decoded->exp - time());
            
            return true;
        } catch (\Exception $e) {
            log_message('error', 'Failed to blacklist token: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if token is blacklisted
     */
    public function isTokenBlacklisted(string $token): bool
    {
        try {
            $decoded = JWT::decode($token, new Key($this->secret, $this->algorithm));
            $jti = $decoded->jti ?? hash('sha256', $token);
            
            return cache()->get('blacklist_' . $jti) !== null;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Set custom token lifetime
     */
    public function setTokenLifetime(int $seconds): self
    {
        $this->tokenLifetime = $seconds;
        return $this;
    }

    /**
     * Set custom refresh token lifetime
     */
    public function setRefreshLifetime(int $seconds): self
    {
        $this->refreshLifetime = $seconds;
        return $this;
    }

    /**
     * Get remaining time for token
     */
    public function getTokenRemainingTime(string $token): ?int
    {
        try {
            $decoded = JWT::decode($token, new Key($this->secret, $this->algorithm));
            $remaining = $decoded->exp - time();
            return $remaining > 0 ? $remaining : 0;
        } catch (\Exception $e) {
            return null;
        }
    }
}