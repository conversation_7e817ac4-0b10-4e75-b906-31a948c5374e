<?php

namespace App\Libraries;

/**
 * XForm Parser Library
 * 
 * Handles XML parsing functionality for XForm documents
 * Provides XForm validation, XML to JSON conversion, and schema validation
 */
class XFormParser
{
    protected $xml;
    protected $errors = [];
    
    /**
     * Parse XForm XML string
     */
    public function parse(string $xmlString): bool
    {
        $this->errors = [];
        
        // Debug: Log the XML content length and first few characters
        log_message('debug', 'XFormParser: Parsing XML of length ' . strlen($xmlString));
        log_message('debug', 'XFormParser: XML starts with: ' . substr($xmlString, 0, 100));
        
        // Enable libxml internal error handling
        libxml_use_internal_errors(true);
        
        try {
            $this->xml = simplexml_load_string($xmlString);
            
            if ($this->xml === false) {
                log_message('debug', 'XFormParser: simplexml_load_string returned false');
                $this->collectLibXMLErrors();
                log_message('debug', 'XFormParser: Collected errors: ' . json_encode($this->errors));
                return false;
            }
            
            log_message('debug', 'XFormParser: XML parsed successfully, root element: ' . $this->xml->getName());
            return true;
            
        } catch (\Exception $e) {
            log_message('error', 'XFormParser: Exception during parsing: ' . $e->getMessage());
            $this->errors[] = 'XML parsing failed: ' . $e->getMessage();
            return false;
        } finally {
            libxml_clear_errors();
        }
    }

    /**
     * Validate XForm structure
     */
    public function validateXForm(): bool
    {
        if (!$this->xml) {
            $this->errors[] = 'No XML loaded. Call parse() first.';
            return false;
        }

        $isValid = true;

        // Check root element (handle both namespaced and non-namespaced)
        $rootName = $this->xml->getName();
        if ($rootName !== 'html' && $rootName !== 'h:html') {
            $this->errors[] = 'Root element must be <html> or <h:html>';
            $isValid = false;
        }

        // Check namespace (more flexible)
        $namespaces = $this->xml->getNamespaces(true);
        $hasXHTMLNamespace = false;
        
        // Check for XHTML namespace in any form
        foreach ($namespaces as $prefix => $uri) {
            if ($uri === 'http://www.w3.org/1999/xhtml') {
                $hasXHTMLNamespace = true;
                break;
            }
        }
        
        if (!$hasXHTMLNamespace) {
            $this->errors[] = 'Missing XHTML namespace';
            $isValid = false;
        }

        // Check for head element (handle namespaced)
        $head = null;
        if (isset($this->xml->head)) {
            $head = $this->xml->head;
        } elseif (isset($this->xml->children('http://www.w3.org/1999/xhtml')->head)) {
            $head = $this->xml->children('http://www.w3.org/1999/xhtml')->head;
        }
        
        if (!$head) {
            $this->errors[] = 'Missing <head> element';
            $isValid = false;
        } else {
            // Check for model in head
            if (!isset($head->model)) {
                $this->errors[] = 'Missing <model> element in <head>';
                $isValid = false;
            }
        }

        // Check for body element (handle namespaced)
        $body = null;
        if (isset($this->xml->body)) {
            $body = $this->xml->body;
        } elseif (isset($this->xml->children('http://www.w3.org/1999/xhtml')->body)) {
            $body = $this->xml->children('http://www.w3.org/1999/xhtml')->body;
        }
        
        if (!$body) {
            $this->errors[] = 'Missing <body> element';
            $isValid = false;
        }

        return $isValid;
    }

    /**
     * Extract form metadata
     */
    public function extractMetadata(): array
    {
        if (!$this->xml) {
            return [];
        }

        $metadata = [
            'form_id' => null,
            'title' => null,
            'version' => null,
            'description' => null,
            'author' => null,
            'namespace' => null,
            'instance_name' => null
        ];

        try {
            // Get head element (handle namespaced)
            $head = null;
            if (isset($this->xml->head)) {
                $head = $this->xml->head;
            } elseif (isset($this->xml->children('http://www.w3.org/1999/xhtml')->head)) {
                $head = $this->xml->children('http://www.w3.org/1999/xhtml')->head;
            }
            
            // Extract title
            if ($head && isset($head->title)) {
                $metadata['title'] = (string)$head->title;
            }

            // Extract form ID and version from root attributes
            $attributes = $this->xml->attributes();
            if (isset($attributes['version'])) {
                $metadata['version'] = (string)$attributes['version'];
            }

            // Extract from model/instance
            if ($head && isset($head->model->instance)) {
                $instance = $head->model->instance;
                
                // Get first child element (the data structure)
                $children = $instance->children();
                if ($children->count() > 0) {
                    $firstChild = $children[0];
                    $metadata['instance_name'] = $firstChild->getName();
                    $metadata['form_id'] = $firstChild->getName();
                    
                    // Check for id attribute
                    $childAttributes = $firstChild->attributes();
                    if (isset($childAttributes['id'])) {
                        $metadata['form_id'] = (string)$childAttributes['id'];
                    }
                    
                    // Check for version in instance
                    if (isset($childAttributes['version'])) {
                        $metadata['version'] = (string)$childAttributes['version'];
                    }
                }
            }

            // Extract description from meta or model
            if ($head && isset($head->model->meta->description)) {
                $metadata['description'] = (string)$head->model->meta->description;
            }

            // Extract namespace
            $namespaces = $this->xml->getNamespaces(true);
            if (isset($namespaces[''])) {
                $metadata['namespace'] = $namespaces[''];
            }

        } catch (\Exception $e) {
            $this->errors[] = 'Metadata extraction failed: ' . $e->getMessage();
        }

        return $metadata;
    }

    /**
     * Extract form fields/controls
     */
    public function extractFields(): array
    {
        if (!$this->xml) {
            return [];
        }

        $fields = [];

        try {
            // Get body element (handle namespaced)
            $body = null;
            if (isset($this->xml->body)) {
                $body = $this->xml->body;
            } elseif (isset($this->xml->children('http://www.w3.org/1999/xhtml')->body)) {
                $body = $this->xml->children('http://www.w3.org/1999/xhtml')->body;
            }
            
            if ($body) {
                // Parse body for input controls
                $this->parseBodyElements($body, $fields);
            }
            
        } catch (\Exception $e) {
            $this->errors[] = 'Field extraction failed: ' . $e->getMessage();
        }

        return $fields;
    }

    /**
     * Recursively parse body elements for form controls
     */
    private function parseBodyElements(\SimpleXMLElement $element, array &$fields, string $parentPath = ''): void
    {
        foreach ($element->children() as $child) {
            $nodeName = $child->getName();
            $attributes = $child->attributes();
            
            switch ($nodeName) {
                case 'input':
                    $this->parseInputElement($child, $fields, $parentPath);
                    break;
                    
                case 'select1':
                case 'select':
                    $this->parseSelectElement($child, $fields, $parentPath);
                    break;
                    
                case 'upload':
                    $this->parseUploadElement($child, $fields, $parentPath);
                    break;
                    
                case 'group':
                case 'repeat':
                    $groupPath = $parentPath ? $parentPath . '/' . (string)$attributes['ref'] : (string)$attributes['ref'];
                    $this->parseBodyElements($child, $fields, $groupPath);
                    break;
                    
                default:
                    // Continue parsing child elements
                    $this->parseBodyElements($child, $fields, $parentPath);
                    break;
            }
        }
    }

    /**
     * Parse input element
     */
    private function parseInputElement(\SimpleXMLElement $input, array &$fields, string $parentPath): void
    {
        $attributes = $input->attributes();
        $ref = (string)$attributes['ref'];
        
        $field = [
            'name' => $this->extractFieldName($ref),
            'ref' => $ref,
            'type' => 'input',
            'data_type' => $this->getInputDataType($input),
            'label' => $this->getLabel($input),
            'hint' => $this->getHint($input),
            'required' => $this->isRequired($ref),
            'readonly' => isset($attributes['readonly']) ? (string)$attributes['readonly'] === 'true()' : false,
            'parent_path' => $parentPath
        ];

        $fields[] = $field;
    }

    /**
     * Parse select element (single or multiple choice)
     */
    private function parseSelectElement(\SimpleXMLElement $select, array &$fields, string $parentPath): void
    {
        $attributes = $select->attributes();
        $ref = (string)$attributes['ref'];
        
        $options = [];
        foreach ($select->item as $item) {
            $options[] = [
                'value' => (string)$item->value,
                'label' => (string)$item->label
            ];
        }

        $field = [
            'name' => $this->extractFieldName($ref),
            'ref' => $ref,
            'type' => $select->getName(), // select1 or select
            'label' => $this->getLabel($select),
            'hint' => $this->getHint($select),
            'required' => $this->isRequired($ref),
            'options' => $options,
            'parent_path' => $parentPath
        ];

        $fields[] = $field;
    }

    /**
     * Parse upload element
     */
    private function parseUploadElement(\SimpleXMLElement $upload, array &$fields, string $parentPath): void
    {
        $attributes = $upload->attributes();
        $ref = (string)$attributes['ref'];
        
        $field = [
            'name' => $this->extractFieldName($ref),
            'ref' => $ref,
            'type' => 'upload',
            'media_type' => (string)$attributes['mediatype'] ?? 'image/*',
            'label' => $this->getLabel($upload),
            'hint' => $this->getHint($upload),
            'required' => $this->isRequired($ref),
            'parent_path' => $parentPath
        ];

        $fields[] = $field;
    }

    /**
     * Get input data type from bind elements
     */
    private function getInputDataType(\SimpleXMLElement $input): string
    {
        $attributes = $input->attributes();
        $ref = (string)$attributes['ref'];
        
        // Look for bind element with matching nodeset
        if (isset($this->xml->head->model->bind)) {
            foreach ($this->xml->head->model->bind as $bind) {
                $bindAttributes = $bind->attributes();
                if ((string)$bindAttributes['nodeset'] === $ref) {
                    return (string)$bindAttributes['type'] ?? 'string';
                }
            }
        }
        
        return 'string';
    }

    /**
     * Get label for element
     */
    private function getLabel(\SimpleXMLElement $element): string
    {
        if (isset($element->label)) {
            return (string)$element->label;
        }
        return '';
    }

    /**
     * Get hint for element
     */
    private function getHint(\SimpleXMLElement $element): string
    {
        if (isset($element->hint)) {
            return (string)$element->hint;
        }
        return '';
    }

    /**
     * Check if field is required based on bind constraints
     */
    private function isRequired(string $ref): bool
    {
        if (isset($this->xml->head->model->bind)) {
            foreach ($this->xml->head->model->bind as $bind) {
                $bindAttributes = $bind->attributes();
                if ((string)$bindAttributes['nodeset'] === $ref) {
                    $required = (string)$bindAttributes['required'];
                    return $required === 'true()' || $required === 'true';
                }
            }
        }
        return false;
    }

    /**
     * Extract field name from reference path
     */
    private function extractFieldName(string $ref): string
    {
        // Remove leading slash and extract last segment
        $ref = ltrim($ref, '/');
        $parts = explode('/', $ref);
        return end($parts);
    }

    /**
     * Convert XML to JSON
     */
    public function xmlToJson(string $xmlString): string|false
    {
        try {
            $xml = simplexml_load_string($xmlString);
            if ($xml === false) {
                return false;
            }

            // Convert SimpleXML to array
            $array = $this->simpleXMLToArray($xml);
            
            return json_encode($array, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            
        } catch (\Exception $e) {
            $this->errors[] = 'XML to JSON conversion failed: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * Convert SimpleXMLElement to array
     */
    private function simpleXMLToArray(\SimpleXMLElement $xml): array
    {
        $array = [];
        
        // Handle attributes
        $attributes = $xml->attributes();
        if (count($attributes) > 0) {
            foreach ($attributes as $key => $value) {
                $array['@attributes'][$key] = (string)$value;
            }
        }
        
        // Handle children
        $children = $xml->children();
        if (count($children) > 0) {
            foreach ($children as $key => $child) {
                if (count($child->children()) > 0 || count($child->attributes()) > 0) {
                    $array[$key] = $this->simpleXMLToArray($child);
                } else {
                    $array[$key] = (string)$child;
                }
            }
        } else {
            // Leaf node with just text content
            return (string)$xml;
        }
        
        return $array;
    }

    /**
     * Validate XML against XForm schema
     */
    public function validateAgainstSchema(string $schemaPath = null): bool
    {
        if (!$this->xml) {
            $this->errors[] = 'No XML loaded for schema validation';
            return false;
        }

        // Basic XForm structure validation (if no schema provided)
        if (!$schemaPath) {
            return $this->validateXFormStructure();
        }

        // Validate against provided XSD schema
        try {
            $dom = new \DOMDocument();
            $dom->loadXML($this->xml->asXML());
            
            if (!file_exists($schemaPath)) {
                $this->errors[] = 'Schema file not found: ' . $schemaPath;
                return false;
            }

            libxml_use_internal_errors(true);
            $isValid = $dom->schemaValidate($schemaPath);
            
            if (!$isValid) {
                $this->collectLibXMLErrors();
            }
            
            return $isValid;
            
        } catch (\Exception $e) {
            $this->errors[] = 'Schema validation failed: ' . $e->getMessage();
            return false;
        } finally {
            libxml_clear_errors();
        }
    }

    /**
     * Basic XForm structure validation
     */
    private function validateXFormStructure(): bool
    {
        $isValid = true;
        
        // Required elements check
        $requiredPaths = [
            'head/title',
            'head/model',
            'head/model/instance',
            'body'
        ];
        
        foreach ($requiredPaths as $path) {
            if (!$this->elementExists($path)) {
                $this->errors[] = "Required element missing: {$path}";
                $isValid = false;
            }
        }
        
        // Check for proper bindings
        if (isset($this->xml->body) && isset($this->xml->head->model->bind)) {
            $this->validateBindings();
        }
        
        return $isValid;
    }

    /**
     * Check if element exists at given path
     */
    private function elementExists(string $path): bool
    {
        $parts = explode('/', $path);
        $current = $this->xml;
        
        foreach ($parts as $part) {
            if (!isset($current->$part)) {
                return false;
            }
            $current = $current->$part;
        }
        
        return true;
    }

    /**
     * Validate form bindings
     */
    private function validateBindings(): void
    {
        // Extract all refs from body
        $bodyRefs = [];
        $this->extractRefsFromBody($this->xml->body, $bodyRefs);
        
        // Extract all nodesets from bindings
        $bindNodesets = [];
        foreach ($this->xml->head->model->bind as $bind) {
            $attributes = $bind->attributes();
            if (isset($attributes['nodeset'])) {
                $bindNodesets[] = (string)$attributes['nodeset'];
            }
        }
        
        // Check for orphaned controls (controls without bindings)
        foreach ($bodyRefs as $ref) {
            if (!in_array($ref, $bindNodesets)) {
                $this->errors[] = "Control '{$ref}' has no corresponding binding";
            }
        }
    }

    /**
     * Extract all refs from body elements
     */
    private function extractRefsFromBody(\SimpleXMLElement $element, array &$refs): void
    {
        foreach ($element->children() as $child) {
            $attributes = $child->attributes();
            if (isset($attributes['ref'])) {
                $refs[] = (string)$attributes['ref'];
            }
            
            // Recurse for nested elements
            $this->extractRefsFromBody($child, $refs);
        }
    }

    /**
     * Collect libxml errors
     */
    private function collectLibXMLErrors(): void
    {
        $libxmlErrors = libxml_get_errors();
        foreach ($libxmlErrors as $error) {
            $this->errors[] = trim($error->message);
        }
    }

    /**
     * Get parsing errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Check if there are any errors
     */
    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    /**
     * Get the parsed XML object
     */
    public function getXML(): \SimpleXMLElement|false
    {
        return $this->xml;
    }

    /**
     * Clear errors and XML
     */
    public function reset(): void
    {
        $this->xml = null;
        $this->errors = [];
    }
}