<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class KoboTestDataSeeder extends Seeder
{
    public function run()
    {
        // Create test users
        $userData = [
            [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
                'role' => 'admin',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'username' => 'enumerator1',
                'email' => '<EMAIL>',
                'password_hash' => password_hash('enum123', PASSWORD_DEFAULT),
                'role' => 'enumerator',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($userData as $user) {
            $this->db->table('kobo_users')->insert($user);
        }

        // Create test forms
        $formData = [
            [
                'form_id' => 'test_survey_001',
                'form_name' => 'Basic Survey Test',
                'xml_content' => '<?xml version="1.0" encoding="UTF-8"?>
<html xmlns="http://www.w3.org/2002/xforms" xmlns:h="http://www.w3.org/1999/xhtml" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:jr="http://openrosa.org/javarosa">
  <h:head>
    <h:title>Basic Survey Test</h:title>
    <model>
      <instance>
        <test_survey_001 id="test_survey_001">
          <name/>
          <age/>
          <location/>
          <meta>
            <instanceID/>
          </meta>
        </test_survey_001>
      </instance>
      <bind nodeset="/test_survey_001/name" type="string" required="true()"/>
      <bind nodeset="/test_survey_001/age" type="int"/>
      <bind nodeset="/test_survey_001/location" type="geopoint"/>
      <bind nodeset="/test_survey_001/meta/instanceID" type="string" readonly="true()" calculate="concat(\'uuid:\', uuid())"/>
    </model>
  </h:head>
  <h:body>
    <input ref="/test_survey_001/name">
      <label>What is your name?</label>
    </input>
    <input ref="/test_survey_001/age">
      <label>What is your age?</label>
    </input>
    <input ref="/test_survey_001/location">
      <label>Current location</label>
    </input>
  </h:body>
</html>',
                'status' => 'active',
                'version' => '1.0',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        foreach ($formData as $form) {
            $this->db->table('kobo_forms')->insert($form);
        }

        // Create test submissions
        $submissionData = [
            [
                'submission_id' => 'uuid:' . $this->generateUUID(),
                'form_id' => 'test_survey_001',
                'user_id' => 2,
                'device_id' => 'device_001',
                'xml_data' => '<?xml version="1.0" encoding="UTF-8"?>
<test_survey_001 id="test_survey_001">
  <name>John Doe</name>
  <age>25</age>
  <location>-1.286389 36.817223 0 0</location>
  <meta>
    <instanceID>uuid:' . $this->generateUUID() . '</instanceID>
  </meta>
</test_survey_001>',
                'json_data' => json_encode([
                    'name' => 'John Doe',
                    'age' => '25',
                    'location' => '-1.286389 36.817223 0 0',
                    'meta' => [
                        'instanceID' => 'uuid:' . $this->generateUUID()
                    ]
                ]),
                'status' => 'processed',
                'submitted_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'processed_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
            ],
            [
                'submission_id' => 'uuid:' . $this->generateUUID(),
                'form_id' => 'test_survey_001',
                'user_id' => 2,
                'device_id' => 'device_002',
                'xml_data' => '<?xml version="1.0" encoding="UTF-8"?>
<test_survey_001 id="test_survey_001">
  <name>Jane Smith</name>
  <age>30</age>
  <location>-1.295 36.820 0 0</location>
  <meta>
    <instanceID>uuid:' . $this->generateUUID() . '</instanceID>
  </meta>
</test_survey_001>',
                'json_data' => json_encode([
                    'name' => 'Jane Smith',
                    'age' => '30',
                    'location' => '-1.295 36.820 0 0',
                    'meta' => [
                        'instanceID' => 'uuid:' . $this->generateUUID()
                    ]
                ]),
                'status' => 'pending',
                'submitted_at' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 hour'))
            ],
            [
                'submission_id' => 'uuid:' . $this->generateUUID(),
                'form_id' => 'test_survey_001',
                'user_id' => 2,
                'device_id' => 'device_001',
                'xml_data' => '<?xml version="1.0" encoding="UTF-8"?>
<test_survey_001 id="test_survey_001">
  <name>Bob Johnson</name>
  <age>45</age>
  <location>-1.300 36.825 0 0</location>
  <meta>
    <instanceID>uuid:' . $this->generateUUID() . '</instanceID>
  </meta>
</test_survey_001>',
                'json_data' => json_encode([
                    'name' => 'Bob Johnson',
                    'age' => '45',
                    'location' => '-1.300 36.825 0 0',
                    'meta' => [
                        'instanceID' => 'uuid:' . $this->generateUUID()
                    ]
                ]),
                'status' => 'error',
                'submitted_at' => date('Y-m-d H:i:s', strtotime('-30 minutes')),
                'created_at' => date('Y-m-d H:i:s', strtotime('-30 minutes')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-30 minutes'))
            ]
        ];

        foreach ($submissionData as $submission) {
            $this->db->table('kobo_submissions')->insert($submission);
        }

        echo "Test data seeded successfully!\n";
    }

    private function generateUUID(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}