<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            padding: 2rem 0;
        }
        
        .module-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            margin: 2rem 0;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <header class="header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-2">
                                <li class="breadcrumb-item">
                                    <a href="<?= base_url() ?>" class="text-light text-decoration-none">
                                        <i class="fas fa-home me-1"></i>CodiTest
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="<?= base_url('modules') ?>" class="text-light text-decoration-none">Modules</a>
                                </li>
                                <li class="breadcrumb-item active text-light" aria-current="page">
                                    Kobo Collect
                                </li>
                            </ol>
                        </nav>
                        <h1><i class="fas fa-mobile-alt me-3"></i><?= $module['name'] ?></h1>
                        <p class="mb-0"><?= $module['description'] ?></p>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="badge bg-success fs-6 me-2">
                            <i class="fas fa-check-circle me-1"></i><?= ucfirst($module['status']) ?>
                        </span>
                        <span class="badge bg-light text-dark fs-6">
                            v<?= $module['version'] ?>
                        </span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Module Content -->
        <div class="container py-5">
            <div class="row">
                <div class="col-md-8">
                    <div class="module-card">
                        <h2 class="mb-4">Module Overview</h2>
                        <p class="lead">This module provides a complete Kobo Collect integration system that allows you to create, distribute, and collect survey data using the Kobo Collect mobile application without relying on the standard Kobo Toolbox server.</p>
                        
                        <h3 class="mt-4 mb-3">Key Features</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Form Management & Distribution</li>
                                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>OpenRosa Protocol Compliance</li>
                                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Real-time Data Collection</li>
                                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>XML to JSON Conversion</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>User Management & Authentication</li>
                                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Media File Handling</li>
                                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Offline Data Synchronization</li>
                                    <li class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>Data Export & Analytics</li>
                                </ul>
                            </div>
                        </div>
                        
                        <h3 class="mt-4 mb-3">Technical Implementation</h3>
                        <p>Built using CodeIgniter 4's MVC architecture, this module demonstrates:</p>
                        <ul>
                            <li>RESTful API development with proper HTTP status codes</li>
                            <li>XML parsing and manipulation</li>
                            <li>Database design for complex relationships</li>
                            <li>File upload and media management</li>
                            <li>Authentication and authorization systems</li>
                            <li>Background job processing</li>
                        </ul>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="module-card">
                        <h3 class="mb-4">Quick Actions</h3>
                        <div class="d-grid gap-2">
                            <a href="<?= base_url('modules/kobo-collect/admin') ?>" class="btn btn-primary btn-lg">
                                <i class="fas fa-cog me-2"></i>Admin Panel
                            </a>
                            <a href="<?= base_url('api/v1/formList') ?>" class="btn btn-success btn-lg">
                                <i class="fas fa-code me-2"></i>API Endpoints
                            </a>
                            <a href="<?= base_url('docs/kobo-collect') ?>" class="btn btn-info btn-lg">
                                <i class="fas fa-book me-2"></i>Documentation
                            </a>
                                <i class="fas fa-book me-2"></i>Documentation
                            </a>
                            <a href="https://github.com/anziinols/coditests/tree/master/dev_guide" class="btn btn-warning btn-lg" target="_blank">
                                <i class="fas fa-file-alt me-2"></i>System Design
                            </a>
                        </div>
                        
                        <hr class="my-4">
                        
                        <h4 class="mb-3">Module Statistics</h4>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border rounded p-3">
                                    <h5 class="text-primary mb-1">0</h5>
                                    <small class="text-muted">Active Forms</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-3">
                                    <h5 class="text-success mb-1">0</h5>
                                    <small class="text-muted">Submissions</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row text-center mt-2">
                            <div class="col-6">
                                <div class="border rounded p-3">
                                    <h5 class="text-info mb-1">0</h5>
                                    <small class="text-muted">Users</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-3">
                                    <h5 class="text-warning mb-1">100%</h5>
                                    <small class="text-muted">Health</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="module-card">
                        <h4 class="mb-3">Related Modules</h4>
                        <div class="list-group list-group-flush">
                            <a href="<?= base_url('modules/database-testing') ?>" class="list-group-item list-group-item-action">
                                <i class="fas fa-database me-2"></i>Database Testing
                                <span class="badge bg-secondary ms-auto">Coming Soon</span>
                            </a>
                            <a href="<?= base_url('modules/api-testing') ?>" class="list-group-item list-group-item-action">
                                <i class="fas fa-code me-2"></i>API Development
                                <span class="badge bg-secondary ms-auto">Coming Soon</span>
                            </a>
                            <a href="<?= base_url('modules/file-testing') ?>" class="list-group-item list-group-item-action">
                                <i class="fas fa-upload me-2"></i>File Management
                                <span class="badge bg-secondary ms-auto">Coming Soon</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="bg-dark text-white py-4 mt-5">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <h6>CodiTest - Kobo Collect Module</h6>
                        <p class="mb-0 small">Testing CodeIgniter 4 capabilities through real-world implementations</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="<?= base_url() ?>" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>