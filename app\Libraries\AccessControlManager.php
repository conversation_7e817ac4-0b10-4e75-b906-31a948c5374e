<?php

namespace App\Libraries;

use App\Models\Kobo\KoboUserModel;
use App\Models\Kobo\KoboFormAccessModel;
use App\Models\Kobo\KoboAuditLogModel;

/**
 * Access Control Manager
 * 
 * Handles role-based access control, permissions, and restrictions
 */
class AccessControlManager
{
    protected $userModel;
    protected $formAccessModel;
    protected $auditModel;
    
    // Role hierarchy (higher number = more permissions)
    protected $roleHierarchy = [
        'viewer' => 1,
        'enumerator' => 2,
        'supervisor' => 3,
        'admin' => 4
    ];
    
    // Permission definitions
    protected $permissions = [
        'forms.create' => ['admin', 'supervisor'],
        'forms.read' => ['admin', 'supervisor', 'enumerator', 'viewer'],
        'forms.update' => ['admin', 'supervisor'],
        'forms.delete' => ['admin'],
        'forms.publish' => ['admin', 'supervisor'],
        
        'submissions.create' => ['admin', 'supervisor', 'enumerator'],
        'submissions.read' => ['admin', 'supervisor', 'enumerator', 'viewer'],
        'submissions.update' => ['admin', 'supervisor'],
        'submissions.delete' => ['admin'],
        'submissions.export' => ['admin', 'supervisor'],
        
        'users.create' => ['admin'],
        'users.read' => ['admin', 'supervisor'],
        'users.update' => ['admin'],
        'users.delete' => ['admin'],
        'users.assign_roles' => ['admin'],
        
        'system.settings' => ['admin'],
        'system.logs' => ['admin', 'supervisor'],
        'system.backup' => ['admin'],
        'system.maintenance' => ['admin'],
        
        'reports.view' => ['admin', 'supervisor', 'enumerator'],
        'reports.export' => ['admin', 'supervisor'],
        
        'api.access' => ['admin', 'supervisor', 'enumerator'],
        'api.admin' => ['admin']
    ];

    public function __construct()
    {
        $this->userModel = new KoboUserModel();
        $this->formAccessModel = new KoboFormAccessModel();
        $this->auditModel = new KoboAuditLogModel();
    }

    /**
     * Check if user has permission
     */
    public function hasPermission(int $userId, string $permission, ?array $context = null): bool
    {
        try {
            $user = $this->userModel->find($userId);
            
            if (!$user || $user['status'] !== 'active') {
                return false;
            }

            // Check basic role permission
            if (!$this->checkRolePermission($user['role'], $permission)) {
                return false;
            }

            // Check context-specific permissions (form-level, device-level, etc.)
            if ($context && !$this->checkContextPermission($user, $permission, $context)) {
                return false;
            }

            return true;

        } catch (\Exception $e) {
            log_message('error', 'Permission check failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check role-based permission
     */
    protected function checkRolePermission(string $userRole, string $permission): bool
    {
        if (!isset($this->permissions[$permission])) {
            // Unknown permission - deny by default
            return false;
        }

        $allowedRoles = $this->permissions[$permission];
        return in_array($userRole, $allowedRoles);
    }

    /**
     * Check context-specific permissions
     */
    protected function checkContextPermission(array $user, string $permission, array $context): bool
    {
        // Form-level access control
        if (isset($context['form_id'])) {
            return $this->checkFormAccess($user['id'], $context['form_id'], $permission);
        }

        // Device-level restrictions
        if (isset($context['device_id'])) {
            return $this->checkDeviceAccess($user['id'], $context['device_id']);
        }

        // IP-based restrictions
        if (isset($context['ip_address'])) {
            return $this->checkIPAccess($user['id'], $context['ip_address']);
        }

        // Time-based restrictions
        if (isset($context['current_time'])) {
            return $this->checkTimeAccess($user['id'], $context['current_time']);
        }

        return true;
    }

    /**
     * Check form-specific access
     */
    protected function checkFormAccess(int $userId, string $formId, string $permission): bool
    {
        try {
            $access = $this->formAccessModel->getFormAccess($userId, $formId);
            
            if (!$access || $access['status'] !== 'active') {
                return false;
            }

            // Check permission level
            $permissionLevel = $this->getPermissionLevel($permission);
            $userLevel = $this->getAccessLevel($access['access_level']);

            return $userLevel >= $permissionLevel;

        } catch (\Exception $e) {
            log_message('error', 'Form access check failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check device-based access
     */
    protected function checkDeviceAccess(int $userId, string $deviceId): bool
    {
        try {
            $user = $this->userModel->find($userId);
            
            // If user has no device restrictions, allow access
            if (empty($user['allowed_devices'])) {
                return true;
            }

            $allowedDevices = json_decode($user['allowed_devices'], true);
            return in_array($deviceId, $allowedDevices);

        } catch (\Exception $e) {
            log_message('error', 'Device access check failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check IP-based access restrictions
     */
    protected function checkIPAccess(int $userId, string $ipAddress): bool
    {
        try {
            $user = $this->userModel->find($userId);
            
            // If user has no IP restrictions, allow access
            if (empty($user['ip_restrictions'])) {
                return true;
            }

            $restrictions = json_decode($user['ip_restrictions'], true);
            
            // Check whitelist
            if (isset($restrictions['whitelist'])) {
                return $this->isIPInRange($ipAddress, $restrictions['whitelist']);
            }

            // Check blacklist
            if (isset($restrictions['blacklist'])) {
                return !$this->isIPInRange($ipAddress, $restrictions['blacklist']);
            }

            return true;

        } catch (\Exception $e) {
            log_message('error', 'IP access check failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check time-based access restrictions
     */
    protected function checkTimeAccess(int $userId, string $currentTime): bool
    {
        try {
            $user = $this->userModel->find($userId);
            
            // If user has no time restrictions, allow access
            if (empty($user['time_restrictions'])) {
                return true;
            }

            $restrictions = json_decode($user['time_restrictions'], true);
            $currentDateTime = new \DateTime($currentTime);
            
            // Check allowed hours
            if (isset($restrictions['allowed_hours'])) {
                $hour = (int)$currentDateTime->format('H');
                $start = $restrictions['allowed_hours']['start'] ?? 0;
                $end = $restrictions['allowed_hours']['end'] ?? 23;
                
                if ($hour < $start || $hour > $end) {
                    return false;
                }
            }

            // Check allowed days
            if (isset($restrictions['allowed_days'])) {
                $dayOfWeek = (int)$currentDateTime->format('w'); // 0 = Sunday
                if (!in_array($dayOfWeek, $restrictions['allowed_days'])) {
                    return false;
                }
            }

            return true;

        } catch (\Exception $e) {
            log_message('error', 'Time access check failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Grant form access to user
     */
    public function grantFormAccess(int $userId, string $formId, string $accessLevel = 'read', ?array $restrictions = null): bool
    {
        try {
            $accessData = [
                'user_id' => $userId,
                'form_id' => $formId,
                'access_level' => $accessLevel,
                'restrictions' => $restrictions ? json_encode($restrictions) : null,
                'granted_by' => $this->getCurrentUserId(),
                'granted_at' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ];

            $result = $this->formAccessModel->insert($accessData);
            
            if ($result) {
                $this->auditModel->logActivity(
                    $userId,
                    'access_control',
                    'form_access_granted',
                    $formId,
                    ['access_level' => $accessLevel, 'restrictions' => $restrictions]
                );
            }

            return (bool)$result;

        } catch (\Exception $e) {
            log_message('error', 'Failed to grant form access: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Revoke form access from user
     */
    public function revokeFormAccess(int $userId, string $formId): bool
    {
        try {
            $result = $this->formAccessModel->where('user_id', $userId)
                                          ->where('form_id', $formId)
                                          ->set('status', 'revoked')
                                          ->set('revoked_at', date('Y-m-d H:i:s'))
                                          ->set('revoked_by', $this->getCurrentUserId())
                                          ->update();

            if ($result) {
                $this->auditModel->logActivity(
                    $userId,
                    'access_control',
                    'form_access_revoked',
                    $formId
                );
            }

            return (bool)$result;

        } catch (\Exception $e) {
            log_message('error', 'Failed to revoke form access: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user's effective permissions
     */
    public function getUserPermissions(int $userId): array
    {
        try {
            $user = $this->userModel->find($userId);
            
            if (!$user) {
                return [];
            }

            $userPermissions = [];
            $userRole = $user['role'];

            foreach ($this->permissions as $permission => $allowedRoles) {
                if (in_array($userRole, $allowedRoles)) {
                    $userPermissions[] = $permission;
                }
            }

            return $userPermissions;

        } catch (\Exception $e) {
            log_message('error', 'Failed to get user permissions: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Check if user can access specific resource
     */
    public function canAccessResource(int $userId, string $resource, string $action, ?array $context = null): bool
    {
        $permission = "{$resource}.{$action}";
        return $this->hasPermission($userId, $permission, $context);
    }

    /**
     * Get permission level numeric value
     */
    protected function getPermissionLevel(string $permission): int
    {
        $levels = [
            'read' => 1,
            'create' => 2,
            'update' => 3,
            'delete' => 4
        ];

        // Extract action from permission (e.g., 'forms.update' -> 'update')
        $parts = explode('.', $permission);
        $action = end($parts);

        return $levels[$action] ?? 1;
    }

    /**
     * Get access level numeric value
     */
    protected function getAccessLevel(string $accessLevel): int
    {
        $levels = [
            'read' => 1,
            'write' => 2,
            'manage' => 3,
            'admin' => 4
        ];

        return $levels[$accessLevel] ?? 1;
    }

    /**
     * Check if IP is in range
     */
    protected function isIPInRange(string $ip, array $ranges): bool
    {
        foreach ($ranges as $range) {
            if ($this->ipInRange($ip, $range)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if IP is within a specific range
     */
    protected function ipInRange(string $ip, string $range): bool
    {
        if (strpos($range, '/') === false) {
            // Single IP
            return $ip === $range;
        }

        // CIDR range
        list($subnet, $bits) = explode('/', $range);
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask; # nb: in case the supplied subnet wasn't correctly aligned
        
        return ($ip & $mask) == $subnet;
    }

    /**
     * Get current user ID (from session or request context)
     */
    protected function getCurrentUserId(): ?int
    {
        $request = service('request');
        return $request->user_id ?? null;
    }

    /**
     * Set user device restrictions
     */
    public function setDeviceRestrictions(int $userId, array $deviceIds): bool
    {
        try {
            return $this->userModel->update($userId, [
                'allowed_devices' => json_encode($deviceIds)
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Failed to set device restrictions: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Set user IP restrictions
     */
    public function setIPRestrictions(int $userId, array $restrictions): bool
    {
        try {
            return $this->userModel->update($userId, [
                'ip_restrictions' => json_encode($restrictions)
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Failed to set IP restrictions: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Set user time restrictions
     */
    public function setTimeRestrictions(int $userId, array $restrictions): bool
    {
        try {
            return $this->userModel->update($userId, [
                'time_restrictions' => json_encode($restrictions)
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Failed to set time restrictions: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if user role has higher privileges than another role
     */
    public function hasHigherRole(string $userRole, string $targetRole): bool
    {
        $userLevel = $this->roleHierarchy[$userRole] ?? 0;
        $targetLevel = $this->roleHierarchy[$targetRole] ?? 0;
        
        return $userLevel > $targetLevel;
    }

    /**
     * Get available roles
     */
    public function getAvailableRoles(): array
    {
        return array_keys($this->roleHierarchy);
    }

    /**
     * Log access attempt
     */
    protected function logAccessAttempt(int $userId, string $resource, string $action, bool $granted, ?array $context = null): void
    {
        try {
            $this->auditModel->logActivity(
                $userId,
                'access_control',
                $granted ? 'access_granted' : 'access_denied',
                $resource,
                [
                    'action' => $action,
                    'context' => $context,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            );
        } catch (\Exception $e) {
            log_message('error', 'Failed to log access attempt: ' . $e->getMessage());
        }
    }
}