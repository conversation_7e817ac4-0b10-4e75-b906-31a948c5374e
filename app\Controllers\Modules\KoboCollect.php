<?php

namespace App\Controllers\Modules;

use App\Controllers\BaseController;

class KoboCollect extends BaseController
{
    public function index()
    {
        $data = [
            'title' => 'Kobo Collect Integration Module - CodiTest',
            'module' => [
                'name' => 'Kobo Collect Integration',
                'description' => 'Mobile data collection system with OpenRosa protocol compliance',
                'version' => '1.0.0',
                'status' => 'active'
            ]
        ];
        
        return view('modules/kobo-collect/index', $data);
    }
    
    public function admin()
    {
        $data = [
            'title' => 'Kobo Collect Admin Panel - CodiTest'
        ];
        
        return view('modules/kobo-collect/admin', $data);
    }
}