<?php

namespace App\Models\Kobo;

use CodeIgniter\Model;

/**
 * Kobo Media File Model
 * 
 * Handles file upload handling, media file validation, and file type categorization
 */
class KoboMediaFileModel extends Model
{
    protected $table = 'kobo_media_files';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'form_id',
        'submission_id',
        'original_filename',
        'stored_filename',
        'file_path',
        'file_type',
        'file_size',
        'mime_type',
        'status',
        'uploaded_by',
        'created_at',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'form_id' => 'permit_empty|string|max_length[255]',
        'submission_id' => 'permit_empty|string|max_length[255]',
        'original_filename' => 'required|string|max_length[255]',
        'stored_filename' => 'required|string|max_length[255]',
        'file_path' => 'required|string',
        'file_type' => 'required|in_list[image,audio,video,document,other]',
        'file_size' => 'required|integer',
        'mime_type' => 'required|string|max_length[100]',
        'status' => 'required|in_list[active,deleted,quarantine]'
    ];

    protected $validationMessages = [
        'form_id' => [
            'string' => 'Form ID must be a valid string',
            'max_length' => 'Form ID cannot exceed 255 characters'
        ],
        'submission_id' => [
            'string' => 'Submission ID must be a valid string',
            'max_length' => 'Submission ID cannot exceed 255 characters'
        ],
        'original_filename' => [
            'required' => 'Original filename is required',
            'string' => 'Original filename must be a valid string',
            'max_length' => 'Original filename cannot exceed 255 characters'
        ],
        'stored_filename' => [
            'required' => 'Stored filename is required',
            'string' => 'Stored filename must be a valid string',
            'max_length' => 'Stored filename cannot exceed 255 characters'
        ],
        'file_path' => [
            'required' => 'File path is required',
            'string' => 'File path must be a valid string'
        ],
        'file_type' => [
            'required' => 'File type is required',
            'in_list' => 'File type must be image, audio, video, document, or other'
        ],
        'file_size' => [
            'required' => 'File size is required',
            'integer' => 'File size must be a valid integer'
        ],
        'mime_type' => [
            'required' => 'MIME type is required',
            'string' => 'MIME type must be a valid string',
            'max_length' => 'MIME type cannot exceed 100 characters'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be active, deleted, or quarantine'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // File type mappings
    protected $fileTypeMap = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'],
        'audio' => ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a', 'wma'],
        'video' => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'],
        'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv']
    ];

    // MIME type mappings
    protected $mimeTypeMap = [
        'image' => [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
            'image/bmp', 'image/webp', 'image/svg+xml'
        ],
        'audio' => [
            'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/aac',
            'audio/flac', 'audio/x-m4a', 'audio/x-ms-wma'
        ],
        'video' => [
            'video/mp4', 'video/avi', 'video/quicktime', 'video/x-ms-wmv',
            'video/x-flv', 'video/webm', 'video/x-matroska', 'video/3gpp'
        ],
        'document' => [
            'application/pdf', 'application/msword', 
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain', 'text/csv'
        ]
    ];

    // Max file sizes (in bytes)
    protected $maxFileSizes = [
        'image' => 10485760,    // 10MB
        'audio' => 52428800,    // 50MB
        'video' => 104857600,   // 100MB
        'document' => 20971520, // 20MB
        'other' => 10485760     // 10MB
    ];

    /**
     * Upload and store media file
     */
    public function uploadFile(\CodeIgniter\HTTP\Files\UploadedFile $file, string $formId = null, string $submissionId = null, int $uploadedBy = null): int|false
    {
        // Validate file
        $validation = $this->validateUploadedFile($file);
        if (!empty($validation['errors'])) {
            return false;
        }

        // Generate stored filename
        $storedFilename = $this->generateStoredFilename($file->getClientName(), $file->getClientExtension());
        
        // Determine upload path
        $uploadPath = $this->getUploadPath($formId, $submissionId);
        
        // Create directory if it doesn't exist
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Move file to upload directory
        $fullPath = $uploadPath . DIRECTORY_SEPARATOR . $storedFilename;
        
        if (!$file->move($uploadPath, $storedFilename)) {
            return false;
        }

        // Store file information in database
        $fileData = [
            'form_id' => $formId,
            'submission_id' => $submissionId,
            'original_filename' => $file->getClientName(),
            'stored_filename' => $storedFilename,
            'file_path' => $fullPath,
            'file_type' => $this->determineFileType($file->getClientExtension()),
            'file_size' => $file->getSize(),
            'mime_type' => $file->getClientMimeType(),
            'status' => 'active',
            'uploaded_by' => $uploadedBy
        ];

        return $this->insert($fileData);
    }

    /**
     * Validate uploaded file
     */
    public function validateUploadedFile(\CodeIgniter\HTTP\Files\UploadedFile $file): array
    {
        $errors = [];
        
        // Check if file was uploaded successfully
        if (!$file->isValid() || $file->hasMoved()) {
            $errors[] = 'File upload failed or file has already been moved';
            return ['errors' => $errors];
        }

        // Check file size
        $fileExtension = strtolower($file->getClientExtension());
        $fileType = $this->determineFileType($fileExtension);
        $maxSize = $this->maxFileSizes[$fileType] ?? $this->maxFileSizes['other'];
        
        if ($file->getSize() > $maxSize) {
            $errors[] = "File size exceeds maximum allowed size of " . $this->formatFileSize($maxSize);
        }

        // Check file extension
        if (!$this->isAllowedExtension($fileExtension)) {
            $errors[] = "File extension '{$fileExtension}' is not allowed";
        }

        // Check MIME type
        if (!$this->isAllowedMimeType($file->getClientMimeType(), $fileType)) {
            $errors[] = "File MIME type '{$file->getClientMimeType()}' is not allowed for file type '{$fileType}'";
        }

        // Scan for malicious content (basic check)
        if ($this->isSuspiciousFile($file)) {
            $errors[] = "File appears to contain suspicious content";
        }

        return [
            'errors' => $errors,
            'file_type' => $fileType,
            'file_size' => $file->getSize(),
            'mime_type' => $file->getClientMimeType()
        ];
    }

    /**
     * Determine file type based on extension
     */
    public function determineFileType(string $extension): string
    {
        $extension = strtolower($extension);
        
        foreach ($this->fileTypeMap as $type => $extensions) {
            if (in_array($extension, $extensions)) {
                return $type;
            }
        }

        return 'other';
    }

    /**
     * Check if file extension is allowed
     */
    public function isAllowedExtension(string $extension): bool
    {
        $extension = strtolower($extension);
        
        foreach ($this->fileTypeMap as $extensions) {
            if (in_array($extension, $extensions)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if MIME type is allowed for file type
     */
    public function isAllowedMimeType(string $mimeType, string $fileType): bool
    {
        return in_array($mimeType, $this->mimeTypeMap[$fileType] ?? []);
    }

    /**
     * Basic check for suspicious files
     */
    public function isSuspiciousFile(\CodeIgniter\HTTP\Files\UploadedFile $file): bool
    {
        // Check for executable extensions
        $suspiciousExtensions = ['exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'php', 'asp', 'aspx'];
        $extension = strtolower($file->getClientExtension());
        
        if (in_array($extension, $suspiciousExtensions)) {
            return true;
        }

        // Check file content for suspicious patterns (basic)
        $tempPath = $file->getTempName();
        if (is_readable($tempPath)) {
            $content = file_get_contents($tempPath, false, null, 0, 1024); // Read first 1KB
            
            // Look for script tags or other suspicious patterns
            $suspiciousPatterns = ['<script', '<?php', '<%', 'eval(', 'exec('];
            
            foreach ($suspiciousPatterns as $pattern) {
                if (stripos($content, $pattern) !== false) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Generate unique stored filename
     */
    private function generateStoredFilename(string $originalFilename, string $extension): string
    {
        $basename = pathinfo($originalFilename, PATHINFO_FILENAME);
        $cleanBasename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
        $uniqueId = uniqid();
        
        return $cleanBasename . '_' . $uniqueId . '.' . $extension;
    }

    /**
     * Get upload path for file
     */
    private function getUploadPath(string $formId = null, string $submissionId = null): string
    {
        $basePath = WRITEPATH . 'uploads' . DIRECTORY_SEPARATOR . 'kobo';
        
        if ($formId) {
            $basePath .= DIRECTORY_SEPARATOR . 'forms' . DIRECTORY_SEPARATOR . $formId;
            
            if ($submissionId) {
                $basePath .= DIRECTORY_SEPARATOR . 'submissions' . DIRECTORY_SEPARATOR . $submissionId;
            }
        } else {
            $basePath .= DIRECTORY_SEPARATOR . 'general';
        }

        return $basePath;
    }

    /**
     * Get files by form ID
     */
    public function getFilesByForm(string $formId): array
    {
        return $this->where('form_id', $formId)
                    ->where('status', 'active')
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get files by submission ID
     */
    public function getFilesBySubmission(string $submissionId): array
    {
        return $this->where('submission_id', $submissionId)
                    ->where('status', 'active')
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get files by type
     */
    public function getFilesByType(string $fileType): array
    {
        return $this->where('file_type', $fileType)
                    ->where('status', 'active')
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Delete file (soft delete)
     */
    public function deleteFile(int $fileId): bool
    {
        return $this->update($fileId, ['status' => 'deleted']);
    }

    /**
     * Permanently delete file
     */
    public function permanentlyDeleteFile(int $fileId): bool
    {
        $file = $this->find($fileId);
        
        if (!$file) {
            return false;
        }

        // Delete physical file
        if (file_exists($file['file_path'])) {
            unlink($file['file_path']);
        }

        // Delete database record
        return $this->delete($fileId);
    }

    /**
     * Quarantine suspicious file
     */
    public function quarantineFile(int $fileId): bool
    {
        return $this->update($fileId, ['status' => 'quarantine']);
    }

    /**
     * Restore file from quarantine
     */
    public function restoreFile(int $fileId): bool
    {
        return $this->update($fileId, ['status' => 'active']);
    }

    /**
     * Get file statistics
     */
    public function getFileStats(): array
    {
        $db = \Config\Database::connect();
        
        // Get counts by file type
        $typeQuery = $db->query("
            SELECT 
                file_type,
                status,
                COUNT(*) as count,
                SUM(file_size) as total_size
            FROM kobo_media_files
            GROUP BY file_type, status
            ORDER BY file_type, status
        ");

        $typeStats = [];
        foreach ($typeQuery->getResultArray() as $row) {
            if (!isset($typeStats[$row['file_type']])) {
                $typeStats[$row['file_type']] = [];
            }
            $typeStats[$row['file_type']][$row['status']] = [
                'count' => $row['count'],
                'total_size' => $row['total_size']
            ];
        }

        // Get total statistics
        $totalQuery = $db->query("
            SELECT 
                COUNT(*) as total_files,
                SUM(file_size) as total_size,
                AVG(file_size) as average_size
            FROM kobo_media_files
            WHERE status = 'active'
        ");

        $totals = $totalQuery->getRowArray();

        return [
            'by_type_status' => $typeStats,
            'totals' => $totals,
            'total_active_files' => $this->where('status', 'active')->countAllResults()
        ];
    }

    /**
     * Format file size in human readable format
     */
    public function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Get files with pagination and filters
     */
    public function getFilesWithPagination(int $perPage = 20, array $filters = []): array
    {
        $builder = $this->select('kobo_media_files.*, kobo_users.username as uploaded_by_name')
                        ->join('kobo_users', 'kobo_users.id = kobo_media_files.uploaded_by', 'left');

        // Apply filters
        if (!empty($filters['form_id'])) {
            $builder->where('kobo_media_files.form_id', $filters['form_id']);
        }

        if (!empty($filters['file_type'])) {
            $builder->where('kobo_media_files.file_type', $filters['file_type']);
        }

        if (!empty($filters['status'])) {
            $builder->where('kobo_media_files.status', $filters['status']);
        }

        if (!empty($filters['search'])) {
            $builder->like('kobo_media_files.original_filename', $filters['search']);
        }

        $builder->orderBy('kobo_media_files.created_at', 'DESC');

        return [
            'files' => $builder->paginate($perPage),
            'pager' => $this->pager
        ];
    }

    /**
     * Clean up deleted files older than specified days
     */
    public function cleanupDeletedFiles(int $daysOld = 30): int
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysOld} days"));
        
        $deletedFiles = $this->where('status', 'deleted')
                             ->where('updated_at <', $cutoffDate)
                             ->findAll();

        $cleanedCount = 0;
        foreach ($deletedFiles as $file) {
            if ($this->permanentlyDeleteFile($file['id'])) {
                $cleanedCount++;
            }
        }

        return $cleanedCount;
    }

    /**
     * Get disk usage by form
     */
    public function getDiskUsageByForm(): array
    {
        $db = \Config\Database::connect();
        
        $query = $db->query("
            SELECT 
                form_id,
                COUNT(*) as file_count,
                SUM(file_size) as total_size
            FROM kobo_media_files
            WHERE status = 'active' AND form_id IS NOT NULL
            GROUP BY form_id
            ORDER BY total_size DESC
        ");

        return $query->getResultArray();
    }

    /**
     * Create thumbnail for image files
     */
    public function createThumbnail(int $fileId, int $width = 150, int $height = 150): bool
    {
        $file = $this->find($fileId);
        
        if (!$file || $file['file_type'] !== 'image') {
            return false;
        }

        $imagePath = $file['file_path'];
        if (!file_exists($imagePath)) {
            return false;
        }

        // Create thumbnail path
        $thumbnailPath = str_replace($file['stored_filename'], 'thumb_' . $file['stored_filename'], $imagePath);

        // Load image manipulation library (assuming GD is available)
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            return false;
        }

        $sourceImage = null;
        switch ($imageInfo[2]) {
            case IMAGETYPE_JPEG:
                $sourceImage = imagecreatefromjpeg($imagePath);
                break;
            case IMAGETYPE_PNG:
                $sourceImage = imagecreatefrompng($imagePath);
                break;
            case IMAGETYPE_GIF:
                $sourceImage = imagecreatefromgif($imagePath);
                break;
            default:
                return false;
        }

        if (!$sourceImage) {
            return false;
        }

        // Calculate new dimensions maintaining aspect ratio
        $originalWidth = imagesx($sourceImage);
        $originalHeight = imagesy($sourceImage);
        
        $ratio = min($width / $originalWidth, $height / $originalHeight);
        $newWidth = intval($originalWidth * $ratio);
        $newHeight = intval($originalHeight * $ratio);

        // Create thumbnail
        $thumbnail = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($imageInfo[2] == IMAGETYPE_PNG || $imageInfo[2] == IMAGETYPE_GIF) {
            imagecolortransparent($thumbnail, imagecolorallocatealpha($thumbnail, 0, 0, 0, 127));
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
        }

        imagecopyresampled($thumbnail, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);

        // Save thumbnail
        $success = false;
        switch ($imageInfo[2]) {
            case IMAGETYPE_JPEG:
                $success = imagejpeg($thumbnail, $thumbnailPath, 85);
                break;
            case IMAGETYPE_PNG:
                $success = imagepng($thumbnail, $thumbnailPath, 9);
                break;
            case IMAGETYPE_GIF:
                $success = imagegif($thumbnail, $thumbnailPath);
                break;
        }

        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($thumbnail);

        return $success;
    }

    /**
     * Get file download URL
     */
    public function getDownloadUrl(int $fileId): string|false
    {
        $file = $this->find($fileId);
        
        if (!$file || $file['status'] !== 'active') {
            return false;
        }

        return base_url("api/v1/media/download/{$fileId}");
    }
}