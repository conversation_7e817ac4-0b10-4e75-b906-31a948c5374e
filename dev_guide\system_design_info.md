# Kobo Collect Integration Data System Design

## 1. System Overview

This system creates a custom data collection infrastructure that integrates with Kobo Collect mobile app while bypassing the standard Kobo Toolbox server. The system consists of a CodeIgniter 4 web application that serves as both the form distribution server and data collection endpoint.

### Key Components:
- **CodeIgniter 4 Web Application**: Central server handling form management and data collection
- **Kobo Collect Mobile App**: Data collection interface for field users
- **Database Layer**: Storage for forms, submissions, and user management
- **API Layer**: RESTful endpoints for form distribution and data submission

## 2. System Architecture

```
┌─────────────────┐    ┌──────────────────────┐    ┌─────────────────┐
│   Kobo Collect  │    │   CodeIgniter 4      │    │    Database     │
│   Mobile App    │◄───┤   Web Application    │◄───┤   (MySQL/       │
│                 │    │                      │    │   PostgreSQL)   │
│  - Form Download│    │  - Form Management   │    │                 │
│  - Data Entry   │    │  - Data Collection   │    │  - Forms        │
│  - Submission   │    │  - User Management   │    │  - Submissions  │
└─────────────────┘    │  - API Endpoints     │    │  - Users        │
                       └──────────────────────┘    └─────────────────┘
```

## 3. Database Schema

### 3.1 Core Tables

#### Forms Table
```sql
CREATE TABLE forms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    form_id VARCHAR(255) UNIQUE NOT NULL,
    form_name VARCHAR(255) NOT NULL,
    version VARCHAR(50) NOT NULL,
    xml_content LONGTEXT NOT NULL,
    manifest_content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

#### Submissions Table
```sql
CREATE TABLE submissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    submission_id VARCHAR(255) UNIQUE NOT NULL,
    form_id VARCHAR(255) NOT NULL,
    xml_data LONGTEXT NOT NULL,
    json_data JSON,
    submitted_by VARCHAR(255),
    device_id VARCHAR(255),
    submission_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (form_id) REFERENCES forms(form_id),
    INDEX idx_form_id (form_id),
    INDEX idx_submission_date (submission_date)
);
```

#### Users Table
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'manager', 'enumerator') DEFAULT 'enumerator',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive') DEFAULT 'active'
);
```

#### Form Access Table
```sql
CREATE TABLE form_access (
    id INT PRIMARY KEY AUTO_INCREMENT,
    form_id VARCHAR(255) NOT NULL,
    user_id INT,
    device_id VARCHAR(255),
    access_type ENUM('download', 'submit') NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (form_id) REFERENCES forms(form_id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 4. CodeIgniter 4 Application Structure

### 4.1 Directory Structure
```
app/
├── Controllers/
│   ├── API/
│   │   ├── FormsController.php
│   │   ├── SubmissionsController.php
│   │   └── AuthController.php
│   ├── Admin/
│   │   ├── DashboardController.php
│   │   ├── FormManagerController.php
│   │   └── UserManagerController.php
├── Models/
│   ├── FormModel.php
│   ├── SubmissionModel.php
│   └── UserModel.php
├── Libraries/
│   ├── XFormParser.php
│   ├── KoboAPIHandler.php
│   └── DataValidator.php
├── Helpers/
│   └── kobo_helper.php
└── Config/
    └── Routes.php
```

### 4.2 API Routes Configuration
```php
// app/Config/Routes.php
$routes->group('api/v1', ['namespace' => 'App\Controllers\API'], function($routes) {
    // Form management endpoints
    $routes->get('formList', 'FormsController::getFormList');
    $routes->get('forms/(:segment)', 'FormsController::getForm/$1');
    $routes->get('forms/(:segment)/manifest', 'FormsController::getFormManifest/$1');
    
    // Data submission endpoints
    $routes->post('submission', 'SubmissionsController::submit');
    $routes->post('submissions/(:segment)', 'SubmissionsController::submit/$1');
    
    // Authentication
    $routes->post('auth/login', 'AuthController::login');
    $routes->post('auth/register', 'AuthController::register');
});

// Admin panel routes
$routes->group('admin', ['namespace' => 'App\Controllers\Admin'], function($routes) {
    $routes->get('/', 'DashboardController::index');
    $routes->resource('forms', ['controller' => 'FormManagerController']);
    $routes->resource('users', ['controller' => 'UserManagerController']);
});
```

## 5. Core Controllers Implementation

### 5.1 Forms API Controller
```php
<?php
namespace App\Controllers\API;

use CodeIgniter\RESTful\ResourceController;

class FormsController extends ResourceController
{
    protected $format = 'xml';
    
    public function getFormList()
    {
        $formModel = new \App\Models\FormModel();
        $forms = $formModel->getActiveFormsList();
        
        // Return OpenRosa compliant form list
        return $this->respond($this->buildFormListXML($forms));
    }
    
    public function getForm($formId)
    {
        $formModel = new \App\Models\FormModel();
        $form = $formModel->getFormByFormId($formId);
        
        if (!$form) {
            return $this->failNotFound('Form not found');
        }
        
        $this->response->setHeader('Content-Type', 'text/xml');
        return $this->respond($form['xml_content']);
    }
    
    public function getFormManifest($formId)
    {
        $formModel = new \App\Models\FormModel();
        $form = $formModel->getFormByFormId($formId);
        
        if (!$form || !$form['manifest_content']) {
            return $this->failNotFound('Manifest not found');
        }
        
        $this->response->setHeader('Content-Type', 'text/xml');
        return $this->respond($form['manifest_content']);
    }
}
```

### 5.2 Submissions API Controller
```php
<?php
namespace App\Controllers\API;

use CodeIgniter\RESTful\ResourceController;

class SubmissionsController extends ResourceController
{
    public function submit($formId = null)
    {
        try {
            $xmlData = $this->request->getBody();
            $submissionModel = new \App\Models\SubmissionModel();
            
            // Parse XML and extract form ID if not provided
            if (!$formId) {
                $formId = $this->extractFormIdFromXML($xmlData);
            }
            
            // Validate form exists
            $formModel = new \App\Models\FormModel();
            if (!$formModel->formExists($formId)) {
                return $this->fail('Invalid form ID', 400);
            }
            
            // Generate submission ID
            $submissionId = $this->generateSubmissionId();
            
            // Save submission
            $submissionData = [
                'submission_id' => $submissionId,
                'form_id' => $formId,
                'xml_data' => $xmlData,
                'json_data' => json_encode($this->xmlToArray($xmlData)),
                'submitted_by' => $this->request->getHeaderLine('X-OpenRosa-DeviceID'),
                'device_id' => $this->request->getHeaderLine('X-OpenRosa-DeviceID')
            ];
            
            $submissionModel->save($submissionData);
            
            // Return OpenRosa compliant response
            return $this->respondCreated([
                'message' => 'Submission successful',
                'submissionId' => $submissionId
            ]);
            
        } catch (\Exception $e) {
            log_message('error', 'Submission error: ' . $e->getMessage());
            return $this->fail('Submission failed', 500);
        }
    }
}
```

## 6. Kobo Collect Configuration

### 6.1 Server Configuration
To configure Kobo Collect to use your custom server:

1. **Server URL Configuration**:
   ```
   Server URL: http://localhost/coditests/api/v1
   Username: [user credentials]
   Password: [user password]
   ```

2. **Form Management URL**:
   ```
   Form List URL: http://localhost/coditests/api/v1/formList
   Form Download URL: http://localhost/coditests/api/v1/forms/{formId}
   Submission URL: http://localhost/coditests/api/v1/submission
   ```

### 6.2 OpenRosa Protocol Compliance
Your server must comply with OpenRosa standards:

- **Form List Response**: XML format with proper form metadata
- **Authentication**: Basic HTTP authentication or digest authentication
- **HTTP Headers**: Proper OpenRosa headers for device identification
- **Error Handling**: Standard HTTP status codes

## 7. Security Implementation

### 7.1 Authentication & Authorization
```php
// JWT Token based authentication
class AuthController extends BaseController
{
    public function login()
    {
        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');
        
        $userModel = new UserModel();
        $user = $userModel->validateUser($username, $password);
        
        if ($user) {
            $token = $this->generateJWTToken($user);
            return $this->respond(['token' => $token]);
        }
        
        return $this->fail('Invalid credentials', 401);
    }
}
```

### 7.2 Data Validation
- XML schema validation for form submissions
- Input sanitization for all user inputs
- Rate limiting for API endpoints
- CSRF protection for web forms

### 7.3 Access Control
- Role-based permissions (Admin, Manager, Enumerator)
- Form-level access control
- Device-based restrictions
- IP whitelisting for sensitive operations

## 8. Data Processing & Export

### 8.1 Automated Data Processing
```php
// Background job for processing submissions
class SubmissionProcessor
{
    public function processSubmissions()
    {
        $submissionModel = new SubmissionModel();
        $unprocessed = $submissionModel->getUnprocessedSubmissions();
        
        foreach ($unprocessed as $submission) {
            // Convert XML to structured data
            $processedData = $this->processXMLData($submission['xml_data']);
            
            // Update submission with processed data
            $submissionModel->markAsProcessed($submission['id'], $processedData);
            
            // Trigger webhooks or notifications if configured
            $this->triggerWebhooks($submission);
        }
    }
}
```

### 8.2 Data Export Features
- CSV export for data analysis
- Excel export with formatting
- JSON API for external integrations
- Real-time data synchronization

## 9. Monitoring & Logging

### 9.1 System Monitoring
- API endpoint performance monitoring
- Database query optimization
- Error rate tracking
- Form submission success rates

### 9.2 Audit Logging
- User activity logs
- Form modification history
- Data access logs
- System security events

## 10. Deployment Considerations

### 10.1 Server Requirements
- PHP 8.1 or higher
- MySQL 8.0 or PostgreSQL 13+
- Web server (Apache/Nginx)
- SSL certificate for HTTPS
- Sufficient storage for form data and media files

### 10.2 Scalability
- Database connection pooling
- Redis cache for session management
- Load balancing for high traffic
- CDN for static assets and media files

### 10.3 Backup & Recovery
- Automated database backups
- File system backups for uploaded media
- Disaster recovery procedures
- Data retention policies

## 11. Integration Points

### 11.1 External System Integration
- Webhook support for real-time data push
- REST API for third-party applications
- LDAP/Active Directory for user authentication
- SMS/Email notifications for form submissions

### 11.2 Reporting & Analytics
- Built-in reporting dashboard
- Data visualization components
- Export to BI tools (Power BI, Tableau)
- Custom report generation

This system design provides a complete solution for custom Kobo Collect integration, ensuring data flows directly to your server while maintaining compatibility with the Kobo Collect mobile application.