<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateKoboSubmissionsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'submission_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'form_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'device_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'xml_data' => [
                'type' => 'LONGTEXT',
            ],
            'json_data' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'processing', 'processed', 'error', 'duplicate'],
                'default' => 'pending',
            ],
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 45,
                'null' => true,
            ],
            'submitted_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'processed_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
        ]);
        
        $this->forge->addPrimaryKey('id');
        $this->forge->addUniqueKey('submission_id');
        $this->forge->addKey('form_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('device_id');
        $this->forge->addKey('status');
        $this->forge->addKey('submitted_at');
        $this->forge->addKey('created_at');
        $this->forge->addForeignKey('form_id', 'kobo_forms', 'form_id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('user_id', 'kobo_users', 'id', 'SET NULL', 'CASCADE');
        $this->forge->createTable('kobo_submissions');
    }

    public function down()
    {
        $this->forge->dropTable('kobo_submissions');
    }
}
