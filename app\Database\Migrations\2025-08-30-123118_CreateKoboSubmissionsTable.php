<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateKoboSubmissionsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'submission_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'form_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'xml_data' => [
                'type' => 'LONGTEXT',
            ],
            'json_data' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'submitted_by' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'device_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'processed' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'submission_date' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
        ]);
        
        $this->forge->addPrimaryKey('id');
        $this->forge->addUniqueKey('submission_id');
        $this->forge->addKey('form_id');
        $this->forge->addKey('submission_date');
        $this->forge->addKey('device_id');
        $this->forge->addForeignKey('form_id', 'kobo_forms', 'form_id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('kobo_submissions');
    }

    public function down()
    {
        $this->forge->dropTable('kobo_submissions');
    }
}
