<?php

namespace App\Controllers\Api\V1;

use CodeIgniter\RESTful\ResourceController;
use App\Libraries\JSONAPIExporter;

/**
 * Export API Controller
 * 
 * RESTful API endpoints for data export with pagination and real-time sync
 */
class ExportController extends ResourceController
{
    protected $jsonExporter;
    protected $format = 'json';

    public function __construct()
    {
        parent::__construct();
        $this->jsonExporter = new JSONAPIExporter();
    }

    /**
     * Export submissions with pagination
     * GET /api/v1/export/submissions
     */
    public function submissions()
    {
        try {
            // Get query parameters
            $params = $this->request->getGet();
            
            // Export submissions
            $result = $this->jsonExporter->exportSubmissions($params);
            
            if ($result['success']) {
                return $this->respond($result, 200);
            } else {
                return $this->respond($result, 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Submissions export API error: ' . $e->getMessage());
            return $this->respond([
                'success' => false,
                'error' => [
                    'message' => 'Internal server error',
                    'code' => 500
                ]
            ], 500);
        }
    }

    /**
     * Export forms with metadata
     * GET /api/v1/export/forms
     */
    public function forms()
    {
        try {
            $params = $this->request->getGet();
            $result = $this->jsonExporter->exportForms($params);
            
            if ($result['success']) {
                return $this->respond($result, 200);
            } else {
                return $this->respond($result, 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Forms export API error: ' . $e->getMessage());
            return $this->respond([
                'success' => false,
                'error' => [
                    'message' => 'Internal server error',
                    'code' => 500
                ]
            ], 500);
        }
    }

    /**
     * Export aggregated statistics
     * GET /api/v1/export/statistics
     */
    public function statistics()
    {
        try {
            $params = $this->request->getGet();
            $result = $this->jsonExporter->exportStatistics($params);
            
            if ($result['success']) {
                return $this->respond($result, 200);
            } else {
                return $this->respond($result, 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Statistics export API error: ' . $e->getMessage());
            return $this->respond([
                'success' => false,
                'error' => [
                    'message' => 'Internal server error',
                    'code' => 500
                ]
            ], 500);
        }
    }

    /**
     * Real-time data synchronization
     * GET /api/v1/export/sync
     */
    public function sync()
    {
        try {
            $params = $this->request->getGet();
            $result = $this->jsonExporter->syncData($params);
            
            if ($result['success']) {
                return $this->respond($result, 200);
            } else {
                return $this->respond($result, 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Real-time sync API error: ' . $e->getMessage());
            return $this->respond([
                'success' => false,
                'error' => [
                    'message' => 'Internal server error',
                    'code' => 500
                ]
            ], 500);
        }
    }

    /**
     * API documentation endpoint
     * GET /api/v1/export/docs
     */
    public function docs()
    {
        $documentation = [
            'api_version' => '1.0',
            'endpoints' => [
                'submissions' => [
                    'method' => 'GET',
                    'url' => base_url('api/v1/export/submissions'),
                    'description' => 'Export submission data with pagination and filtering',
                    'parameters' => [
                        'page' => 'Page number (default: 1)',
                        'page_size' => 'Number of records per page (default: 50, max: 500)',
                        'form_id' => 'Filter by form ID',
                        'user_id' => 'Filter by user ID',
                        'status' => 'Filter by status (draft, active, inactive, processed, failed)',
                        'date_from' => 'Filter by date from (Y-m-d H:i:s)',
                        'date_to' => 'Filter by date to (Y-m-d H:i:s)',
                        'fields' => 'Comma-separated list of fields to include',
                        'sort_by' => 'Sort field (id, created_at, updated_at, title, status)',
                        'sort_order' => 'Sort order (ASC, DESC)',
                        'include_metadata' => 'Include metadata (true/false)',
                        'include_media' => 'Include media files (true/false)',
                        'flatten_json' => 'Flatten JSON data structure (true/false)',
                        'real_time' => 'Disable caching for real-time data (true/false)'
                    ],
                    'example' => base_url('api/v1/export/submissions?page=1&page_size=25&form_id=1&include_metadata=true')
                ],
                'forms' => [
                    'method' => 'GET',
                    'url' => base_url('api/v1/export/forms'),
                    'description' => 'Export form definitions with metadata',
                    'parameters' => [
                        'page' => 'Page number (default: 1)',
                        'page_size' => 'Number of records per page (default: 50, max: 500)',
                        'status' => 'Filter by status',
                        'date_from' => 'Filter by creation date from',
                        'date_to' => 'Filter by creation date to',
                        'fields' => 'Comma-separated list of fields to include',
                        'include_metadata' => 'Include metadata (true/false)',
                        'real_time' => 'Disable caching for real-time data (true/false)'
                    ],
                    'example' => base_url('api/v1/export/forms?status=active&include_metadata=true')
                ],
                'statistics' => [
                    'method' => 'GET',
                    'url' => base_url('api/v1/export/statistics'),
                    'description' => 'Export aggregated statistics and analytics',
                    'parameters' => [
                        'real_time' => 'Disable caching for real-time data (true/false)'
                    ],
                    'example' => base_url('api/v1/export/statistics')
                ],
                'sync' => [
                    'method' => 'GET',
                    'url' => base_url('api/v1/export/sync'),
                    'description' => 'Real-time data synchronization endpoint',
                    'parameters' => [
                        'since_timestamp' => 'Unix timestamp for changes since (default: 1 hour ago)',
                        'entity_types' => 'Comma-separated entity types (submissions, forms, users)',
                        'limit' => 'Maximum number of changes to return (default: 100, max: 1000)'
                    ],
                    'example' => base_url('api/v1/export/sync?since_timestamp=1693123200&entity_types=submissions,forms')
                ]
            ],
            'authentication' => [
                'method' => 'Bearer Token',
                'header' => 'Authorization: Bearer YOUR_JWT_TOKEN',
                'description' => 'All export endpoints require valid JWT authentication'
            ],
            'response_format' => [
                'success_response' => [
                    'success' => true,
                    'data' => '...',
                    'pagination' => '...',
                    'generated_at' => 'Y-m-d H:i:s',
                    'api_version' => '1.0'
                ],
                'error_response' => [
                    'success' => false,
                    'error' => [
                        'message' => 'Error description',
                        'code' => 'HTTP status code',
                        'timestamp' => 'Y-m-d H:i:s'
                    ],
                    'api_version' => '1.0'
                ]
            ],
            'rate_limits' => [
                'default' => '60 requests per hour',
                'authenticated' => '1000 requests per hour',
                'headers' => [
                    'X-RateLimit-Limit' => 'Total requests allowed',
                    'X-RateLimit-Remaining' => 'Requests remaining',
                    'X-RateLimit-Reset' => 'Reset timestamp'
                ]
            ]
        ];

        return $this->respond($documentation, 200);
    }

    /**
     * Health check endpoint
     * GET /api/v1/export/health
     */
    public function health()
    {
        $health = [
            'status' => 'healthy',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'services' => [
                'database' => $this->checkDatabaseHealth(),
                'cache' => $this->checkCacheHealth(),
                'storage' => $this->checkStorageHealth()
            ],
            'performance' => [
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true),
                'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
            ]
        ];

        // Determine overall health status
        $servicesHealthy = array_reduce($health['services'], function($carry, $service) {
            return $carry && $service['status'] === 'healthy';
        }, true);

        if (!$servicesHealthy) {
            $health['status'] = 'degraded';
        }

        $statusCode = $health['status'] === 'healthy' ? 200 : 503;
        return $this->respond($health, $statusCode);
    }

    /**
     * Check database connectivity
     */
    protected function checkDatabaseHealth(): array
    {
        try {
            $db = \Config\Database::connect();
            $db->query('SELECT 1');
            
            return [
                'status' => 'healthy',
                'response_time' => 'fast',
                'last_check' => date('Y-m-d H:i:s')
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'last_check' => date('Y-m-d H:i:s')
            ];
        }
    }

    /**
     * Check cache service
     */
    protected function checkCacheHealth(): array
    {
        try {
            $cache = cache();
            $testKey = 'health_check_' . time();
            $cache->save($testKey, 'test', 10);
            $retrieved = $cache->get($testKey);
            $cache->delete($testKey);
            
            return [
                'status' => $retrieved === 'test' ? 'healthy' : 'degraded',
                'response_time' => 'fast',
                'last_check' => date('Y-m-d H:i:s')
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'last_check' => date('Y-m-d H:i:s')
            ];
        }
    }

    /**
     * Check storage accessibility
     */
    protected function checkStorageHealth(): array
    {
        try {
            $uploadPath = WRITEPATH . 'uploads/';
            $testFile = $uploadPath . 'health_check_' . time() . '.txt';
            
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            
            file_put_contents($testFile, 'health check');
            $content = file_get_contents($testFile);
            unlink($testFile);
            
            return [
                'status' => $content === 'health check' ? 'healthy' : 'degraded',
                'writable' => is_writable($uploadPath),
                'last_check' => date('Y-m-d H:i:s')
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'last_check' => date('Y-m-d H:i:s')
            ];
        }
    }
}