<?php

namespace App\Controllers\API\V1;

use App\Controllers\BaseController;
use App\Models\Kobo\KoboUserModel;
use App\Models\Kobo\KoboAuditLogModel;
use App\Libraries\JWTHelper;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Authentication API Controller
 * 
 * Handles JWT token authentication, user login/logout, and registration
 */
class AuthController extends BaseController
{
    protected $userModel;
    protected $auditModel;
    protected $jwtHelper;

    public function __construct()
    {
        $this->userModel = new KoboUserModel();
        $this->auditModel = new KoboAuditLogModel();
        $this->jwtHelper = new JWTHelper();
    }

    /**
     * User login
     * POST /api/v1/auth/login
     */
    public function login(): ResponseInterface
    {
        try {
            $loginData = $this->request->getJSON(true);
            
            if (!isset($loginData['username']) || !isset($loginData['password'])) {
                return $this->respond([
                    'error' => 'Username and password are required'
                ], 400);
            }

            $username = $loginData['username'];
            $password = $loginData['password'];

            // Authenticate user
            $user = $this->userModel->authenticateUser($username, $password);
            
            if (!$user) {
                // Log failed login attempt
                $this->auditModel->logSecurityEvent(
                    'login_failed',
                    null,
                    [
                        'username' => $username,
                        'ip_address' => $this->request->getIPAddress(),
                        'user_agent' => $this->request->getUserAgent()->getAgentString()
                    ]
                );

                return $this->respond([
                    'error' => 'Invalid credentials'
                ], 401);
            }

            // Generate JWT tokens using helper
            $tokenResponse = $this->jwtHelper->createTokenResponse($user);

            // Log successful login
            $this->auditModel->logLogin($user['id'], true);

            // Update last login time
            $this->userModel->update($user['id'], [
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            return $this->respond([
                'success' => true,
                'message' => 'Login successful',
                'data' => $tokenResponse
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Login error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * User logout
     * POST /api/v1/auth/logout
     */
    public function logout(): ResponseInterface
    {
        try {
            // Get user from JWT token
            $user = $this->getCurrentUser();
            
            if (!$user) {
                return $this->respond([
                    'error' => 'Not authenticated'
                ], 401);
            }

            // Log logout
            $this->auditModel->logLogout($user['id']);

            // In a production system, you would:
            // 1. Add token to blacklist
            // 2. Invalidate refresh token
            // 3. Clear any session data

            return $this->respond([
                'success' => true,
                'message' => 'Logout successful'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Logout error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * User registration
     * POST /api/v1/auth/register
     */
    public function register(): ResponseInterface
    {
        try {
            $userData = $this->request->getJSON(true);
            
            // Validate required fields
            $requiredFields = ['username', 'email', 'password'];
            foreach ($requiredFields as $field) {
                if (!isset($userData[$field]) || empty($userData[$field])) {
                    return $this->respond([
                        'error' => "Field '{$field}' is required"
                    ], 400);
                }
            }

            // Validate password strength
            $passwordErrors = $this->userModel->validatePasswordStrength($userData['password']);
            if (!empty($passwordErrors)) {
                return $this->respond([
                    'error' => 'Password validation failed',
                    'details' => $passwordErrors
                ], 400);
            }

            // Check if registration is allowed
            if (!$this->isRegistrationAllowed()) {
                return $this->respond([
                    'error' => 'Registration is not allowed'
                ], 403);
            }

            // Set default role for new users
            $userData['role'] = $userData['role'] ?? 'enumerator';
            $userData['status'] = 'active';
            $userData['password'] = $userData['password']; // Will be hashed by model

            // Create user
            $userId = $this->userModel->createUser($userData);
            
            if (!$userId) {
                // Get validation errors
                $errors = $this->userModel->errors();
                return $this->respond([
                    'error' => 'Registration failed',
                    'details' => $errors
                ], 400);
            }

            // Get created user
            $user = $this->userModel->find($userId);
            unset($user['password_hash']); // Remove sensitive data

            // Log registration
            $this->auditModel->logActivity(
                $userId,
                'user',
                'registered',
                $userId,
                ['new_data' => $user]
            );

            return $this->respond([
                'success' => true,
                'message' => 'Registration successful',
                'data' => [
                    'user' => $user
                ]
            ], 201);

        } catch (\Exception $e) {
            log_message('error', 'Registration error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Refresh access token
     * POST /api/v1/auth/refresh
     */
    public function refresh(): ResponseInterface
    {
        try {
            $refreshData = $this->request->getJSON(true);
            
            if (!isset($refreshData['refresh_token'])) {
                return $this->respond([
                    'error' => 'Refresh token is required'
                ], 400);
            }

            $refreshToken = $refreshData['refresh_token'];

            // Validate refresh token and get user
            try {
                $tokenPayload = $this->jwtHelper->validateToken($refreshToken);
                
                if (!isset($tokenPayload->token_type) || $tokenPayload->token_type !== 'refresh') {
                    return $this->respond([
                        'error' => 'Invalid refresh token type'
                    ], 401);
                }

                // Check if user is still active
                $currentUser = $this->userModel->find($tokenPayload->sub);
                if (!$currentUser || $currentUser['status'] !== 'active') {
                    return $this->respond([
                        'error' => 'User account is inactive'
                    ], 401);
                }

                // Refresh tokens using helper
                $tokenResponse = $this->jwtHelper->refreshAccessToken($refreshToken, $currentUser);

                return $this->respond([
                    'success' => true,
                    'message' => 'Token refreshed successfully',
                    'data' => $tokenResponse
                ]);
                
            } catch (\Exception $e) {
                return $this->respond([
                    'error' => 'Invalid refresh token'
                ], 401);
            }

        } catch (\Exception $e) {
            log_message('error', 'Token refresh error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get current user profile
     * GET /api/v1/auth/me
     */
    public function me(): ResponseInterface
    {
        try {
            $user = $this->getCurrentUser();
            
            if (!$user) {
                return $this->respond([
                    'error' => 'Not authenticated'
                ], 401);
            }

            // Get fresh user data
            $currentUser = $this->userModel->find($user['id']);
            unset($currentUser['password_hash']); // Remove sensitive data

            return $this->respond([
                'success' => true,
                'data' => [
                    'user' => $currentUser
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Get current user error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Change password
     * POST /api/v1/auth/change-password
     */
    public function changePassword(): ResponseInterface
    {
        try {
            $user = $this->getCurrentUser();
            
            if (!$user) {
                return $this->respond([
                    'error' => 'Not authenticated'
                ], 401);
            }

            $passwordData = $this->request->getJSON(true);
            
            if (!isset($passwordData['current_password']) || !isset($passwordData['new_password'])) {
                return $this->respond([
                    'error' => 'Current password and new password are required'
                ], 400);
            }

            // Verify current password
            $currentUser = $this->userModel->find($user['id']);
            if (!password_verify($passwordData['current_password'], $currentUser['password_hash'])) {
                return $this->respond([
                    'error' => 'Current password is incorrect'
                ], 400);
            }

            // Validate new password strength
            $passwordErrors = $this->userModel->validatePasswordStrength($passwordData['new_password']);
            if (!empty($passwordErrors)) {
                return $this->respond([
                    'error' => 'New password validation failed',
                    'details' => $passwordErrors
                ], 400);
            }

            // Update password
            if (!$this->userModel->updatePassword($user['id'], $passwordData['new_password'])) {
                return $this->respond([
                    'error' => 'Failed to update password'
                ], 500);
            }

            // Log password change
            $this->auditModel->logActivity(
                $user['id'],
                'user',
                'password_changed',
                $user['id']
            );

            return $this->respond([
                'success' => true,
                'message' => 'Password changed successfully'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Password change error: ' . $e->getMessage());
            return $this->respond([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Validate JWT token (public method for other controllers)
     */
    public function validateToken(string $token): array|false
    {
        try {
            $decoded = $this->jwtHelper->validateToken($token);
            
            // Convert to user array format
            return [
                'id' => $decoded->sub,
                'username' => $decoded->username ?? null,
                'role' => $decoded->role ?? null
            ];

        } catch (\Exception $e) {
            log_message('debug', 'Token validation failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get current authenticated user
     */
    private function getCurrentUser(): array|false
    {
        $authHeader = $this->request->getHeader('Authorization');
        
        if (!$authHeader || !str_starts_with($authHeader->getValue(), 'Bearer ')) {
            return false;
        }

        $token = $this->jwtHelper->extractTokenFromHeader($authHeader->getValue());
        return $this->validateToken($token);
    }

    /**
     * Check if registration is allowed
     */
    private function isRegistrationAllowed(): bool
    {
        // In production, you might want to:
        // 1. Check environment settings
        // 2. Verify admin approval is required
        // 3. Check invitation codes
        // 4. Implement rate limiting
        
        return env('ALLOW_REGISTRATION', true);
    }
}