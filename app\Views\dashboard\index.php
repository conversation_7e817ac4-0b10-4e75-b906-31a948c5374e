<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'CodiTest - CodeIgniter 4 Testing Platform' ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --info-color: #0891b2;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, var(--dark-color) 0%, #334155 100%);
            color: white;
            padding: 2rem 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-weight: 700;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0;
        }

        .dashboard-container {
            padding: 3rem 0;
        }

        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .feature-card.coming-soon {
            opacity: 0.7;
            border: 2px dashed var(--border-color);
        }

        .feature-card.coming-soon:hover {
            transform: none;
            opacity: 0.8;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.35rem 0.65rem;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
            border-color: transparent;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1.5rem;
        }

        .feature-card h3 {
            font-weight: 600;
            font-size: 1.25rem;
            margin-bottom: 0.75rem;
            color: var(--dark-color);
        }

        .feature-card p {
            color: var(--secondary-color);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
            flex-grow: 1;
        }

        .feature-list li {
            padding: 0.5rem 0;
            color: var(--secondary-color);
            font-size: 0.9rem;
            border-bottom: 1px solid #f1f5f9;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li::before {
            content: "✓";
            color: var(--success-color);
            font-weight: bold;
            margin-right: 0.5rem;
        }

        .action-button {
            margin-top: auto;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-primary { background: var(--primary-color); }
        .btn-success { background: var(--success-color); }
        .btn-info { background: var(--info-color); }
        .btn-warning { background: var(--warning-color); }
        .btn-danger { background: var(--danger-color); }
        .btn-secondary { background: var(--secondary-color); }

        .action-button:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }

        .stats-section {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 3rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid var(--border-color);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--secondary-color);
            margin-top: 0.25rem;
        }

        .footer {
            background: var(--dark-color);
            color: white;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .feature-card {
                margin-bottom: 1.5rem;
            }
            
            .dashboard-container {
                padding: 2rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <header class="header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1><i class="fas fa-flask me-3"></i><?= $appName ?? 'CodiTest' ?></h1>
                        <p><?= $appDescription ?? 'Modular CodeIgniter 4 Testing & Feature Demonstration Platform' ?></p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="/docs" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-book me-1"></i> Documentation
                            </a>
                            <a href="/modules" class="btn btn-light btn-sm">
                                <i class="fas fa-th-large me-1"></i> All Modules
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-container">
            <div class="container">
                <!-- System Statistics -->
                <div class="stats-section">
                    <h2 class="mb-4 text-center">System Overview</h2>
                    <div class="row">
                        <div class="col-md-3 col-6">
                            <div class="stat-item">
                                <div class="stat-number">8</div>
                                <div class="stat-label">Available Modules</div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="stat-item">
                                <div class="stat-number">1</div>
                                <div class="stat-label">Active Modules</div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="stat-item">
                                <div class="stat-number">7</div>
                                <div class="stat-label">Coming Soon</div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="stat-item">
                                <div class="stat-number">100%</div>
                                <div class="stat-label">Platform Health</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Module Cards -->
                <h2 class="mb-4 text-center">CodeIgniter 4 Testing Modules</h2>
                <div class="row">
                    <?php foreach ($modules as $module): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="feature-card<?= $module['status'] === 'coming_soon' ? ' coming-soon' : '' ?>">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="feature-icon bg-<?= $module['color'] ?>">
                                    <i class="<?= $module['icon'] ?>"></i>
                                </div>
                                <?php if ($module['status'] === 'coming_soon'): ?>
                                <span class="badge bg-secondary">Coming Soon</span>
                                <?php elseif ($module['status'] === 'active'): ?>
                                <span class="badge bg-success">Active</span>
                                <?php endif; ?>
                            </div>
                            <h3><?= $module['title'] ?></h3>
                            <p><?= $module['description'] ?></p>
                            <ul class="feature-list">
                                <?php foreach ($module['features'] as $item): ?>
                                <li><?= $item ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <?php if ($module['status'] === 'active'): ?>
                            <a href="<?= $module['url'] ?>" class="action-button btn btn-<?= $module['color'] ?> text-white">
                                <i class="fas fa-play me-2"></i>Launch Module
                            </a>
                            <?php else: ?>
                            <button class="action-button btn btn-outline-<?= $module['color'] ?>" disabled>
                                <i class="fas fa-clock me-2"></i>Coming Soon
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Quick Actions -->
                <div class="row mt-5">
                    <div class="col-12">
                        <div class="stats-section">
                            <h3 class="mb-4 text-center">Quick Development Actions</h3>
                            <div class="row text-center">
                                <div class="col-md-3 col-6 mb-3">
                                    <a href="<?= base_url('modules/kobo-collect') ?>" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-mobile-alt mb-2 d-block"></i>
                                        Kobo Collect
                                    </a>
                                </div>
                                <div class="col-md-3 col-6 mb-3">
                                    <a href="<?= base_url('docs') ?>" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-book mb-2 d-block"></i>
                                        Documentation
                                    </a>
                                </div>
                                <div class="col-md-3 col-6 mb-3">
                                    <a href="<?= base_url('modules') ?>" class="btn btn-info btn-lg w-100">
                                        <i class="fas fa-th-large mb-2 d-block"></i>
                                        All Modules
                                    </a>
                                </div>
                                <div class="col-md-3 col-6 mb-3">
                                    <a href="https://github.com/anziinols/coditests" class="btn btn-warning btn-lg w-100" target="_blank">
                                        <i class="fab fa-github mb-2 d-block"></i>
                                        GitHub Repo
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <h5>CodiTest</h5>
                        <p class="mb-0">CodeIgniter 4 Testing & Feature Demonstration Platform</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <p class="mb-0">
                            <i class="fas fa-flask me-2"></i>
                            Built for testing and demonstrating CI4 capabilities
                        </p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Add smooth scrolling and interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate stats on load
            const stats = document.querySelectorAll('.stat-number');
            stats.forEach(stat => {
                const finalValue = stat.textContent;
                if (!isNaN(finalValue)) {
                    let currentValue = 0;
                    const increment = finalValue / 50;
                    const timer = setInterval(() => {
                        currentValue += increment;
                        stat.textContent = Math.floor(currentValue);
                        if (currentValue >= finalValue) {
                            stat.textContent = finalValue;
                            clearInterval(timer);
                        }
                    }, 30);
                }
            });

            // Add hover effects to cards
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.borderColor = getComputedStyle(this.querySelector('.feature-icon')).backgroundColor;
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.borderColor = 'var(--border-color)';
                });
            });
        });
    </script>
</body>
</html>