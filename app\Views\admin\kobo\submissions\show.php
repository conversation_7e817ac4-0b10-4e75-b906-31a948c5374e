<?= $this->extend('admin/layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2 fw-bold">
        <i class="bi bi-file-earmark-text text-primary me-2"></i>
        Submission Details
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?= base_url('admin/kobo/submissions') ?>" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>
                Back to List
            </a>
            <button class="btn btn-sm btn-outline-primary" onclick="downloadSubmission()">
                <i class="bi bi-download me-1"></i>
                Download
            </button>
        </div>
        <div class="btn-group">
            <button class="btn btn-sm btn-outline-success" onclick="updateStatus('processed')">
                <i class="bi bi-check-circle me-1"></i>
                Mark Processed
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="deleteSubmission()">
                <i class="bi bi-trash me-1"></i>
                Delete
            </button>
        </div>
    </div>
</div>

<!-- Submission Info Card -->
<div class="row">
    <div class="col-lg-8">
        <!-- Basic Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Basic Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Submission ID:</dt>
                            <dd class="col-sm-8">
                                <code><?= esc($submission['submission_id']) ?></code>
                            </dd>
                            
                            <dt class="col-sm-4">Form ID:</dt>
                            <dd class="col-sm-8">
                                <code><?= esc($submission['form_id']) ?></code>
                            </dd>
                            
                            <dt class="col-sm-4">Status:</dt>
                            <dd class="col-sm-8">
                                <?php
                                $statusColors = [
                                    'pending' => 'warning',
                                    'processing' => 'info',
                                    'processed' => 'success',
                                    'error' => 'danger'
                                ];
                                $statusColor = $statusColors[$submission['status']] ?? 'secondary';
                                ?>
                                <span class="badge bg-<?= $statusColor ?>">
                                    <?= ucfirst($submission['status']) ?>
                                </span>
                            </dd>
                            
                            <dt class="col-sm-4">Device ID:</dt>
                            <dd class="col-sm-8">
                                <?= esc($submission['device_id'] ?? 'Unknown') ?>
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Submitted:</dt>
                            <dd class="col-sm-8">
                                <?= date('M j, Y \a\t H:i:s', strtotime($submission['created_at'])) ?>
                            </dd>
                            
                            <?php if ($submission['processed_at']): ?>
                            <dt class="col-sm-4">Processed:</dt>
                            <dd class="col-sm-8">
                                <?= date('M j, Y \a\t H:i:s', strtotime($submission['processed_at'])) ?>
                            </dd>
                            <?php endif; ?>
                            
                            <dt class="col-sm-4">Form Name:</dt>
                            <dd class="col-sm-8">
                                <?= esc($form['form_name'] ?? 'Unknown Form') ?>
                            </dd>
                            
                            <dt class="col-sm-4">IP Address:</dt>
                            <dd class="col-sm-8">
                                <?= esc($submission['ip_address'] ?? 'Not recorded') ?>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submission Data -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-database me-2"></i>
                    Submission Data
                </h5>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-secondary" onclick="toggleDataView('formatted')" id="btnFormatted">
                        Formatted
                    </button>
                    <button class="btn btn-outline-secondary" onclick="toggleDataView('raw')" id="btnRaw">
                        Raw JSON
                    </button>
                    <button class="btn btn-outline-secondary" onclick="toggleDataView('xml')" id="btnXML">
                        XML
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Formatted View -->
                <div id="formattedView" class="data-view">
                    <?php if (!empty($submission_data)): ?>
                        <div class="row">
                            <?php foreach ($submission_data as $field => $value): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="border rounded p-3">
                                        <strong class="text-primary"><?= esc($field) ?>:</strong><br>
                                        <span class="text-muted">
                                            <?php if (is_array($value)): ?>
                                                <pre class="mb-0"><?= esc(json_encode($value, JSON_PRETTY_PRINT)) ?></pre>
                                            <?php else: ?>
                                                <?= esc($value) ?>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No formatted data available</p>
                    <?php endif; ?>
                </div>

                <!-- Raw JSON View -->
                <div id="rawView" class="data-view" style="display: none;">
                    <pre class="bg-light p-3 rounded"><code><?= esc(json_encode($submission_data, JSON_PRETTY_PRINT)) ?></code></pre>
                </div>

                <!-- XML View -->
                <div id="xmlView" class="data-view" style="display: none;">
                    <pre class="bg-light p-3 rounded"><code><?= esc($submission['xml_data']) ?></code></pre>
                </div>
            </div>
        </div>

        <!-- Media Files -->
        <?php if (!empty($media_files)): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-paperclip me-2"></i>
                    Media Files (<?= count($media_files) ?>)
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($media_files as $media): ?>
                        <div class="col-md-4 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title"><?= esc($media['original_name']) ?></h6>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            Type: <?= esc($media['file_type']) ?><br>
                                            Size: <?= number_format($media['file_size'] / 1024, 2) ?> KB
                                        </small>
                                    </p>
                                    <a href="<?= base_url('admin/kobo/media/' . $media['id'] . '/download') ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-download"></i> Download
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Actions Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-success btn-sm" onclick="updateStatus('processed')">
                        <i class="bi bi-check-circle me-1"></i>
                        Mark as Processed
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="updateStatus('pending')">
                        <i class="bi bi-clock me-1"></i>
                        Mark as Pending
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="updateStatus('error')">
                        <i class="bi bi-x-circle me-1"></i>
                        Mark as Error
                    </button>
                    <hr>
                    <button class="btn btn-outline-info btn-sm" onclick="showVisualization()">
                        <i class="bi bi-bar-chart me-1"></i>
                        Data Visualization
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="exportSubmission()">
                        <i class="bi bi-download me-1"></i>
                        Export Data
                    </button>
                </div>
            </div>
        </div>

        <!-- Activity Log -->
        <?php if (!empty($audit_logs)): ?>
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Activity Log</h6>
            </div>
            <div class="card-body">
                <?php foreach ($audit_logs as $log): ?>
                    <div class="activity-item p-2 mb-2">
                        <small>
                            <strong><?= esc($log['action']) ?></strong><br>
                            <span class="text-muted">
                                <?= date('M j, Y H:i', strtotime($log['created_at'])) ?>
                            </span>
                        </small>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    let currentSubmissionId = <?= $submission['id'] ?>;

    function toggleDataView(view) {
        // Hide all views
        document.querySelectorAll('.data-view').forEach(el => el.style.display = 'none');
        document.querySelectorAll('[id^="btn"]').forEach(btn => btn.classList.remove('active'));
        
        // Show selected view
        document.getElementById(view + 'View').style.display = 'block';
        document.getElementById('btn' + view.charAt(0).toUpperCase() + view.slice(1)).classList.add('active');
    }

    function updateStatus(newStatus) {
        if (confirm(`Are you sure you want to change the status to "${newStatus}"?`)) {
            ajaxRequest(`<?= base_url('admin/kobo/submissions') ?>/${currentSubmissionId}/status`, 'POST', {
                status: newStatus
            }, function(data) {
                if (data.success) {
                    showToast('Status updated successfully', 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast('Failed to update status: ' + (data.message || 'Unknown error'), 'danger');
                }
            });
        }
    }

    function deleteSubmission() {
        if (confirm('Are you sure you want to delete this submission? This action cannot be undone.')) {
            ajaxRequest(`<?= base_url('admin/kobo/submissions') ?>/${currentSubmissionId}`, 'DELETE', null, function(data) {
                if (data.success) {
                    showToast('Submission deleted successfully', 'success');
                    setTimeout(() => window.location.href = '<?= base_url('admin/kobo/submissions') ?>', 1000);
                } else {
                    showToast('Failed to delete submission: ' + (data.message || 'Unknown error'), 'danger');
                }
            });
        }
    }

    function downloadSubmission() {
        window.open(`<?= base_url('admin/kobo/submissions') ?>/${currentSubmissionId}/export?format=json`, '_blank');
    }

    function exportSubmission() {
        const format = prompt('Export format (json/csv):', 'json');
        if (format && ['json', 'csv'].includes(format.toLowerCase())) {
            window.open(`<?= base_url('admin/kobo/submissions') ?>/${currentSubmissionId}/export?format=${format}`, '_blank');
        }
    }

    function showVisualization() {
        ajaxRequest(`<?= base_url('admin/kobo/submissions') ?>/${currentSubmissionId}/visualization`, 'GET', null, function(data) {
            if (data.success) {
                // TODO: Implement visualization modal
                showToast('Visualization data loaded', 'info');
                console.log('Visualization data:', data.visualization_data);
            } else {
                showToast('Failed to load visualization data', 'danger');
            }
        });
    }

    // Initialize formatted view as default
    document.addEventListener('DOMContentLoaded', function() {
        toggleDataView('formatted');
    });
</script>
<?= $this->endSection() ?>