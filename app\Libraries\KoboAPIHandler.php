<?php

namespace App\Libraries;

use CodeIgniter\HTTP\ResponseInterface;

/**
 * Kobo API Handler Library
 * 
 * Implements OpenRosa protocol methods, HTTP header management,
 * and response formatting utilities for Kobo Collect compatibility
 */
class KoboAPIHandler
{
    protected $response;
    protected $request;
    
    public function __construct()
    {
        $this->response = \Config\Services::response();
        $this->request = \Config\Services::request();
    }

    /**
     * Set OpenRosa compliant headers
     */
    public function setOpenRosaHeaders(): self
    {
        $this->response->setHeader('X-OpenRosa-Version', '1.0')
                      ->setHeader('X-OpenRosa-Accept-Content-Length', '10485760')
                      ->setHeader('Cache-Control', 'no-cache')
                      ->setHeader('Expires', '-1');
        
        return $this;
    }

    /**
     * Format OpenRosa form list XML response
     */
    public function formatFormListResponse(array $forms): string
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><xforms xmlns="http://openrosa.org/xforms/xformsList"></xforms>');
        
        foreach ($forms as $form) {
            $xform = $xml->addChild('xform');
            
            // Required OpenRosa fields
            $xform->addChild('formID', htmlspecialchars($form['formID']));
            $xform->addChild('name', htmlspecialchars($form['name']));
            $xform->addChild('version', htmlspecialchars($form['version'] ?? '1'));
            $xform->addChild('hash', htmlspecialchars($form['hash']));
            $xform->addChild('downloadUrl', htmlspecialchars($form['downloadUrl']));
            
            // Optional fields
            if (!empty($form['manifestUrl'])) {
                $xform->addChild('manifestUrl', htmlspecialchars($form['manifestUrl']));
            }
            
            if (!empty($form['descriptionText'])) {
                $xform->addChild('descriptionText', htmlspecialchars($form['descriptionText']));
            }
        }

        return $xml->asXML();
    }

    /**
     * Format OpenRosa manifest XML response
     */
    public function formatManifestResponse(array $mediaFiles): string
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><manifest xmlns="http://openrosa.org/xforms/xformsManifest"></manifest>');
        
        foreach ($mediaFiles as $media) {
            $mediaFile = $xml->addChild('mediaFile');
            $mediaFile->addChild('filename', htmlspecialchars($media['filename']));
            $mediaFile->addChild('hash', htmlspecialchars($media['hash']));
            $mediaFile->addChild('downloadUrl', htmlspecialchars($media['downloadUrl']));
        }

        return $xml->asXML();
    }

    /**
     * Format OpenRosa submission response
     */
    public function formatSubmissionResponse(string $submissionId, string $message = 'Submission received successfully'): string
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><OpenRosaResponse xmlns="http://openrosa.org/http/response"></OpenRosaResponse>');
        
        $xml->addChild('message', htmlspecialchars($message));
        
        $metadata = $xml->addChild('submissionMetadata');
        $metadata->addChild('instanceID', htmlspecialchars($submissionId));
        
        return $xml->asXML();
    }

    /**
     * Format error response in OpenRosa format
     */
    public function formatErrorResponse(string $errorMessage, int $statusCode = 400): string
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><OpenRosaResponse xmlns="http://openrosa.org/http/response"></OpenRosaResponse>');
        
        $xml->addChild('message', htmlspecialchars($errorMessage));
        $xml->addChild('statusCode', (string)$statusCode);
        
        return $xml->asXML();
    }

    /**
     * Parse multipart form data for submission
     */
    public function parseMultipartSubmission(): array
    {
        $result = [
            'xml_data' => null,
            'media_files' => [],
            'metadata' => []
        ];

        // Get XML submission data
        $xmlContent = $this->request->getPost('xml_submission_file');
        if ($xmlContent) {
            $result['xml_data'] = $xmlContent;
        }

        // Get uploaded files
        $files = $this->request->getFiles();
        foreach ($files as $fieldName => $file) {
            if ($fieldName !== 'xml_submission_file' && $file->isValid()) {
                $result['media_files'][] = [
                    'field_name' => $fieldName,
                    'file' => $file,
                    'original_name' => $file->getClientName(),
                    'mime_type' => $file->getClientMimeType(),
                    'size' => $file->getSize()
                ];
            }
        }

        // Extract metadata from headers
        $result['metadata'] = [
            'device_id' => $this->request->getHeaderLine('X-Device-ID'),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'content_length' => $this->request->getHeaderLine('Content-Length'),
            'submission_time' => date('Y-m-d H:i:s')
        ];

        return $result;
    }

    /**
     * Validate OpenRosa authentication
     */
    public function validateOpenRosaAuth(): array|false
    {
        $authHeader = $this->request->getHeader('Authorization');
        
        if (!$authHeader) {
            return false;
        }

        $authValue = $authHeader->getValue();
        
        // Handle Basic Authentication
        if (str_starts_with($authValue, 'Basic ')) {
            return $this->parseBasicAuth($authValue);
        }
        
        // Handle Digest Authentication (if implemented)
        if (str_starts_with($authValue, 'Digest ')) {
            return $this->parseDigestAuth($authValue);
        }

        return false;
    }

    /**
     * Parse Basic Authentication header
     */
    private function parseBasicAuth(string $authHeader): array|false
    {
        $credentials = base64_decode(substr($authHeader, 6));
        $parts = explode(':', $credentials, 2);
        
        if (count($parts) !== 2) {
            return false;
        }

        return [
            'type' => 'basic',
            'username' => $parts[0],
            'password' => $parts[1]
        ];
    }

    /**
     * Parse Digest Authentication header
     */
    private function parseDigestAuth(string $authHeader): array|false
    {
        $digestPattern = '/(\w+)=(?:"([^"]+)"|([^,\s]+))/';
        preg_match_all($digestPattern, substr($authHeader, 7), $matches, PREG_SET_ORDER);
        
        $digest = [];
        foreach ($matches as $match) {
            $digest[$match[1]] = $match[2] ?: $match[3];
        }

        // Validate required digest fields
        $requiredFields = ['username', 'realm', 'nonce', 'uri', 'response'];
        foreach ($requiredFields as $field) {
            if (!isset($digest[$field])) {
                return false;
            }
        }

        return [
            'type' => 'digest',
            'username' => $digest['username'],
            'realm' => $digest['realm'],
            'nonce' => $digest['nonce'],
            'uri' => $digest['uri'],
            'response' => $digest['response'],
            'opaque' => $digest['opaque'] ?? null,
            'qop' => $digest['qop'] ?? null,
            'nc' => $digest['nc'] ?? null,
            'cnonce' => $digest['cnonce'] ?? null
        ];
    }

    /**
     * Generate digest authentication challenge
     */
    public function generateDigestChallenge(string $realm = 'Kobo Data Collection'): string
    {
        $nonce = md5(uniqid() . time());
        $opaque = md5($realm);
        
        return 'Digest realm="' . $realm . '", qop="auth", nonce="' . $nonce . '", opaque="' . $opaque . '"';
    }

    /**
     * Validate digest authentication response
     */
    public function validateDigestResponse(array $digest, string $password, string $method = 'POST'): bool
    {
        // Calculate expected response
        $ha1 = md5($digest['username'] . ':' . $digest['realm'] . ':' . $password);
        $ha2 = md5($method . ':' . $digest['uri']);
        
        if (isset($digest['qop']) && $digest['qop'] === 'auth') {
            $expectedResponse = md5($ha1 . ':' . $digest['nonce'] . ':' . $digest['nc'] . ':' . $digest['cnonce'] . ':' . $digest['qop'] . ':' . $ha2);
        } else {
            $expectedResponse = md5($ha1 . ':' . $digest['nonce'] . ':' . $ha2);
        }

        return $expectedResponse === $digest['response'];
    }

    /**
     * Set XML response with proper headers
     */
    public function xmlResponse(string $xmlContent, int $statusCode = 200): ResponseInterface
    {
        return $this->response->setStatusCode($statusCode)
                            ->setContentType('text/xml; charset=utf-8')
                            ->setBody($xmlContent);
    }

    /**
     * Set JSON response with CORS headers
     */
    public function jsonResponse(array $data, int $statusCode = 200): ResponseInterface
    {
        return $this->response->setStatusCode($statusCode)
                            ->setContentType('application/json; charset=utf-8')
                            ->setJSON($data)
                            ->setHeader('Access-Control-Allow-Origin', '*')
                            ->setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
                            ->setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Device-ID');
    }

    /**
     * Handle CORS preflight requests
     */
    public function handleCORSPreflight(): ResponseInterface
    {
        return $this->response->setStatusCode(200)
                            ->setHeader('Access-Control-Allow-Origin', '*')
                            ->setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
                            ->setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Device-ID, X-OpenRosa-Version')
                            ->setHeader('Access-Control-Max-Age', '3600')
                            ->setBody('');
    }

    /**
     * Calculate MD5 hash for form/file
     */
    public function calculateHash(string $content): string
    {
        return 'md5:' . md5($content);
    }

    /**
     * Validate content length
     */
    public function validateContentLength(int $maxSize = 10485760): bool // 10MB default
    {
        $contentLength = $this->request->getHeaderLine('Content-Length');
        
        if (!$contentLength) {
            return false;
        }

        return (int)$contentLength <= $maxSize;
    }

    /**
     * Extract submission metadata from XML
     */
    public function extractSubmissionMetadata(string $xmlContent): array
    {
        $metadata = [
            'instance_id' => null,
            'form_id' => null,
            'version' => null,
            'device_id' => null,
            'start_time' => null,
            'end_time' => null
        ];

        try {
            $xml = simplexml_load_string($xmlContent);
            if ($xml === false) {
                return $metadata;
            }

            // Extract meta information
            if (isset($xml->meta)) {
                $meta = $xml->meta;
                
                $metadata['instance_id'] = (string)($meta->instanceID ?? '');
                $metadata['device_id'] = (string)($meta->deviceID ?? '');
                $metadata['start_time'] = (string)($meta->timeStart ?? '');
                $metadata['end_time'] = (string)($meta->timeEnd ?? '');
            }

            // Extract form ID from root element or attributes
            $metadata['form_id'] = $xml->getName();
            
            $attributes = $xml->attributes();
            if (isset($attributes['id'])) {
                $metadata['form_id'] = (string)$attributes['id'];
            }
            
            if (isset($attributes['version'])) {
                $metadata['version'] = (string)$attributes['version'];
            }

        } catch (\Exception $e) {
            log_message('error', 'Metadata extraction failed: ' . $e->getMessage());
        }

        return $metadata;
    }

    /**
     * Build form download URL
     */
    public function buildFormDownloadUrl(string $formId): string
    {
        return base_url("api/v1/forms/{$formId}");
    }

    /**
     * Build form manifest URL
     */
    public function buildFormManifestUrl(string $formId): string
    {
        return base_url("api/v1/forms/{$formId}/manifest");
    }

    /**
     * Build media file download URL
     */
    public function buildMediaDownloadUrl(int $mediaId): string
    {
        return base_url("api/v1/media/download/{$mediaId}");
    }

    /**
     * Validate OpenRosa version compatibility
     */
    public function validateOpenRosaVersion(): bool
    {
        $version = $this->request->getHeaderLine('X-OpenRosa-Version');
        
        // Accept version 1.0 and compatible versions
        $supportedVersions = ['1.0'];
        
        return in_array($version, $supportedVersions) || empty($version);
    }

    /**
     * Log API request for debugging
     */
    public function logAPIRequest(string $endpoint, array $additionalData = []): void
    {
        $logData = [
            'endpoint' => $endpoint,
            'method' => $this->request->getMethod(),
            'ip_address' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent()->getAgentString(),
            'openrosa_version' => $this->request->getHeaderLine('X-OpenRosa-Version'),
            'content_type' => $this->request->getHeaderLine('Content-Type'),
            'content_length' => $this->request->getHeaderLine('Content-Length'),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $logData = array_merge($logData, $additionalData);

        log_message('info', 'API Request: ' . json_encode($logData));
    }

    /**
     * Generate unique submission ID
     */
    public function generateSubmissionId(): string
    {
        return 'uuid:' . sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Format file size in human readable format
     */
    public function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}