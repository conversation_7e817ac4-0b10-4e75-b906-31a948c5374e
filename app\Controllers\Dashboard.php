<?php

namespace App\Controllers;

class Dashboard extends BaseController
{
    public function index()
    {
        $data = [
            'title' => 'CodiTest - CodeIgniter 4 Testing Platform',
            'appName' => 'CodiTest',
            'appDescription' => 'Modular CodeIgniter 4 Testing & Feature Demonstration Platform',
            'modules' => $this->getTestingModules()
        ];
        
        return view('dashboard/index', $data);
    }
    
    private function getTestingModules()
    {
        return [
            [
                'title' => 'Kobo Collect Integration',
                'description' => 'Mobile data collection system with OpenRosa protocol compliance',
                'icon' => 'fas fa-mobile-alt',
                'url' => base_url('modules/kobo-collect'),
                'color' => 'primary',
                'status' => 'active',
                'features' => [
                    'Form management & distribution',
                    'Data collection from mobile devices',
                    'OpenRosa API compliance',
                    'Real-time synchronization'
                ]
            ],
            [
                'title' => 'Database Testing',
                'description' => 'Test CodeIgniter 4 database features, migrations, and query builder',
                'icon' => 'fas fa-database',
                'url' => base_url('modules/database-testing'),
                'color' => 'success',
                'status' => 'coming_soon',
                'features' => [
                    'Query Builder testing',
                    'Migration management',
                    'Model relationships',
                    'Database performance testing'
                ]
            ],
            [
                'title' => 'Authentication & Authorization',
                'description' => 'Test various authentication methods and permission systems',
                'icon' => 'fas fa-shield-alt',
                'url' => base_url('modules/auth-testing'),
                'color' => 'info',
                'status' => 'coming_soon',
                'features' => [
                    'JWT authentication',
                    'Role-based access control',
                    'Social login integration',
                    'Session management'
                ]
            ],
            [
                'title' => 'API Development',
                'description' => 'RESTful API testing with various response formats and validation',
                'icon' => 'fas fa-code',
                'url' => base_url('modules/api-testing'),
                'color' => 'warning',
                'status' => 'coming_soon',
                'features' => [
                    'REST API endpoints',
                    'JSON/XML responses',
                    'API authentication',
                    'Rate limiting & throttling'
                ]
            ],
            [
                'title' => 'File Upload & Management',
                'description' => 'Test file upload, validation, and storage capabilities',
                'icon' => 'fas fa-upload',
                'url' => base_url('modules/file-testing'),
                'color' => 'danger',
                'status' => 'coming_soon',
                'features' => [
                    'Multiple file uploads',
                    'File validation & security',
                    'Image processing',
                    'Cloud storage integration'
                ]
            ],
            [
                'title' => 'Email & Notifications',
                'description' => 'Test email sending, templates, and notification systems',
                'icon' => 'fas fa-envelope',
                'url' => base_url('modules/email-testing'),
                'color' => 'secondary',
                'status' => 'coming_soon',
                'features' => [
                    'SMTP configuration',
                    'Email templates',
                    'Queue management',
                    'Push notifications'
                ]
            ],
            [
                'title' => 'Caching & Performance',
                'description' => 'Test caching mechanisms and performance optimization features',
                'icon' => 'fas fa-tachometer-alt',
                'url' => base_url('modules/performance-testing'),
                'color' => 'dark',
                'status' => 'coming_soon',
                'features' => [
                    'Redis caching',
                    'Query optimization',
                    'Performance monitoring',
                    'Load testing tools'
                ]
            ],
            [
                'title' => 'Testing & Debugging',
                'description' => 'Unit testing, integration testing, and debugging tools',
                'icon' => 'fas fa-bug',
                'url' => base_url('modules/testing-tools'),
                'color' => 'light',
                'status' => 'coming_soon',
                'features' => [
                    'PHPUnit integration',
                    'Debug toolbar usage',
                    'Log management',
                    'Error handling testing'
                ]
            ]
        ];
    }
}