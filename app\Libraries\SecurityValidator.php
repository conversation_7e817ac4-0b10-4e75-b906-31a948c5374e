<?php

namespace App\Libraries;

use CodeIgniter\HTTP\RequestInterface;

/**
 * Security Validator Library
 * 
 * Handles input validation, sanitization, and security checks
 */
class SecurityValidator
{
    protected $request;
    protected $csrfProtection = true;
    
    public function __construct()
    {
        $this->request = service('request');
    }

    /**
     * Validate and sanitize all input data
     */
    public function validateInput(array $data, array $rules = []): array
    {
        $validated = [];
        
        foreach ($data as $key => $value) {
            // Apply field-specific rules
            $fieldRules = $rules[$key] ?? ['sanitize' => true, 'validate' => true];
            
            // Sanitize the value
            if ($fieldRules['sanitize'] ?? true) {
                $value = $this->sanitizeValue($value);
            }
            
            // Validate the value
            if ($fieldRules['validate'] ?? true) {
                $value = $this->validateValue($key, $value, $fieldRules);
            }
            
            $validated[$key] = $value;
        }
        
        return $validated;
    }

    /**
     * Sanitize individual value
     */
    protected function sanitizeValue($value)
    {
        if (is_array($value)) {
            return array_map([$this, 'sanitizeValue'], $value);
        }
        
        if (!is_string($value)) {
            return $value;
        }
        
        // Remove null bytes
        $value = str_replace("\0", '', $value);
        
        // Trim whitespace
        $value = trim($value);
        
        // Remove control characters except tabs, newlines, and carriage returns
        $value = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $value);
        
        return $value;
    }

    /**
     * Validate individual value
     */
    protected function validateValue(string $field, $value, array $rules)
    {
        // XSS Protection
        if ($rules['xss_protection'] ?? true) {
            $value = $this->preventXSS($value);
        }
        
        // SQL Injection Protection (additional layer)
        if ($rules['sql_protection'] ?? true) {
            $value = $this->preventSQLInjection($value);
        }
        
        // Path Traversal Protection
        if ($rules['path_protection'] ?? true) {
            $value = $this->preventPathTraversal($value);
        }
        
        // Command Injection Protection
        if ($rules['command_protection'] ?? true) {
            $value = $this->preventCommandInjection($value);
        }
        
        return $value;
    }

    /**
     * Prevent XSS attacks
     */
    protected function preventXSS($value): string
    {
        if (!is_string($value)) {
            return $value;
        }
        
        // Remove script tags and their content
        $value = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi', '', $value);
        
        // Remove dangerous attributes
        $dangerousAttributes = [
            'onload', 'onerror', 'onclick', 'onmouseover', 'onmouseout',
            'onkeydown', 'onkeyup', 'onkeypress', 'onfocus', 'onblur',
            'onchange', 'onsubmit', 'onreset', 'onselect', 'onunload',
            'javascript:', 'vbscript:', 'data:'
        ];
        
        foreach ($dangerousAttributes as $attr) {
            $value = preg_replace('/' . preg_quote($attr, '/') . '.*?=/i', '', $value);
        }
        
        // Use htmlspecialchars for additional protection
        return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Prevent SQL injection
     */
    protected function preventSQLInjection($value): string
    {
        if (!is_string($value)) {
            return $value;
        }
        
        // Remove SQL comment sequences
        $value = preg_replace('/(-{2}|#{1}).*?$/m', '', $value);
        $value = preg_replace('/\/\*.*?\*\//', '', $value);
        
        // Remove dangerous SQL keywords (case-insensitive)
        $sqlPatterns = [
            '/\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b/i',
            '/\b(script|javascript|vbscript)\b/i',
            '/[\'";]/i'
        ];
        
        foreach ($sqlPatterns as $pattern) {
            $value = preg_replace($pattern, '', $value);
        }
        
        return $value;
    }

    /**
     * Prevent path traversal attacks
     */
    protected function preventPathTraversal($value): string
    {
        if (!is_string($value)) {
            return $value;
        }
        
        // Remove directory traversal sequences
        $value = str_replace(['../', '..\/', '..\\', '../'], '', $value);
        $value = str_replace(['%2e%2e%2f', '%2e%2e%5c', '%2e%2e/'], '', $value);
        
        // Remove null bytes and other dangerous characters
        $value = str_replace(["\0", "%00"], '', $value);
        
        return $value;
    }

    /**
     * Prevent command injection
     */
    protected function preventCommandInjection($value): string
    {
        if (!is_string($value)) {
            return $value;
        }
        
        // Remove shell metacharacters
        $shellMetaChars = ['|', '&', ';', '$', '`', '(', ')', '<', '>', '\\n', '\\r'];
        $value = str_replace($shellMetaChars, '', $value);
        
        return $value;
    }

    /**
     * Validate file uploads
     */
    public function validateFileUpload(\CodeIgniter\HTTP\Files\UploadedFile $file, array $rules = []): array
    {
        $errors = [];
        
        // Check if file was uploaded
        if (!$file->isValid()) {
            $errors[] = 'File upload failed: ' . $file->getErrorString();
            return ['valid' => false, 'errors' => $errors];
        }
        
        // Validate file size
        $maxSize = $rules['max_size'] ?? 10485760; // 10MB default
        if ($file->getSize() > $maxSize) {
            $errors[] = 'File size exceeds maximum allowed size';
        }
        
        // Validate file type
        $allowedTypes = $rules['allowed_types'] ?? ['xml', 'json'];
        $fileExtension = strtolower($file->getExtension());
        if (!in_array($fileExtension, $allowedTypes)) {
            $errors[] = 'File type not allowed';
        }
        
        // Validate MIME type
        $allowedMimeTypes = $rules['allowed_mime_types'] ?? [
            'text/xml',
            'application/xml',
            'application/json',
            'text/plain'
        ];
        $fileMimeType = $file->getMimeType();
        if (!in_array($fileMimeType, $allowedMimeTypes)) {
            $errors[] = 'MIME type not allowed';
        }
        
        // Check for dangerous file content
        if ($this->containsMaliciousContent($file)) {
            $errors[] = 'File contains potentially malicious content';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'file_info' => [
                'name' => $file->getName(),
                'size' => $file->getSize(),
                'type' => $file->getMimeType(),
                'extension' => $file->getExtension()
            ]
        ];
    }

    /**
     * Check for malicious content in uploaded files
     */
    protected function containsMaliciousContent(\CodeIgniter\HTTP\Files\UploadedFile $file): bool
    {
        $content = file_get_contents($file->getTempName());
        
        // Check for suspicious patterns
        $maliciousPatterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload=/i',
            '/onerror=/i',
            '/<\?php/i',
            '/<%/i'
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Verify CSRF token
     */
    public function verifyCsrfToken(): bool
    {
        if (!$this->csrfProtection) {
            return true;
        }
        
        $csrf = service('security');
        $token = $this->request->getPost(csrf_token()) ?? 
                $this->request->getHeader('X-CSRF-TOKEN');
        
        if (is_object($token)) {
            $token = $token->getValue();
        }
        
        return $csrf->verify($token);
    }

    /**
     * Rate limiting check for security
     */
    public function checkSecurityRateLimit(string $action, string $identifier): bool
    {
        $cache = cache();
        $key = "security_rate_limit_{$action}_{$identifier}";
        $attempts = $cache->get($key) ?: 0;
        
        // Different limits for different actions
        $limits = [
            'login' => 10,        // 10 attempts per hour
            'password_reset' => 5, // 5 attempts per hour
            'file_upload' => 20,   // 20 uploads per hour
            'form_submit' => 100   // 100 submissions per hour
        ];
        
        $limit = $limits[$action] ?? 60;
        
        if ($attempts >= $limit) {
            return false;
        }
        
        // Increment counter
        $cache->save($key, $attempts + 1, 3600);
        
        return true;
    }

    /**
     * Validate JSON input
     */
    public function validateJSON(string $json): array
    {
        $data = json_decode($json, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'valid' => false,
                'error' => 'Invalid JSON: ' . json_last_error_msg()
            ];
        }
        
        // Check for dangerous JSON patterns
        if ($this->containsDangerousJSON($json)) {
            return [
                'valid' => false,
                'error' => 'JSON contains potentially dangerous content'
            ];
        }
        
        return [
            'valid' => true,
            'data' => $data
        ];
    }

    /**
     * Check for dangerous patterns in JSON
     */
    protected function containsDangerousJSON(string $json): bool
    {
        $dangerousPatterns = [
            '/__proto__/',
            '/constructor/',
            '/prototype/',
            '/javascript:/i',
            '/<script/i'
        ];
        
        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $json)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Validate XML input
     */
    public function validateXML(string $xml): array
    {
        // Disable external entity loading for security
        $previousValue = libxml_disable_entity_loader(true);
        
        try {
            $dom = new \DOMDocument();
            $dom->substituteEntities = false;
            $dom->resolveExternals = false;
            
            // Load XML with error handling
            libxml_use_internal_errors(true);
            $success = $dom->loadXML($xml);
            $errors = libxml_get_errors();
            libxml_clear_errors();
            
            if (!$success) {
                $errorMessages = array_map(function($error) {
                    return $error->message;
                }, $errors);
                
                return [
                    'valid' => false,
                    'errors' => $errorMessages
                ];
            }
            
            // Check for dangerous XML content
            if ($this->containsDangerousXML($xml)) {
                return [
                    'valid' => false,
                    'errors' => ['XML contains potentially dangerous content']
                ];
            }
            
            return [
                'valid' => true,
                'dom' => $dom
            ];
            
        } finally {
            libxml_disable_entity_loader($previousValue);
        }
    }

    /**
     * Check for dangerous patterns in XML
     */
    protected function containsDangerousXML(string $xml): bool
    {
        $dangerousPatterns = [
            '/<!ENTITY/i',
            '/SYSTEM/i',
            '/PUBLIC/i',
            '/<script/i',
            '/javascript:/i'
        ];
        
        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $xml)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Generate secure random token
     */
    public function generateSecureToken(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * Validate password strength
     */
    public function validatePasswordStrength(string $password): array
    {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        }
        
        return $errors;
    }

    /**
     * Disable CSRF protection (for testing or specific endpoints)
     */
    public function disableCSRFProtection(): self
    {
        $this->csrfProtection = false;
        return $this;
    }
}