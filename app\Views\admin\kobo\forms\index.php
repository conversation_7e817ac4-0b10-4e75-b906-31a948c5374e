<?= $this->extend('admin/layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2 fw-bold">
        <i class="bi bi-file-earmark-text text-primary me-2"></i>
        Form Management
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button class="btn btn-sm btn-outline-secondary" id="refreshForms">
                <i class="bi bi-arrow-clockwise me-1"></i>
                Refresh
            </button>
            <button class="btn btn-sm btn-outline-primary" onclick="exportForms()">
                <i class="bi bi-download me-1"></i>
                Export
            </button>
        </div>
        <a href="<?= base_url('admin/kobo/forms/create') ?>" class="btn btn-sm btn-primary">
            <i class="bi bi-plus-circle me-1"></i>
            Upload New Form
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card primary h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Total Forms</div>
                        <div class="h3 fw-bold"><?= $stats['total_forms'] ?? 0 ?></div>
                    </div>
                    <i class="bi bi-file-earmark-text align-self-center" style="font-size: 2rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card success h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Active Forms</div>
                        <div class="h3 fw-bold"><?= $stats['status_counts']['active'] ?? 0 ?></div>
                    </div>
                    <i class="bi bi-check-circle align-self-center" style="font-size: 2rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card warning h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Draft Forms</div>
                        <div class="h3 fw-bold"><?= $stats['status_counts']['draft'] ?? 0 ?></div>
                    </div>
                    <i class="bi bi-pencil-square align-self-center" style="font-size: 2rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card info h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="text-white-50 small">Total Submissions</div>
                        <div class="h3 fw-bold"><?= $stats['total_submissions'] ?? 0 ?></div>
                    </div>
                    <i class="bi bi-database align-self-center" style="font-size: 2rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3" id="filterForm">
            <div class="col-md-4">
                <label for="search" class="form-label">Search Forms</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= $search ?? '' ?>" placeholder="Search by name or ID...">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Status</option>
                    <option value="active" <?= ($filters['status'] ?? '') === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= ($filters['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                    <option value="draft" <?= ($filters['status'] ?? '') === 'draft' ? 'selected' : '' ?>>Draft</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="created_by" class="form-label">Created By</label>
                <select class="form-select" id="created_by" name="created_by">
                    <option value="">All Users</option>
                    <?php foreach ($users ?? [] as $user): ?>
                        <option value="<?= $user['id'] ?>" <?= ($filters['created_by'] ?? '') == $user['id'] ? 'selected' : '' ?>>
                            <?= esc($user['username']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>
                        Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Forms Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            Forms List
        </h5>
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-secondary" onclick="selectAll()">
                <i class="bi bi-check-all me-1"></i>
                Select All
            </button>
            <button class="btn btn-outline-danger" onclick="bulkAction('delete')" id="bulkDeleteBtn" style="display: none;">
                <i class="bi bi-trash me-1"></i>
                Delete Selected
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($forms)): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                            </th>
                            <th>Form Name</th>
                            <th>Form ID</th>
                            <th>Version</th>
                            <th>Status</th>
                            <th>Created By</th>
                            <th>Submissions</th>
                            <th>Created</th>
                            <th width="150">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($forms as $form): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input row-select" value="<?= $form['id'] ?>">
                                </td>
                                <td>
                                    <div class="fw-bold"><?= esc($form['form_name']) ?></div>
                                    <?php if (!empty($form['description'])): ?>
                                        <small class="text-muted"><?= esc($form['description']) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <code class="small"><?= esc($form['form_id']) ?></code>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= esc($form['version']) ?></span>
                                </td>
                                <td>
                                    <?php
                                    $statusColors = [
                                        'active' => 'success',
                                        'inactive' => 'danger',
                                        'draft' => 'warning'
                                    ];
                                    $statusColor = $statusColors[$form['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?= $statusColor ?>"><?= ucfirst($form['status']) ?></span>
                                </td>
                                <td>
                                    <small><?= esc($form['created_by_name'] ?? 'Unknown') ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?= $form['submission_count'] ?? 0 ?></span>
                                </td>
                                <td>
                                    <small><?= date('M j, Y', strtotime($form['created_at'])) ?></small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm table-actions">
                                        <button class="btn btn-outline-primary btn-sm" 
                                                onclick="viewForm(<?= $form['id'] ?>)" 
                                                title="View">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" 
                                                onclick="downloadForm('<?= $form['form_id'] ?>')" 
                                                title="Download">
                                            <i class="bi bi-download"></i>
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" 
                                                onclick="editForm(<?= $form['id'] ?>)" 
                                                title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" 
                                                onclick="cloneForm(<?= $form['id'] ?>)" 
                                                title="Clone">
                                            <i class="bi bi-files"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteForm(<?= $form['id'] ?>)" 
                                                title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="bi bi-file-earmark-text" style="font-size: 4rem; color: #dee2e6;"></i>
                <h5 class="mt-3 text-muted">No Forms Found</h5>
                <p class="text-muted">Upload your first form to get started with data collection.</p>
                <a href="<?= base_url('admin/kobo/forms/create') ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    Upload Form
                </a>
            </div>
        <?php endif; ?>
    </div>
    
    <?php if (isset($pager)): ?>
        <div class="card-footer">
            <?= $pager->links() ?>
        </div>
    <?php endif; ?>
</div>

<!-- Form Preview Modal -->
<div class="modal fade" id="formPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-eye me-2"></i>
                    Form Preview
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="formPreviewContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="downloadFromPreview">
                    <i class="bi bi-download me-2"></i>
                    Download XML
                </button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Selection management
    let selectedForms = [];

    document.addEventListener('DOMContentLoaded', function() {
        initializeTableSelection();
        initializeFormActions();
    });

    function initializeTableSelection() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const rowCheckboxes = document.querySelectorAll('.row-select');

        selectAllCheckbox?.addEventListener('change', function() {
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedForms();
        });

        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedForms);
        });
    }

    function updateSelectedForms() {
        const checkboxes = document.querySelectorAll('.row-select:checked');
        selectedForms = Array.from(checkboxes).map(cb => parseInt(cb.value));
        
        const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
        if (selectedForms.length > 0) {
            bulkDeleteBtn.style.display = 'block';
            bulkDeleteBtn.innerHTML = `<i class="bi bi-trash me-1"></i>Delete Selected (${selectedForms.length})`;
        } else {
            bulkDeleteBtn.style.display = 'none';
        }
    }

    function selectAll() {
        document.getElementById('selectAllCheckbox').checked = true;
        document.getElementById('selectAllCheckbox').dispatchEvent(new Event('change'));
    }

    function initializeFormActions() {
        // Refresh forms
        document.getElementById('refreshForms').addEventListener('click', function() {
            window.location.reload();
        });

        // Auto-submit filter form on change
        document.getElementById('status').addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });

        document.getElementById('created_by').addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    }

    // Form actions
    function viewForm(formId) {
        const modal = new bootstrap.Modal(document.getElementById('formPreviewModal'));
        
        // Load form preview
        ajaxRequest(`<?= base_url('admin/kobo/forms') ?>/${formId}/preview`, 'GET', null,
            function(data) {
                if (data.success) {
                    document.getElementById('formPreviewContent').innerHTML = data.preview;
                    document.getElementById('downloadFromPreview').onclick = () => downloadForm(data.form_id);
                } else {
                    document.getElementById('formPreviewContent').innerHTML = 
                        '<div class="alert alert-danger">Failed to load form preview</div>';
                }
            },
            function(error) {
                document.getElementById('formPreviewContent').innerHTML = 
                    '<div class="alert alert-danger">Error loading form preview</div>';
            }
        );
        
        modal.show();
    }

    function downloadForm(formId) {
        window.open(`<?= base_url('api/v1/forms') ?>/${formId}`, '_blank');
    }

    function editForm(formId) {
        window.location.href = `<?= base_url('admin/kobo/forms') ?>/${formId}/edit`;
    }

    function cloneForm(formId) {
        if (confirm('Are you sure you want to clone this form?')) {
            ajaxRequest(`<?= base_url('admin/kobo/forms') ?>/${formId}/clone`, 'POST', null,
                function(data) {
                    if (data.success) {
                        showToast('Form cloned successfully', 'success');
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showToast('Failed to clone form: ' + (data.message || 'Unknown error'), 'danger');
                    }
                },
                function(error) {
                    showToast('Clone request failed', 'danger');
                }
            );
        }
    }

    function deleteForm(formId) {
        if (confirm('Are you sure you want to delete this form? This action cannot be undone.')) {
            ajaxRequest(`<?= base_url('admin/kobo/forms') ?>/${formId}`, 'DELETE', null,
                function(data) {
                    if (data.success) {
                        showToast('Form deleted successfully', 'success');
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showToast('Failed to delete form: ' + (data.message || 'Unknown error'), 'danger');
                    }
                },
                function(error) {
                    showToast('Delete request failed', 'danger');
                }
            );
        }
    }

    function bulkAction(action) {
        if (selectedForms.length === 0) {
            showToast('Please select forms first', 'warning');
            return;
        }

        let confirmMessage = '';
        switch(action) {
            case 'delete':
                confirmMessage = `Are you sure you want to delete ${selectedForms.length} selected forms? This action cannot be undone.`;
                break;
            default:
                showToast('Unknown bulk action', 'danger');
                return;
        }

        if (confirm(confirmMessage)) {
            ajaxRequest('<?= base_url('admin/kobo/forms/bulk') ?>', 'POST', {
                action: action,
                form_ids: selectedForms
            },
            function(data) {
                if (data.success) {
                    showToast(data.message, 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast('Bulk operation failed: ' + (data.message || 'Unknown error'), 'danger');
                }
            },
            function(error) {
                showToast('Bulk operation request failed', 'danger');
            });
        }
    }

    function exportForms() {
        const filters = new URLSearchParams(window.location.search);
        window.open(`<?= base_url('admin/kobo/forms/export') ?>?${filters.toString()}`, '_blank');
    }
</script>
<?= $this->endSection() ?>