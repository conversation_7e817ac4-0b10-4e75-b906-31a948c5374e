<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Libraries\AccessControlManager;

/**
 * Access Control Filter
 * 
 * Handles role-based access control for protected routes
 */
class AccessControlFilter implements FilterInterface
{
    protected $accessManager;

    public function __construct()
    {
        $this->accessManager = new AccessControlManager();
    }

    /**
     * Before method - check access permissions
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Get authenticated user from JWT filter
        if (!isset($request->user_id)) {
            return $this->respondUnauthorized('Authentication required');
        }

        $userId = $request->user_id;
        $userRole = $request->user_role ?? 'viewer';
        
        // Get required permission from route or arguments
        $requiredPermission = $this->getRequiredPermission($request, $arguments);
        
        if (!$requiredPermission) {
            // No specific permission required
            return;
        }

        // Build context for permission check
        $context = $this->buildPermissionContext($request);
        
        // Check if user has required permission
        if (!$this->accessManager->hasPermission($userId, $requiredPermission, $context)) {
            $this->logAccessDenied($userId, $requiredPermission, $context);
            return $this->respondForbidden('Access denied - insufficient permissions');
        }

        // Log successful access
        $this->logAccessGranted($userId, $requiredPermission, $context);
    }

    /**
     * After method - cleanup if needed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Could add additional logging or cleanup here
    }

    /**
     * Determine required permission based on route
     */
    protected function getRequiredPermission(RequestInterface $request, ?array $arguments = null): ?string
    {
        // Check if permission is explicitly provided in arguments
        if ($arguments && isset($arguments['permission'])) {
            return $arguments['permission'];
        }

        // Auto-determine permission based on route and method
        $uri = $request->getUri();
        $path = trim($uri->getPath(), '/');
        $method = strtoupper($request->getMethod());

        return $this->mapRouteToPermission($path, $method);
    }

    /**
     * Map route and HTTP method to permission
     */
    protected function mapRouteToPermission(string $path, string $method): ?string
    {
        // Define route permission mappings
        $routePermissions = [
            // API routes
            'api/v1/forms' => [
                'GET' => 'forms.read',
                'POST' => 'forms.create'
            ],
            'api/v1/forms/{id}' => [
                'GET' => 'forms.read',
                'PUT' => 'forms.update',
                'DELETE' => 'forms.delete'
            ],
            'api/v1/submission' => [
                'POST' => 'submissions.create'
            ],
            'api/v1/submissions' => [
                'GET' => 'submissions.read'
            ],
            'api/v1/submissions/{id}' => [
                'GET' => 'submissions.read',
                'PUT' => 'submissions.update',
                'DELETE' => 'submissions.delete'
            ],
            
            // Admin routes
            'admin/kobo/dashboard' => [
                'GET' => 'system.logs'
            ],
            'admin/kobo/forms' => [
                'GET' => 'forms.read',
                'POST' => 'forms.create'
            ],
            'admin/kobo/forms/{id}' => [
                'GET' => 'forms.read',
                'PUT' => 'forms.update',
                'DELETE' => 'forms.delete'
            ],
            'admin/kobo/users' => [
                'GET' => 'users.read',
                'POST' => 'users.create'
            ],
            'admin/kobo/users/{id}' => [
                'GET' => 'users.read',
                'PUT' => 'users.update',
                'DELETE' => 'users.delete'
            ],
            'admin/kobo/submissions' => [
                'GET' => 'submissions.read'
            ]
        ];

        // First try exact match
        if (isset($routePermissions[$path][$method])) {
            return $routePermissions[$path][$method];
        }

        // Try pattern matching for routes with parameters
        foreach ($routePermissions as $pattern => $methods) {
            if ($this->matchRoutePattern($path, $pattern) && isset($methods[$method])) {
                return $methods[$method];
            }
        }

        // Default permissions based on method
        $defaultPermissions = [
            'GET' => null,    // No permission needed for read-only operations by default
            'POST' => 'api.access',
            'PUT' => 'api.access',
            'PATCH' => 'api.access',
            'DELETE' => 'api.admin'
        ];

        return $defaultPermissions[$method] ?? null;
    }

    /**
     * Match route pattern with parameters
     */
    protected function matchRoutePattern(string $path, string $pattern): bool
    {
        // Convert pattern to regex
        $regex = str_replace(
            ['{id}', '{form_id}', '{user_id}', '{submission_id}'],
            ['[^/]+', '[^/]+', '[^/]+', '[^/]+'],
            $pattern
        );
        
        $regex = '#^' . $regex . '$#';
        
        return preg_match($regex, $path);
    }

    /**
     * Build context for permission checking
     */
    protected function buildPermissionContext(RequestInterface $request): array
    {
        $context = [
            'ip_address' => $request->getIPAddress(),
            'user_agent' => $request->getUserAgent(),
            'current_time' => date('Y-m-d H:i:s')
        ];

        // Extract IDs from route parameters
        $uri = $request->getUri();
        $segments = explode('/', trim($uri->getPath(), '/'));
        
        // Look for form_id in the path
        if (in_array('forms', $segments)) {
            $formIndex = array_search('forms', $segments);
            if (isset($segments[$formIndex + 1]) && is_numeric($segments[$formIndex + 1])) {
                $context['form_id'] = $segments[$formIndex + 1];
            }
        }

        // Look for submission_id in the path
        if (in_array('submissions', $segments)) {
            $submissionIndex = array_search('submissions', $segments);
            if (isset($segments[$submissionIndex + 1]) && is_numeric($segments[$submissionIndex + 1])) {
                $context['submission_id'] = $segments[$submissionIndex + 1];
            }
        }

        // Get device_id from headers or request data
        $deviceId = $request->getHeaderLine('X-Device-ID') ?: $request->getPost('device_id');
        if ($deviceId) {
            $context['device_id'] = $deviceId;
        }

        return $context;
    }

    /**
     * Log access granted event
     */
    protected function logAccessGranted(int $userId, string $permission, array $context): void
    {
        log_message('info', "Access granted - User: {$userId}, Permission: {$permission}");
    }

    /**
     * Log access denied event
     */
    protected function logAccessDenied(int $userId, string $permission, array $context): void
    {
        log_message('warning', "Access denied - User: {$userId}, Permission: {$permission}, Context: " . json_encode($context));
        
        // Could also trigger security alerts for multiple denied attempts
        $this->checkForSuspiciousActivity($userId, $permission);
    }

    /**
     * Check for suspicious activity patterns
     */
    protected function checkForSuspiciousActivity(int $userId, string $permission): void
    {
        $cache = cache();
        $key = "access_denied_{$userId}";
        $attempts = $cache->get($key) ?: 0;
        
        $attempts++;
        $cache->save($key, $attempts, 3600); // 1 hour window
        
        if ($attempts >= 5) {
            // Multiple access denied attempts - potential security threat
            log_message('critical', "Multiple access denied attempts detected for user {$userId}");
            
            // Could trigger additional security measures:
            // - Temporary account suspension
            // - Security alert notifications
            // - Enhanced monitoring
        }
    }

    /**
     * Return 401 Unauthorized response
     */
    protected function respondUnauthorized(string $message = 'Unauthorized'): ResponseInterface
    {
        $response = service('response');
        return $response->setStatusCode(401)
                       ->setJSON([
                           'success' => false,
                           'message' => $message,
                           'error_code' => 'UNAUTHORIZED'
                       ]);
    }

    /**
     * Return 403 Forbidden response
     */
    protected function respondForbidden(string $message = 'Forbidden'): ResponseInterface
    {
        $response = service('response');
        return $response->setStatusCode(403)
                       ->setJSON([
                           'success' => false,
                           'message' => $message,
                           'error_code' => 'FORBIDDEN'
                       ]);
    }
}