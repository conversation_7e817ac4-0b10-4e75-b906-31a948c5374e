<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Rate Limiting Filter
 * 
 * Implements rate limiting and brute force protection for API endpoints
 */
class RateLimitFilter implements FilterInterface
{
    protected $cache;
    protected $limits = [
        'default' => [
            'requests' => 60,  // requests per window
            'window' => 3600   // window in seconds (1 hour)
        ],
        'auth' => [
            'requests' => 10,  // requests per window
            'window' => 900    // window in seconds (15 minutes)
        ],
        'submit' => [
            'requests' => 100, // requests per window
            'window' => 3600   // window in seconds (1 hour)
        ]
    ];

    public function __construct()
    {
        $this->cache = cache();
    }

    /**
     * Before method - check rate limits
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $identifier = $this->getClientIdentifier($request);
        $endpoint = $this->getEndpointType($request);
        
        // Get rate limit configuration for this endpoint
        $config = $this->getRateLimitConfig($endpoint, $arguments);
        
        // Check if client is rate limited
        if ($this->isRateLimited($identifier, $endpoint, $config)) {
            return $this->respondRateLimited($config);
        }

        // Increment request counter
        $this->incrementRequestCount($identifier, $endpoint, $config);
    }

    /**
     * After method - add rate limit headers
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        $identifier = $this->getClientIdentifier($request);
        $endpoint = $this->getEndpointType($request);
        $config = $this->getRateLimitConfig($endpoint, $arguments);
        
        // Add rate limit headers to response
        $this->addRateLimitHeaders($response, $identifier, $endpoint, $config);
    }

    /**
     * Get client identifier for rate limiting
     */
    protected function getClientIdentifier(RequestInterface $request): string
    {
        // Use IP address as primary identifier
        $ip = $request->getIPAddress();
        
        // If authenticated, also consider user ID for more granular control
        $userId = null;
        if (isset($request->user_id)) {
            $userId = $request->user_id;
        }

        return $userId ? "user_{$userId}_{$ip}" : "ip_{$ip}";
    }

    /**
     * Determine endpoint type for different rate limits
     */
    protected function getEndpointType(RequestInterface $request): string
    {
        $uri = $request->getUri();
        $path = trim($uri->getPath(), '/');

        // Authentication endpoints (stricter limits)
        if (strpos($path, 'api/v1/auth') !== false) {
            return 'auth';
        }

        // Data submission endpoints
        if (strpos($path, 'api/v1/submission') !== false) {
            return 'submit';
        }

        // Form download endpoints
        if (strpos($path, 'api/v1/forms') !== false) {
            return 'forms';
        }

        return 'default';
    }

    /**
     * Get rate limit configuration
     */
    protected function getRateLimitConfig(string $endpoint, ?array $arguments = null): array
    {
        // Custom limits can be passed via arguments
        if ($arguments && isset($arguments['requests'], $arguments['window'])) {
            return [
                'requests' => (int)$arguments['requests'],
                'window' => (int)$arguments['window']
            ];
        }

        // Return predefined limits
        return $this->limits[$endpoint] ?? $this->limits['default'];
    }

    /**
     * Check if client is rate limited
     */
    protected function isRateLimited(string $identifier, string $endpoint, array $config): bool
    {
        $key = "rate_limit_{$endpoint}_{$identifier}";
        $currentCount = $this->cache->get($key) ?: 0;
        
        return $currentCount >= $config['requests'];
    }

    /**
     * Increment request count for client
     */
    protected function incrementRequestCount(string $identifier, string $endpoint, array $config): void
    {
        $key = "rate_limit_{$endpoint}_{$identifier}";
        $currentCount = $this->cache->get($key) ?: 0;
        
        $this->cache->save($key, $currentCount + 1, $config['window']);
    }

    /**
     * Add rate limit headers to response
     */
    protected function addRateLimitHeaders(ResponseInterface $response, string $identifier, string $endpoint, array $config): void
    {
        $key = "rate_limit_{$endpoint}_{$identifier}";
        $currentCount = $this->cache->get($key) ?: 0;
        $remaining = max(0, $config['requests'] - $currentCount);
        
        // Calculate reset time
        $resetTime = time() + $config['window'];
        
        $response->setHeader('X-RateLimit-Limit', (string)$config['requests']);
        $response->setHeader('X-RateLimit-Remaining', (string)$remaining);
        $response->setHeader('X-RateLimit-Reset', (string)$resetTime);
        $response->setHeader('X-RateLimit-Window', (string)$config['window']);
    }

    /**
     * Return rate limit exceeded response
     */
    protected function respondRateLimited(array $config): ResponseInterface
    {
        $response = service('response');
        
        $retryAfter = $config['window'];
        
        return $response->setStatusCode(429)
                       ->setHeader('Retry-After', (string)$retryAfter)
                       ->setJSON([
                           'success' => false,
                           'message' => 'Rate limit exceeded',
                           'error_code' => 'RATE_LIMITED',
                           'details' => [
                               'limit' => $config['requests'],
                               'window' => $config['window'],
                               'retry_after' => $retryAfter
                           ]
                       ]);
    }

    /**
     * Check for brute force attacks (multiple failed attempts)
     */
    public function checkBruteForce(string $identifier, string $action = 'login'): bool
    {
        $key = "brute_force_{$action}_{$identifier}";
        $attempts = $this->cache->get($key) ?: 0;
        
        // Block after 5 failed attempts within 15 minutes
        return $attempts >= 5;
    }

    /**
     * Record failed attempt for brute force detection
     */
    public function recordFailedAttempt(string $identifier, string $action = 'login'): void
    {
        $key = "brute_force_{$action}_{$identifier}";
        $attempts = $this->cache->get($key) ?: 0;
        
        // Increment attempts and set 15-minute expiry
        $this->cache->save($key, $attempts + 1, 900);
    }

    /**
     * Clear failed attempts (on successful authentication)
     */
    public function clearFailedAttempts(string $identifier, string $action = 'login'): void
    {
        $key = "brute_force_{$action}_{$identifier}";
        $this->cache->delete($key);
    }

    /**
     * Get current rate limit status for a client
     */
    public function getRateLimitStatus(string $identifier, string $endpoint): array
    {
        $config = $this->limits[$endpoint] ?? $this->limits['default'];
        $key = "rate_limit_{$endpoint}_{$identifier}";
        $currentCount = $this->cache->get($key) ?: 0;
        $remaining = max(0, $config['requests'] - $currentCount);
        
        return [
            'limit' => $config['requests'],
            'used' => $currentCount,
            'remaining' => $remaining,
            'window' => $config['window'],
            'reset_time' => time() + $config['window']
        ];
    }

    /**
     * Whitelist IP addresses (bypass rate limiting)
     */
    protected function isWhitelisted(string $ip): bool
    {
        $whitelist = [
            '127.0.0.1',
            '::1'
        ];

        // Add environment-specific whitelist
        $envWhitelist = env('RATE_LIMIT_WHITELIST', '');
        if ($envWhitelist) {
            $whitelist = array_merge($whitelist, explode(',', $envWhitelist));
        }

        return in_array($ip, $whitelist);
    }

    /**
     * Dynamic rate limit adjustment based on server load
     */
    protected function adjustLimitsForServerLoad(): void
    {
        // Check server metrics (CPU, memory, etc.)
        $load = sys_getloadavg()[0]; // 1-minute load average
        
        if ($load > 2.0) {
            // Reduce limits under high load
            foreach ($this->limits as &$limit) {
                $limit['requests'] = (int)($limit['requests'] * 0.7);
            }
        }
    }

    /**
     * Clean up expired rate limit entries
     */
    public function cleanupExpiredEntries(): void
    {
        // This would typically be run as a scheduled task
        // For cache-based storage, entries expire automatically
        // For database storage, you'd need to delete expired entries
    }
}