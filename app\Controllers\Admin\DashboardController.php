<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Kobo\KoboUserModel;
use App\Models\Kobo\KoboFormModel;
use App\Models\Kobo\KoboSubmissionModel;
use App\Models\Kobo\KoboAuditLogModel;
use App\Models\Kobo\KoboMediaFileModel;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Admin Dashboard Controller
 * 
 * Handles admin panel dashboard with system overview,
 * statistics, metrics, and real-time activity monitoring
 */
class DashboardController extends BaseController
{
    protected $userModel;
    protected $formModel;
    protected $submissionModel;
    protected $auditModel;
    protected $mediaModel;

    public function __construct()
    {
        $this->userModel = new KoboUserModel();
        $this->formModel = new KoboFormModel();
        $this->submissionModel = new KoboSubmissionModel();
        $this->auditModel = new KoboAuditLogModel();
        $this->mediaModel = new KoboMediaFileModel();
    }

    /**
     * Admin dashboard index
     */
    public function index(): string
    {
        // Check admin authentication
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login')->with('error', 'Admin access required');
        }

        try {
            // Gather dashboard statistics
            $data = [
                'title' => 'Kobo Admin Dashboard - CodiTest',
                'stats' => $this->getDashboardStats(),
                'recent_activities' => $this->getRecentActivities(),
                'system_health' => $this->getSystemHealth(),
                'charts_data' => $this->getChartsData(),
                'alerts' => $this->getSystemAlerts()
            ];

            return view('admin/kobo/dashboard/index', $data);

        } catch (\Exception $e) {
            log_message('error', 'Admin dashboard error: ' . $e->getMessage());
            return view('admin/dashboard/error', [
                'error' => 'Unable to load dashboard data'
            ]);
        }
    }

    /**
     * JSON API Export interface
     */
    public function exportApi(): string
    {
        // Check admin authentication
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login')->with('error', 'Admin access required');
        }

        $data = [
            'title' => 'JSON API Export - CodiTest Admin'
        ];

        return view('admin/export/api', $data);
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats(): array
    {
        try {
            // User statistics
            $userStats = $this->userModel->getUserStats();
            
            // Form statistics
            $formStats = $this->formModel->getFormStats();
            
            // Submission statistics
            $submissionStats = $this->submissionModel->getSubmissionStats();
            
            // Media file statistics
            $mediaStats = $this->mediaModel->getFileStats();
            
            // Activity statistics (last 30 days)
            $activityStats = $this->auditModel->getActivityStats('30 days');

            return [
                'users' => [
                    'total' => $userStats['total'],
                    'active' => $userStats['active'],
                    'inactive' => $userStats['inactive'],
                    'by_role' => $userStats['by_role']
                ],
                'forms' => [
                    'total' => $formStats['total_forms'],
                    'active' => $formStats['status_counts']['active'] ?? 0,
                    'draft' => $formStats['status_counts']['draft'] ?? 0,
                    'inactive' => $formStats['status_counts']['inactive'] ?? 0
                ],
                'submissions' => [
                    'total' => $submissionStats['grand_total'],
                    'pending' => $submissionStats['totals']['pending'] ?? 0,
                    'processed' => $submissionStats['totals']['processed'] ?? 0,
                    'failed' => $submissionStats['totals']['failed'] ?? 0
                ],
                'media' => [
                    'total_files' => $mediaStats['total_active_files'],
                    'total_size' => $mediaStats['totals']['total_size'] ?? 0,
                    'average_size' => $mediaStats['totals']['average_size'] ?? 0
                ],
                'activity' => [
                    'total_activities' => count($activityStats['daily_activities']),
                    'unique_users' => array_sum(array_column($activityStats['daily_activities'], 'unique_users')),
                    'top_users' => $activityStats['top_users']
                ]
            ];

        } catch (\Exception $e) {
            log_message('error', 'Dashboard stats error: ' . $e->getMessage());
            return $this->getDefaultStats();
        }
    }

    /**
     * Get recent activities for timeline
     */
    private function getRecentActivities(): array
    {
        try {
            return $this->auditModel->getActivityTimeline(15);
        } catch (\Exception $e) {
            log_message('error', 'Recent activities error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get system health indicators
     */
    private function getSystemHealth(): array
    {
        $health = [
            'status' => 'healthy',
            'database' => 'connected',
            'storage' => 'available',
            'api' => 'operational',
            'uptime' => $this->calculateUptime()
        ];

        try {
            // Test database connection
            $db = \Config\Database::connect();
            $db->query('SELECT 1');
            
            // Check writable directory
            if (!is_writable(WRITEPATH)) {
                $health['storage'] = 'error';
                $health['status'] = 'warning';
            }

            // Check for recent errors
            $recentErrors = $this->auditModel->getSecurityEvents(5);
            if (count($recentErrors) > 10) {
                $health['status'] = 'warning';
            }

        } catch (\Exception $e) {
            $health['database'] = 'error';
            $health['status'] = 'critical';
            log_message('error', 'Health check error: ' . $e->getMessage());
        }

        return $health;
    }

    /**
     * Get data for dashboard charts
     */
    private function getChartsData(): array
    {
        try {
            // Submissions over time (last 30 days)
            $submissionsChart = $this->getSubmissionsChartData();
            
            // User activity chart
            $activityChart = $this->getActivityChartData();
            
            // Forms distribution chart
            $formsChart = $this->getFormsChartData();

            return [
                'submissions_timeline' => $submissionsChart,
                'user_activity' => $activityChart,
                'forms_distribution' => $formsChart
            ];

        } catch (\Exception $e) {
            log_message('error', 'Charts data error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get submissions chart data
     */
    private function getSubmissionsChartData(): array
    {
        $data = [];
        $labels = [];
        
        // Get data for last 30 days
        for ($i = 29; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $labels[] = date('M j', strtotime($date));
            
            // Count submissions for this date
            $count = $this->submissionModel->where('DATE(submitted_at)', $date)->countAllResults();
            $data[] = $count;
        }

        return [
            'labels' => $labels,
            'data' => $data
        ];
    }

    /**
     * Get activity chart data
     */
    private function getActivityChartData(): array
    {
        $activityStats = $this->auditModel->getActivityStats('7 days');
        
        $labels = [];
        $data = [];
        
        foreach ($activityStats['daily_activities'] as $day) {
            $labels[] = date('M j', strtotime($day['activity_date']));
            $data[] = $day['total_activities'];
        }

        return [
            'labels' => array_reverse($labels),
            'data' => array_reverse($data)
        ];
    }

    /**
     * Get forms distribution chart data
     */
    private function getFormsChartData(): array
    {
        $formStats = $this->formModel->getFormStats();
        
        return [
            'labels' => ['Active', 'Draft', 'Inactive'],
            'data' => [
                $formStats['status_counts']['active'] ?? 0,
                $formStats['status_counts']['draft'] ?? 0,
                $formStats['status_counts']['inactive'] ?? 0
            ]
        ];
    }

    /**
     * Get system alerts and notifications
     */
    private function getSystemAlerts(): array
    {
        $alerts = [];

        try {
            // Check for failed submissions
            $failedSubmissions = $this->submissionModel->getSubmissionsByStatus('failed');
            if (count($failedSubmissions) > 0) {
                $alerts[] = [
                    'type' => 'warning',
                    'message' => count($failedSubmissions) . ' submissions failed processing',
                    'action' => '/admin/submissions?status=failed'
                ];
            }

            // Check for inactive users with recent activity
            $inactiveUsers = $this->userModel->where('status', 'inactive')->countAllResults();
            if ($inactiveUsers > 0) {
                $alerts[] = [
                    'type' => 'info',
                    'message' => $inactiveUsers . ' users are currently inactive',
                    'action' => '/admin/users?status=inactive'
                ];
            }

            // Check for storage usage
            $totalSize = $this->mediaModel->getFileStats()['totals']['total_size'] ?? 0;
            if ($totalSize > 100 * 1024 * 1024) { // 100MB
                $alerts[] = [
                    'type' => 'warning',
                    'message' => 'High storage usage detected: ' . $this->formatFileSize($totalSize),
                    'action' => '/admin/media'
                ];
            }

            // Check for security events
            $securityEvents = $this->auditModel->getFailedLoginAttempts('24 hours');
            if (count($securityEvents) > 5) {
                $alerts[] = [
                    'type' => 'danger',
                    'message' => count($securityEvents) . ' failed login attempts in last 24 hours',
                    'action' => '/admin/security'
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'System alerts error: ' . $e->getMessage());
        }

        return $alerts;
    }

    /**
     * Get real-time data via AJAX
     */
    public function realTimeData(): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            return $this->response->setStatusCode(401)->setJSON(['error' => 'Unauthorized']);
        }

        try {
            $data = [
                'stats' => $this->getDashboardStats(),
                'recent_activities' => $this->getRecentActivities(5),
                'system_health' => $this->getSystemHealth(),
                'timestamp' => date('Y-m-d H:i:s')
            ];

            return $this->response->setJSON($data);

        } catch (\Exception $e) {
            log_message('error', 'Real-time data error: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON(['error' => 'Server error']);
        }
    }

    /**
     * Export dashboard data
     */
    public function export(): ResponseInterface
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        try {
            $format = $this->request->getGet('format') ?? 'csv';
            
            $stats = $this->getDashboardStats();
            $activities = $this->auditModel->getRecentActivities(100);

            switch ($format) {
                case 'csv':
                    return $this->exportCSV($stats, $activities);
                case 'json':
                    return $this->exportJSON($stats, $activities);
                default:
                    throw new \InvalidArgumentException('Unsupported export format');
            }

        } catch (\Exception $e) {
            log_message('error', 'Export error: ' . $e->getMessage());
            return redirect()->to('/admin/dashboard')->with('error', 'Export failed');
        }
    }

    /**
     * System maintenance page
     */
    public function maintenance(): string
    {
        if (!$this->isAdminAuthenticated()) {
            return redirect()->to('/admin/login');
        }

        $data = [
            'title' => 'System Maintenance - Kobo Admin',
            'maintenance_tasks' => $this->getMaintenanceTasks()
        ];

        return view('admin/dashboard/maintenance', $data);
    }

    /**
     * Check if admin is authenticated
     */
    private function isAdminAuthenticated(): bool
    {
        // For now, basic session check - will be enhanced with JWT in Phase 4
        $session = session();
        $userId = $session->get('admin_user_id');
        
        if (!$userId) {
            return false;
        }

        $user = $this->userModel->find($userId);
        return $user && $user['status'] === 'active' && $user['role'] === 'admin';
    }

    /**
     * Calculate system uptime (placeholder)
     */
    private function calculateUptime(): string
    {
        // Simple uptime calculation - can be enhanced
        $startTime = strtotime('2025-08-30 00:00:00'); // System start date
        $uptime = time() - $startTime;
        
        $days = floor($uptime / (24 * 3600));
        $hours = floor(($uptime % (24 * 3600)) / 3600);
        
        return "{$days} days, {$hours} hours";
    }

    /**
     * Format file size
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Get default statistics if database fails
     */
    private function getDefaultStats(): array
    {
        return [
            'users' => ['total' => 0, 'active' => 0, 'inactive' => 0, 'by_role' => []],
            'forms' => ['total' => 0, 'active' => 0, 'draft' => 0, 'inactive' => 0],
            'submissions' => ['total' => 0, 'pending' => 0, 'processed' => 0, 'failed' => 0],
            'media' => ['total_files' => 0, 'total_size' => 0, 'average_size' => 0],
            'activity' => ['total_activities' => 0, 'unique_users' => 0, 'top_users' => []]
        ];
    }

    /**
     * Export data as CSV
     */
    private function exportCSV(array $stats, array $activities): ResponseInterface
    {
        $csv = "Dashboard Export - " . date('Y-m-d H:i:s') . "\n\n";
        $csv .= "System Statistics:\n";
        $csv .= "Total Users,Active Users,Total Forms,Active Forms,Total Submissions,Processed Submissions\n";
        $csv .= "{$stats['users']['total']},{$stats['users']['active']},{$stats['forms']['total']},{$stats['forms']['active']},{$stats['submissions']['total']},{$stats['submissions']['processed']}\n\n";
        
        $csv .= "Recent Activities:\n";
        $csv .= "User,Action,Entity,Time\n";
        
        foreach ($activities as $activity) {
            $csv .= "\"{$activity['user']}\",\"{$activity['action']}\",\"{$activity['entity_type']}\",\"{$activity['time']}\"\n";
        }

        return $this->response->download('dashboard_export_' . date('Y-m-d') . '.csv', $csv)
                            ->setContentType('text/csv');
    }

    /**
     * Export data as JSON
     */
    private function exportJSON(array $stats, array $activities): ResponseInterface
    {
        $data = [
            'export_date' => date('Y-m-d H:i:s'),
            'statistics' => $stats,
            'recent_activities' => $activities
        ];

        return $this->response->download('dashboard_export_' . date('Y-m-d') . '.json', json_encode($data, JSON_PRETTY_PRINT))
                            ->setContentType('application/json');
    }

    /**
     * Get maintenance tasks
     */
    private function getMaintenanceTasks(): array
    {
        return [
            [
                'name' => 'Clean up old audit logs',
                'description' => 'Remove audit logs older than 1 year',
                'action' => '/admin/maintenance/cleanup-logs',
                'severity' => 'low'
            ],
            [
                'name' => 'Process pending submissions',
                'description' => 'Batch process all pending submissions',
                'action' => '/admin/maintenance/process-submissions',
                'severity' => 'medium'
            ],
            [
                'name' => 'Optimize database',
                'description' => 'Run database optimization and indexing',
                'action' => '/admin/maintenance/optimize-db',
                'severity' => 'low'
            ]
        ];
    }
}