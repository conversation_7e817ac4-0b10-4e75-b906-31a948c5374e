<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateKoboFormsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'form_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'form_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'version' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
            ],
            'xml_content' => [
                'type' => 'LONGTEXT',
            ],
            'manifest_content' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['active', 'inactive', 'draft'],
                'default' => 'active',
            ],
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
        ]);
        
        $this->forge->addPrimaryKey('id');
        $this->forge->addUniqueKey('form_id');
        $this->forge->addKey('created_by');
        $this->forge->addForeignKey('created_by', 'kobo_users', 'id', 'SET NULL', 'CASCADE');
        $this->forge->createTable('kobo_forms');
    }

    public function down()
    {
        $this->forge->dropTable('kobo_forms');
    }
}
