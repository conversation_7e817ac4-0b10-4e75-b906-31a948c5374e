<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Libraries\SecurityValidator;

/**
 * Security Filter
 * 
 * Comprehensive security middleware for request validation and protection
 */
class SecurityFilter implements FilterInterface
{
    protected $securityValidator;
    protected $protections = [
        'xss' => true,
        'csrf' => true,
        'sql_injection' => true,
        'file_validation' => true,
        'input_sanitization' => true,
        'header_security' => true
    ];

    public function __construct()
    {
        $this->securityValidator = new SecurityValidator();
    }

    /**
     * Before method - validate and sanitize request
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Apply security protections based on configuration
        $config = $this->getSecurityConfig($arguments);
        
        try {
            // Validate CSRF token for POST/PUT/DELETE requests
            if ($config['csrf'] && in_array($request->getMethod(), ['POST', 'PUT', 'PATCH', 'DELETE'])) {
                if (!$this->validateCSRF($request)) {
                    return $this->respondSecurityError('CSRF token validation failed', 403);
                }
            }
            
            // Sanitize and validate input data
            if ($config['input_sanitization']) {
                $this->sanitizeRequestData($request);
            }
            
            // Validate file uploads
            if ($config['file_validation'] && !empty($_FILES)) {
                $fileValidation = $this->validateFileUploads();
                if (!$fileValidation['valid']) {
                    return $this->respondSecurityError('File validation failed: ' . implode(', ', $fileValidation['errors']), 400);
                }
            }
            
            // Check for suspicious request patterns
            if ($this->detectSuspiciousActivity($request)) {
                $this->logSecurityIncident($request, 'suspicious_activity');
                return $this->respondSecurityError('Request blocked for security reasons', 403);
            }
            
            // Validate request headers
            if ($config['header_security'] && !$this->validateRequestHeaders($request)) {
                return $this->respondSecurityError('Invalid request headers', 400);
            }

        } catch (\Exception $e) {
            log_message('error', 'Security Filter Error: ' . $e->getMessage());
            return $this->respondSecurityError('Security validation failed', 500);
        }
    }

    /**
     * After method - add security headers
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        $config = $this->getSecurityConfig($arguments);
        
        if ($config['header_security']) {
            $this->addSecurityHeaders($response);
        }
    }

    /**
     * Get security configuration
     */
    protected function getSecurityConfig(?array $arguments = null): array
    {
        if ($arguments) {
            return array_merge($this->protections, $arguments);
        }
        
        return $this->protections;
    }

    /**
     * Validate CSRF token
     */
    protected function validateCSRF(RequestInterface $request): bool
    {
        return $this->securityValidator->verifyCsrfToken();
    }

    /**
     * Sanitize request data
     */
    protected function sanitizeRequestData(RequestInterface $request): void
    {
        // Get all input data
        $postData = $request->getPost();
        $getData = $request->getGet();
        
        // Sanitization rules
        $rules = [
            'xml_content' => ['xss_protection' => true, 'sql_protection' => true],
            'form_data' => ['xss_protection' => true, 'path_protection' => true],
            'search' => ['xss_protection' => true, 'sql_protection' => true]
        ];
        
        // Sanitize POST data
        if (!empty($postData)) {
            $sanitizedPost = $this->securityValidator->validateInput($postData, $rules);
            // Note: In CI4, we can't directly modify $_POST, but we can validate and store clean data
            foreach ($sanitizedPost as $key => $value) {
                $request->setGlobal('post', array_merge($request->getPost(), [$key => $value]));
            }
        }
        
        // Sanitize GET data
        if (!empty($getData)) {
            $sanitizedGet = $this->securityValidator->validateInput($getData, $rules);
            // Similar approach for GET data
        }
    }

    /**
     * Validate file uploads
     */
    protected function validateFileUploads(): array
    {
        $errors = [];
        
        foreach ($_FILES as $fieldName => $fileData) {
            if ($fileData['error'] === UPLOAD_ERR_OK) {
                // Create UploadedFile object
                $file = new \CodeIgniter\HTTP\Files\UploadedFile(
                    $fileData['tmp_name'],
                    $fileData['name'],
                    $fileData['type'],
                    $fileData['size'],
                    $fileData['error']
                );
                
                $validation = $this->securityValidator->validateFileUpload($file);
                
                if (!$validation['valid']) {
                    $errors = array_merge($errors, $validation['errors']);
                }
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Detect suspicious request patterns
     */
    protected function detectSuspiciousActivity(RequestInterface $request): bool
    {
        $suspiciousPatterns = [
            // Common attack patterns
            '/\b(union|select|insert|update|delete|drop)\b.*\b(from|where|order|group)\b/i',
            '/<script[^>]*>.*?<\/script>/is',
            '/javascript:[^"\']*["\']*/i',
            '/(?:\.{2,}[\/\\\\]){2,}/',
            '/\b(exec|system|shell_exec|passthru|eval)\s*\(/i'
        ];
        
        // Check URL and query parameters
        $uri = (string) $request->getUri();
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $uri)) {
                return true;
            }
        }
        
        // Check POST data
        $postData = $request->getPost();
        if ($postData) {
            $serializedData = serialize($postData);
            foreach ($suspiciousPatterns as $pattern) {
                if (preg_match($pattern, $serializedData)) {
                    return true;
                }
            }
        }
        
        // Check headers for suspicious content
        $headers = $request->headers();
        foreach ($headers as $header) {
            $headerValue = $header->getValue();
            foreach ($suspiciousPatterns as $pattern) {
                if (preg_match($pattern, $headerValue)) {
                    return true;
                }
            }
        }
        
        // Check for unusually long URLs or parameters
        if (strlen($uri) > 2048) {
            return true;
        }
        
        return false;
    }

    /**
     * Validate request headers
     */
    protected function validateRequestHeaders(RequestInterface $request): bool
    {
        // Check Content-Type for POST requests
        if (in_array($request->getMethod(), ['POST', 'PUT', 'PATCH'])) {
            $contentType = $request->getHeaderLine('Content-Type');
            
            $allowedContentTypes = [
                'application/json',
                'application/x-www-form-urlencoded',
                'multipart/form-data',
                'text/xml',
                'application/xml'
            ];
            
            $isValid = false;
            foreach ($allowedContentTypes as $type) {
                if (strpos($contentType, $type) !== false) {
                    $isValid = true;
                    break;
                }
            }
            
            if (!$isValid && !empty($contentType)) {
                return false;
            }
        }
        
        // Check for suspicious User-Agent
        $userAgent = $request->getHeaderLine('User-Agent');
        if (empty($userAgent) || $this->isSuspiciousUserAgent($userAgent)) {
            return false;
        }
        
        return true;
    }

    /**
     * Check for suspicious User-Agent strings
     */
    protected function isSuspiciousUserAgent(string $userAgent): bool
    {
        $suspiciousAgents = [
            'sqlmap',
            'nikto',
            'nessus',
            'openvas',
            'nmap',
            'masscan',
            'zap'
        ];
        
        $userAgentLower = strtolower($userAgent);
        foreach ($suspiciousAgents as $agent) {
            if (strpos($userAgentLower, $agent) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Add security headers to response
     */
    protected function addSecurityHeaders(ResponseInterface $response): void
    {
        // Prevent XSS attacks
        $response->setHeader('X-XSS-Protection', '1; mode=block');
        
        // Prevent MIME type sniffing
        $response->setHeader('X-Content-Type-Options', 'nosniff');
        
        // Prevent clickjacking
        $response->setHeader('X-Frame-Options', 'DENY');
        
        // Force HTTPS (if enabled)
        if (env('FORCE_HTTPS', false)) {
            $response->setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }
        
        // Content Security Policy
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; " .
               "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; " .
               "font-src 'self' https://cdn.jsdelivr.net; " .
               "img-src 'self' data:; " .
               "connect-src 'self';";
        
        $response->setHeader('Content-Security-Policy', $csp);
        
        // Referrer Policy
        $response->setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        
        // Permissions Policy (formerly Feature Policy)
        $response->setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
    }

    /**
     * Log security incidents
     */
    protected function logSecurityIncident(RequestInterface $request, string $type): void
    {
        $incident = [
            'type' => $type,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $request->getIPAddress(),
            'user_agent' => $request->getUserAgent(),
            'method' => $request->getMethod(),
            'uri' => (string) $request->getUri(),
            'post_data' => $request->getPost(),
            'headers' => $request->headers()
        ];
        
        // Log to file
        log_message('critical', 'Security Incident: ' . json_encode($incident));
        
        // Optionally send alert notifications
        $this->sendSecurityAlert($incident);
    }

    /**
     * Send security alert notifications
     */
    protected function sendSecurityAlert(array $incident): void
    {
        // Implementation for sending security alerts
        // Could send email, SMS, or push notifications to administrators
        
        $alertEmail = env('SECURITY_ALERT_EMAIL');
        if ($alertEmail) {
            $email = service('email');
            $email->setTo($alertEmail);
            $email->setSubject('Security Alert - ' . $incident['type']);
            $email->setMessage('Security incident detected: ' . json_encode($incident, JSON_PRETTY_PRINT));
            $email->send();
        }
    }

    /**
     * Return security error response
     */
    protected function respondSecurityError(string $message, int $statusCode): ResponseInterface
    {
        $response = service('response');
        
        return $response->setStatusCode($statusCode)
                       ->setJSON([
                           'success' => false,
                           'message' => $message,
                           'error_code' => 'SECURITY_VIOLATION',
                           'timestamp' => date('Y-m-d H:i:s')
                       ]);
    }

    /**
     * Get client IP with proxy support
     */
    protected function getRealClientIP(RequestInterface $request): string
    {
        $ipHeaders = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($ipHeaders as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $request->getIPAddress();
    }
}